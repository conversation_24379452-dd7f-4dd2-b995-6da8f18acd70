import {
    Entity,
    MarqueePicker,
    MarqueePickerMouseControl,

    ObjectsKdTree3,
} from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";

import { OverlappingPicker } from "../../../libs/overlappingPick";
import mathTs from "../../../libs/mathTs";

enum SectionPlaneType {
    FRONT = "Front-section-box",
    BACK = "Back-section-box",
    LEFT = "Left-section-box",
    RIGHT = "Right-section-box",
    TOP = "Top-section-box",
    BOTTOM = "Bottom-section-box",
}
enum ArrowType {
    FRONT = "Front__Arrow-section-box",
    BACK = "Back__Arrow-section-box",
    LEFT = "Left__Arrow-section-box",
    RIGHT = "Right__Arrow-section-box",
    TOP = "Top__Arrow-section-box",
    BOTTOM = "Bottom__Arrow-section-box",
}
enum AxisType {
    FRONT = "Front__Axis-section-box",
    BACK = "Back__Axis-section-box",
    LEFT = "Left__Axis-section-box",
    RIGHT = "Right__Axis-section-box",
    TOP = "Top__Axis-section-box",
    BOTTOM = "Bottom__Axis-section-box",

}

const LIMIT_DRAG_MAX_OBJECTS = 10000; // Điều chỉnh theo hiệu suất
export class EventHandler {
    private _store = GlobalStore.getInstance().getAll();
    private _canvas: HTMLCanvasElement | null = null;

    selectedEntities: Map<string, Entity> = new Map();
    private _lastColorizes: Map<string, number[]> = new Map();
    private marqueePicker: MarqueePicker | undefined;
    private marqueeControl: MarqueePickerMouseControl | undefined;

    private _isDragging = false;
    private _mouseDownPos: { x: number; y: number } | null = null;
    private picker = new OverlappingPicker();
    // ...existing code...
    private _dragDirection: 'left-to-right' | 'right-to-left' | null = null;
    constructor() {
        this._canvas = this._store.canvas ?? null;
        if (!this._canvas) {
            console.error("Canvas not found in store.");
            return;
        }
        this.initialize();
        this.setupMarqueePicker();

    }

    private _processingSelection = false;
    private _pendingSelectionIds: string[] = [];
    private _selectionDebounceTimer: NodeJS.Timeout | null = null;

    //#region setupMarqueePicker
    setupMarqueePicker() {
        const view = this._store.viewer!;
        const objectsKdTree3 = new ObjectsKdTree3({ viewer: view });
        if (!this.marqueePicker) {
            this.marqueePicker = new MarqueePicker({
                viewer: view,
                objectsKdTree3,
            });


        }
        if (!this.marqueeControl) {
            this.marqueeControl = new MarqueePickerMouseControl({
                marqueePicker: this.marqueePicker,
            });
        }
        this.marqueeControl.setActive(true);

        this.marqueePicker.on("clear", () => {
            this.clearSelections();
        });

        this.marqueePicker.on("picked", (objectIds: (string | number)[]) => {
            // Debounce để tránh xử lý quá nhiều lần
            if (this._selectionDebounceTimer) {
                clearTimeout(this._selectionDebounceTimer);
            }

            this._selectionDebounceTimer = setTimeout(() => {
                this._handleMarqueeSelection(objectIds);
            }, 50); // 50ms debounce
        });


        // this.marqueePicker.on("picked", (objectIds: (string | number)[]) => {
        //     console.log("Picked objects:", objectIds);
        //     const ids = objectIds.map((id) => String(id));

        //     // Lọc để chỉ lấy entity gốc (không có UUID ở giữa)
        //     const filteredIds = ids.filter((id) => {
        //         const parts = id.split("__");

        //         // Format chuẩn: entityName__sceneId#sceneId (2 parts)
        //         if (parts.length === 2) {
        //             return true;
        //         }

        //         // Format với UUID: entityName__uuid__sceneId#sceneId (3 parts)
        //         // Chỉ lấy nếu không có entity gốc tương ứng
        //         if (parts.length === 3) {
        //             const entityName = parts[0];
        //             const sceneId = parts[2];
        //             const rootEntityId = `${entityName}__${sceneId}`;

        //             // Chỉ lấy nếu không tồn tại entity gốc
        //             return !ids.includes(rootEntityId);
        //         }

        //         return false;
        //     });

        //     // Xử lý với các entity đã lọc
        //     filteredIds.forEach((id) => {
        //         const entity = view.scene.objects[id];
        //         if (entity && !entity.xrayed) {
        //             // Lấy prefix để tìm tất cả entity liên quan
        //             const prefix = id.split("__")[0]; // Lấy phần tên trước dấu "__" đầu tiên
        //             const matchedKeys = view.scene.objectIds.filter((objId) => objId.startsWith(prefix + "__"));

        //             // Lưu màu cho tất cả entity trong cụm
        //             matchedKeys.forEach((key) => {
        //                 if (!this._lastColorizes.has(key)) {
        //                     const targetEntity = view.scene.objects[key];
        //                     if (targetEntity) {
        //                         this._lastColorizes.set(
        //                             key,
        //                             targetEntity.colorize ? [...targetEntity.colorize] : [1, 1, 1, 1]
        //                         );
        //                     }
        //                 }
        //             });

        //             // Thêm entity gốc vào selectedEntities (tránh duplicate)
        //             if (!this.selectedEntities.find((ent) => ent.id === id)) {
        //                 this.selectedEntities.push(entity);
        //             }
        //         }
        //     });

        //     // Collect tất cả matched keys để select
        //     const allMatchedKeys = new Set<string>();
        //     filteredIds.forEach((id) => {
        //         const entity = view.scene.objects[id];
        //         if (entity && !entity.xrayed) {
        //             const prefix = id.split("__")[0];
        //             const matchedKeys = view.scene.objectIds.filter((objId) => objId.startsWith(prefix + "__"));
        //             matchedKeys.forEach(key => allMatchedKeys.add(key));
        //         }
        //     });

        //     // Select tất cả matched entities
        //     const selectedIds = Array.from(allMatchedKeys).filter((id) => {
        //         const entity = view.scene.objects[id];
        //         return entity && !entity.xrayed;
        //     });

        //     view.scene.setObjectsSelected(selectedIds, true);
        // });

    }

    //#region initialize
    initialize() {
        // //Clear selected entities in global Viewer


        this._canvas?.addEventListener("mousedown", this._handleMouseDown);
        this._canvas?.addEventListener("mousemove", this._handleMouseMove);
        this._canvas?.addEventListener("mouseup", this._handleMouseUp);
        //For trigger select again
        if (this.marqueeControl) {
            this.marqueeControl?.setActive(true);
        }



    }
    //#region _handleMouseDown

    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
        this._dragDirection = null; // Reset drag direction
    };
    //#region _handleMouseMove

    private _handleMouseMove = (event: MouseEvent) => {
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) {
            this._isDragging = true;

            // Tính hướng drag chỉ theo trái/phải
            const deltaX = event.offsetX - this._mouseDownPos.x;
            this._dragDirection = deltaX > 0 ? 'left-to-right' : 'right-to-left';
        }
    };
    //#region _handleMouseUp
    private _handleMouseUp = (event: MouseEvent) => {
        if (event.button !== 0 || !this._mouseDownPos) return;
        // Tính final drag direction nếu đang drag
        if (this._isDragging && this._mouseDownPos) {
            const deltaX = event.offsetX - this._mouseDownPos.x;
            this._dragDirection = deltaX > 0 ? 'left-to-right' : 'right-to-left';
        }

        if (!this._isDragging) {
            this._handleClick(event);
        }
        this._mouseDownPos = null;
        this._isDragging = false;
    };

    private _handleMarqueeSelection = (objectIds: (string | number)[]) => {
        if (this._processingSelection) return;

        this._processingSelection = true;
        console.time("Selecting entities");
        try {
            const ids = objectIds.map((id) => String(id));

            // Giới hạn số lượng đối tượng xử lý cùng lúc

            if (ids.length > LIMIT_DRAG_MAX_OBJECTS) {
                // console.warn(`Too many objects selected (${ids.length}). Processing first ${MAX_OBJECTS} objects.`);
                ids.splice(LIMIT_DRAG_MAX_OBJECTS);
            }

            if (this._dragDirection === 'left-to-right') {
                console.log("Left-to-right: ");
                this._handleDragToRightOptimized(ids);
            } else if (this._dragDirection === 'right-to-left') {
                console.log("Right-to-left");
                this._handleDragToLeftOptimized(ids);
            }
        } finally {
            this._processingSelection = false;
        }
        console.timeEnd("Selecting entities");
    };

    // Phiên bản tối ưu của _handleDragToRight
    private _handleDragToRightOptimized = (ids: string[]) => {
        const entityPatternGroups = new Map<string, Set<string>>();

        // Kiểm tra nếu cần batch (chỉ khi có quá nhiều objects)
        if (ids.length > 2000) {
            // Batch process chỉ khi thực sự cần
            const batchSize = 500; // Tăng batch size để giảm overhead
            let currentBatch = 0;

            const processBatch = () => {
                const start = currentBatch * batchSize;
                const end = Math.min(start + batchSize, ids.length);

                for (let i = start; i < end; i++) {
                    const id = ids[i];
                    const parts = id.split("__");
                    if (parts.length >= 2) {
                        const sceneId = parts[0];
                        const entityId = parts[1];
                        const entityPattern = `${sceneId}__${entityId}`;

                        if (!entityPatternGroups.has(entityPattern)) {
                            entityPatternGroups.set(entityPattern, new Set());
                        }
                        entityPatternGroups.get(entityPattern)!.add(id);
                    }
                }

                currentBatch++;

                if (end < ids.length) {
                    requestAnimationFrame(processBatch);
                } else {
                    this._processGroupSelection(entityPatternGroups, true);
                }
            };

            processBatch();
        } else {
            // Xử lý trực tiếp nếu không quá nhiều
            for (let i = 0; i < ids.length; i++) {
                const id = ids[i];
                const parts = id.split("__");
                if (parts.length >= 2) {
                    const sceneId = parts[0];
                    const entityId = parts[1];
                    const entityPattern = `${sceneId}__${entityId}`;

                    if (!entityPatternGroups.has(entityPattern)) {
                        entityPatternGroups.set(entityPattern, new Set());
                    }
                    entityPatternGroups.get(entityPattern)!.add(id);
                }
            }

            this._processGroupSelection(entityPatternGroups, true);
        }
    };

    // Phiên bản tối ưu của _handleDragToLeft
    private _handleDragToLeftOptimized = (ids: string[]) => {
        // Collect entity patterns with Set for better performance
        const entityPatternsSet = new Set<string>();

        // Xử lý trực tiếp thay vì chia batch
        for (let i = 0; i < ids.length; i++) {
            const id = ids[i];
            const parts = id.split("__");
            if (parts.length >= 2) {
                const sceneId = parts[0];
                const entityId = parts[1];
                const entityPattern = `${sceneId}__${entityId}`;
                entityPatternsSet.add(entityPattern);
            }
        }

        // Convert to Map format for compatibility
        const entityPatternGroups = new Map<string, Set<string>>();
        entityPatternsSet.forEach(pattern => {
            entityPatternGroups.set(pattern, new Set([pattern]));
        });

        // Xử lý ngay lập tức
        this._processGroupSelection(entityPatternGroups, false);
    };

    // Helper method để xử lý selection cuối cùng
    private _processGroupSelection = (entityPatternGroups: Map<string, Set<string>>, isFullPickRequired: boolean) => {
        const view = this._store.viewer!;
        const allMatchedKeys = new Set<string>();

        // Nhóm patterns theo modelId để tối ưu lookup
        const modelGroups = new Map<string, Map<string, Set<string>>>();

        entityPatternGroups.forEach((pickedIds, pattern) => {
            // Extract modelId từ pattern: "modelId__entityId"
            const modelId = pattern.split("__")[0];

            if (!modelGroups.has(modelId)) {
                modelGroups.set(modelId, new Map());
            }

            modelGroups.get(modelId)!.set(pattern, pickedIds);
        });

        // Xử lý từng model riêng biệt
        modelGroups.forEach((patterns, modelId) => {
            const model = view.scene.models[modelId] as any;
            // Kiểm tra model có tồn tại không
            if (!model) return;

            // Lấy keys từ model.objects (key map) thay vì model.objects trực tiếp
            const modelObjectIds = Object.keys(model.objects || {});

            patterns.forEach((pickedIds, pattern) => {
                // Filter chỉ trong model này
                const allGroupKeys = modelObjectIds.filter((objId) => {
                    return objId.startsWith(pattern) &&
                        (objId === pattern || objId.startsWith(pattern + "__"));
                });

                if (allGroupKeys.length === 0) return;

                const shouldSelect = isFullPickRequired ?
                    allGroupKeys.every(key => pickedIds.has(key)) :
                    true;

                if (shouldSelect) {
                    // Tìm root entity một cách hiệu quả
                    let rootEntity: Entity | null = null;
                    let minParts = Infinity;

                    for (const key of allGroupKeys) {
                        const entity = view.scene.objects[key];
                        if (entity && !entity.xrayed) {
                            const parts = key.split("__").length;
                            if (parts < minParts) {
                                minParts = parts;
                                rootEntity = entity;
                            }
                        }
                    }

                    // Set trực tiếp vào Map thay vì push vào array
                    if (rootEntity && !this.selectedEntities.has(rootEntity.id as string)) {
                        this.selectedEntities.set(rootEntity.id as string, rootEntity);
                    }

                    // Batch add to sets
                    allGroupKeys.forEach(key => allMatchedKeys.add(key));

                    // Batch save colors
                    // this._batchSaveColors(allGroupKeys);
                }
            });
        });

        // Bỏ bước _batchAddEntities vì đã set trực tiếp ở trên


        // Batch select objects
        const selectedIds = Array.from(allMatchedKeys).filter((id) => {
            const entity = view.scene.objects[id];
            return entity && !entity.xrayed;
        });

        // Sử dụng requestAnimationFrame để không block UI
        requestAnimationFrame(() => {
            view.scene.setObjectsSelected(selectedIds, true);
        });
    };

    // Có thể xóa method _batchAddEntities vì không cần thiết nữa
    // private _batchAddEntities = (entities: Entity[]) => {
    //     entities.forEach((entity) => {
    //         // Sử dụng Map.has() thay vì Array.find() - O(1) vs O(n)
    //         if (!this.selectedEntities.has(entity.id as string)) {
    //             this.selectedEntities.set(entity.id as string, entity);
    //         }
    //     });
    // };


    private _batchAddEntities = (entities: Entity[]) => {
        entities.forEach((entity) => {
            // Sử dụng Map.has() thay vì Array.find() - O(1) vs O(n)
            if (!this.selectedEntities.has(entity.id as string)) {
                this.selectedEntities.set(entity.id as string, entity);
            }
        });
    };
    //#region _handleClick
    private _handleClick(event: MouseEvent) {
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        let selectedEntity: Entity | null = null;

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        while (true) {
            const hit = this.picker.pickEntityAABB(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            if (!hit || !hit.entity) break;
            if (this._store.contextMenu?.sectionBox) {
                // Kiểm tra nếu có sectionBox thì bỏ qua các entity của section box
                if (this._store.contextMenu?.sectionBox) {
                    const entityId = hit.entity.id;
                    const isSectionBoxEntity =
                        Object.values(SectionPlaneType).includes(entityId as SectionPlaneType) ||
                        Object.values(ArrowType).includes(entityId as ArrowType) ||
                        Object.values(AxisType).includes(entityId as AxisType);

                    if (isSectionBoxEntity) {
                        continue; // Bỏ qua entity này và tiếp tục pick entity phía sau
                    }
                }
            }


            if (!hit.entity.xrayed) {
                selectedEntity = hit.entity;
                break; // gặp thằng không xray đầu tiên thì break
            }
        }


        if (selectedEntity) {
            console.log("hit section plane", selectedEntity);
            const isSelected = this.selectedEntities.has(selectedEntity.id as string);;
            if (event.ctrlKey) {
                if (isSelected) {
                    this._deselectEntity(selectedEntity);
                } else {
                    this._addEntity(selectedEntity);
                }
            } else {
                this.clearSelections();
                this._addEntity(selectedEntity);
            }
        } else {
            if (!event.ctrlKey) {
                this.clearSelections();
            }
        }
    }


    //#region _deselectEntity
    private _deselectEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);

        // Extract EntityId from format "sceneId__entityId" or "sceneId__entityId__uuid"
        const parts = key.split("__");
        if (parts.length < 2) return; // Invalid format

        const sceneId = parts[0]; // "0c0_04#0c0_04"
        const entityId = parts[1]; // "2COT2qvizFJgJS7ay5BjN9"

        // Find all entities that start with "sceneId__entityId"
        const entityPattern = `${sceneId}__${entityId}`;
        const matchedKeys = viewer.scene.objectIds.filter((id) => id.startsWith(entityPattern));

        console.log(`Deselecting entities with pattern: ${entityPattern}`, matchedKeys);



        // Deselect toàn bộ matchedKeys
        viewer.scene.setObjectsSelected(matchedKeys, false);


        // Gỡ entity khỏi Map - O(1) operation
        this.selectedEntities.delete(entity.id as string);
    }

    //#region _addEntity
    private _addEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);

        // Extract EntityId from format "sceneId__entityId" or "sceneId__entityId__uuid"
        const parts = key.split("__");
        if (parts.length < 2) return; // Invalid format

        const sceneId = parts[0]; // "0c0_04#0c0_04"
        const entityId = parts[1]; // "2COT2qvizFJgJS7ay5BjN9"

        // Find all entities that start with "sceneId__entityId"
        const entityPattern = `${sceneId}__${entityId}`;
        const matchedKeys = viewer.scene.objectIds.filter((id) => id.startsWith(entityPattern));

        console.log(`Selecting entities with pattern: ${entityPattern}`, matchedKeys);

        viewer.scene.setObjectsSelected(matchedKeys, true);
        if (!this.selectedEntities.has(entity.id as string)) {
            this.selectedEntities.set(entity.id as string, entity);
        }
    }

    //#region _clearSelections
    clearSelections() {
        console.log("Clearing selections", this.selectedEntities);
        const viewer = this._store.viewer!;

        viewer.scene.setObjectsSelected(viewer.scene.objectIds, false);
        this.selectedEntities.clear();

    }




    //#region destroy
    public destroy() {
        const scene = this._store.viewer?.scene;
        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);


        this.clearSelections();
        //Clear selected entities in global Viewer
        scene?.setObjectsHighlighted(scene.selectedObjectIds, false);
        scene?.setObjectsSelected(scene.selectedObjectIds, false);
        scene?.setObjectsXRayed(scene.selectedObjectIds, false);


        this._isDragging = false;
        this._mouseDownPos = null;
        this.marqueeControl?.setActive(false);
        // Clear debounce timer
        if (this._selectionDebounceTimer) {
            clearTimeout(this._selectionDebounceTimer);
            this._selectionDebounceTimer = null;
        }
    }
}
