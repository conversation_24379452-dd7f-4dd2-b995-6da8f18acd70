declare module "web-ifc-cdn" {
    export class IfcAPI {
        constructor();
        SetWasmPath(path: string): void;
        Init(): Promise<void>;
        Init(config?: any): Promise<void>;
        OpenModel(data: Uint8Array, settings?: any): number;
        CloseModel(modelID: number): void;
        GetAllLines(modelID: number): any[];
        GetLine(modelID: number, expressID: number): any;
        WriteLine(modelID: number, lineObject: any): void;
        GetGeometry(modelID: number, geometryExpressID: number): any;
    }
    
    export const IFCAPI_VERSION: string;
}