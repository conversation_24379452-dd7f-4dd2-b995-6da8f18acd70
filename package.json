{"name": "dpu-xeokit-view", "version": "0.0.7", "description": "DPU Xeokit Viewer version Beta", "homepage": "https://dpunity.com", "license": "DPU", "type": "module", "author": {"name": "PHUC", "url": "https://dpunity.com"}, "keywords": ["DP-Unity", "Cesium"], "repository": {"type": "git", "url": "https://dpunity.com"}, "main": "./dist/index.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.js"}}, "scripts": {"clean": "shx rm -rf dist", "build": "npm run clean && webpack --config webpack.config.js", "build:watch": "npm run clean && webpack --config webpack.config.js --watch", "build:dev": "npm run clean && webpack --config webpack.dev.config.js", "build:dev:watch": "npm run clean && webpack --config webpack.dev.config.js --watch", "build:stats": "npm run clean && webpack --config webpack.config.js --json > webpack-stats.json"}, "devDependencies": {"assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chalk": "^5.4.1", "copy-webpack-plugin": "^12.0.2", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "progress-bar-webpack-plugin": "^2.1.0", "querystring-es3": "^0.2.1", "shx": "^0.3.4", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "url": "^0.11.4", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@creooxag/cxconverter": "^0.0.3-alpha", "@loaders.gl/core": "^4.3.4", "@loaders.gl/gltf": "^4.3.4", "@xeokit/xeokit-sdk": "^2.6.82", "konva": "^9.3.18", "split.js": "^1.6.5", "url-loader": "^4.1.1", "web-ifc": "^0.0.51", "worker-loader": "^3.0.8"}}