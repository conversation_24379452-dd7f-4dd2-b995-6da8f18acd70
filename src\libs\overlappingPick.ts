import { PickResult } from "@xeokit/xeokit-sdk";
import math from "./mathTs";

interface PickRay {
    origin: number[]; // Vec3
    direction: number[]; // Vec3
}

interface Scene {
    pick(ray: PickRay & { pickSurface?: boolean }): PickResult | null;
}

interface PickOptions {
    wrapAround?: boolean;
    pickCloser?: boolean;
}

export class OverlappingPicker {
    private rayOriginPrecision: number;
    private rayDirectionPrecision: number;
    private alreadyPicked: any[];
    private recentPickEntity: any | null;
    private referenceRay: PickRay | null;
    private sentinel: {};

    constructor() {
        this.rayOriginPrecision = 0.001;
        this.rayDirectionPrecision = 0.2 * math.DEGTORAD;
        this.alreadyPicked = [];
        this.recentPickEntity = null;
        this.referenceRay = null;
        this.sentinel = {};
    }

    reset(): void {
        this.alreadyPicked.forEach(i => i.pickable = true);
        this.alreadyPicked = [];
        this.recentPickEntity = null;
        this.referenceRay = null;
    }

    /** Pick theo AABB (bounding box entity) */
    pickEntityAABB(scene: Scene, pickRay: PickRay, options: PickOptions = {}): PickResult | null {
        return this._pick(scene, pickRay, false, options);
    }

    /** Pick theo surface (pick từng mặt tam giác) */
    pickSurface(scene: Scene, pickRay: PickRay, options: PickOptions = {}): PickResult | null {
        return this._pick(scene, pickRay, true, options);
    }

    /** Hàm private xử lý pick chung cho 2 loại */
    private _pick(scene: Scene, pickRay: PickRay, pickSurface: boolean, options: PickOptions): PickResult | null {
        const { wrapAround = false, pickCloser = false } = options;

        const rayChanged =
            this.referenceRay &&
            (math.distVec3(new Float32Array(pickRay.origin), new Float32Array(this.referenceRay.origin)) > this.rayOriginPrecision ||
                math.angleVec3(new Float32Array(pickRay.direction), new Float32Array(this.referenceRay.direction)) > this.rayDirectionPrecision);

        if (!this.referenceRay || rayChanged) {
            this.referenceRay = { origin: pickRay.origin, direction: pickRay.direction };
        }

        if (rayChanged) {
            this.alreadyPicked.forEach(i => i.pickable = true);
            this.alreadyPicked = [];
        }

        if (pickCloser) {
            for (let i = 0; i < 2; ++i) {
                if (this.alreadyPicked.length > 0)
                    this.alreadyPicked.pop().pickable = true;
            }
        }

        const innerPick = (resetAndRepeat: boolean): PickResult | null => {
            const pickResult = scene.pick({
                origin: this.referenceRay!.origin,
                direction: this.referenceRay!.direction,
                pickSurface: pickSurface,
            });
            // console.log("pickResult", pickResult);
            const pickEntity = pickResult && pickResult.entity;

            if (pickEntity) {
                if (rayChanged && pickEntity === this.recentPickEntity && !pickCloser) {
                    this.alreadyPicked.push(pickEntity);
                    pickEntity.pickable = false;
                    return innerPick(true);
                } else {
                    this.alreadyPicked.push(pickEntity);
                    pickEntity.pickable = false;
                    this.recentPickEntity = pickEntity;
                    return pickResult;
                }
            } else if (wrapAround && resetAndRepeat && this.alreadyPicked.length > 0) {
                this.alreadyPicked.forEach(i => i.pickable = true);
                this.alreadyPicked = [];
                return innerPick(false);
            } else {
                if (this.alreadyPicked.length > 0 && this.alreadyPicked[this.alreadyPicked.length - 1] !== this.sentinel) {
                    this.alreadyPicked.push(this.sentinel);
                }
                this.recentPickEntity = null;
                return null;
            }
        };

        return innerPick(true);
    }
}
