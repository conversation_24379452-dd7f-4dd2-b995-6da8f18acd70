Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FE8E
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210286019, 0007FFFFAAB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC00  000210068E24 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEE0  00021006A225 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFCF3C0000 ntdll.dll
7FFFCD510000 KERNEL32.DLL
7FFFCC930000 KERNELBASE.dll
7FFFCD710000 USER32.dll
000210040000 msys-2.0.dll
7FFFCC900000 win32u.dll
7FFFCD5E0000 GDI32.dll
7FFFCC7C0000 gdi32full.dll
7FFFCCD30000 msvcp_win.dll
7FFFCC4F0000 ucrtbase.dll
7FFFCEF30000 advapi32.dll
7FFFCEE80000 msvcrt.dll
7FFFCEB20000 sechost.dll
7FFFCD160000 RPCRT4.dll
7FFFCC150000 CRYPTBASE.DLL
7FFFCCF60000 bcryptPrimitives.dll
7FFFCDC90000 IMM32.DLL
