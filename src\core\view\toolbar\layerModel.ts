import GlobalStore from "../../../core/globalStore/globalStore";
import { TreeLayerModel } from "../dialog/layerModel/treeLayerModel";
import { Toolbar } from "./toolbar";

export class LayerModel {
    private _toolbar: Toolbar;
    private _store = GlobalStore.getInstance().getAll();
    private _treeLayerModel: TreeLayerModel | undefined;
    constructor(toolbar: Toolbar) {
        this._toolbar = toolbar;

        this._treeLayerModel = new TreeLayerModel();
    }




    //#region LayerModelAction
    private _activeBtnLayerModel = false
    layerModelAction = (idElement: string) => {
        this._activeBtnLayerModel = !this._activeBtnLayerModel
        if (!this._activeBtnLayerModel) {
            this._toolbar.activeBtn(idElement, false);
            this._treeLayerModel?.showHideModalTreeLayerModel(false);
        } else {
            this._toolbar.activeBtn(idElement, true);
            this._treeLayerModel?.showHideModalTreeLayerModel(true);
        }

        this._treeLayerModel?.setClosedCallback((status: boolean) => {
            if (!status) {
                this._toolbar.activeBtn(idElement, false)
                this._activeBtnLayerModel = false;
            }
        });

    }

}