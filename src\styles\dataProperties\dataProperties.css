.dpu-table-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: var(--background-color-4);
}
.dpu-table-container .no-data {
  width: 100%;
  height: 100%;
  color: var(--color-text-3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.info-table {
  width: 100%;
  border-collapse: collapse; /* Xóa kho<PERSON>ng cách giữa các ô */
  border: 1px solid var(--border-color-1);
  border-width: 1px; /* Top = 0, Right = 1px, Bottom = 1px, Left = 1px */
}

.info-table th {
  background-color: var(--background-color-2);
  text-align: left;
  padding: 10px;
  border: 1px solid var(--border-color-1); /* Đường viền giữa các tiêu đề */
}

.info-table td {
  border: 1px solid var(--border-color-1); /* Đường viền giữa các ô */
  padding: 2px;
  vertical-align: top;
}

.info-table tr:nth-child(even) {
  background-color: var(--background-color-2);
}

.info-table tr:nth-child(1) td {
  border-top: none;
}
.dpu-container-master-row .key,
.dpu-container-master-row .value,
.dpu-container-master-row .unit {
  font-size: 12px;
  text-align: left; /* Căn trái cho cột Key */
  position: absolute;
  left: 3px;
  top: 3px;
  right: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* .value {
  min-width: 50px;

} */
.dpu-container-node-master-row {
  width: 100%;
  margin-bottom: 5px;
}
.dpu-container-node-master-row .content-key {
  font-size: 14px;
}
.dpu-container-node-master-row .dpu-container-master-row {
      padding: 5px 5px 0px 5px;
}
.dpu-container-node-master-row.hidden .dpu-container-master-row {
  display: none;
}

.dpu-container-node-master-row.hidden .dpu-row-table .dpu-arrow {
  transform: rotate(0deg);
}

.dpu-container-master-row .content-td {
  position: relative;
}
.dpu-container-master-row .content-td::before {
  content: "&nbsp;";
  visibility: hidden;
}
.dpu-container-master-row .content-td.content-td-key {
  width: 30%;
}
.dpu-container-master-row .content-td.content-td-value {
  width: 70%;
}
.dpu-container-master-row .unit {
  min-width: 35px;
}
.dpu-container-master-row {
  width: 100%;
}
.dpu-container-master-row.open .dpu-row-table .dpu-arrow {
  transform: rotate(0deg);
}
.dpu-container-master-row.open .container-table {
  padding: 2px;
}
.container-table {
  width: 100%;
  padding: 10px;
  overflow: hidden;
}
.dpu-row-table {
  position: sticky;
  z-index: 10;
  /* top: 35px; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  cursor: pointer;
  background-color: var(--dropdown-background-color-1);
  /* background-image: linear-gradient(
    var(--dropdown-background-color-1),
    var(--dropdown-background-color-2)
  ); */
  /* border: 1px solid var(--border-color-1); */
  /* box-shadow: rgba(0, 0, 0, 0.01) 0px 2px 1px, rgba(0, 0, 0, 0.01) 0px 4px 2px,
    rgba(0, 0, 0, 0.01) 0px 8px 4px, rgba(0, 0, 0, 0.03) 0px 16px 8px,
    rgba(0, 0, 0, 0.03) 0px 32px 16px; */
  /* box-shadow: 0px 3px 15px -10px #494949; */
}
.dpu-row-table.node {
  top: 0;

}
.dpu-row-table .content-key {
  font-size: 12px;
  font-weight: bold;
  width: calc(100% - 20px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dpu-row-table .dpu-arrow {
  width: 10px;
  height: 10px;
  line-height: 10px;
  fill: var(--color-text-1);
  transform: rotate(-90deg);
  transition: 0.1s;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-container img {
  animation: rotate 1s linear infinite;
}
