import { parse } from '@loaders.gl/core';
import { GLTFLoader, postProcessGLTF } from '@loaders.gl/gltf';
import {
    sRGBEncoding, math, worldToRTCPositions, utils
} from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";
import { core } from "@xeokit/xeokit-sdk/src/viewer/scene/core.js";
import {
    ClampToEdgeWrapping,
    LinearFilter,
    LinearMipMapLinearFilter,
    LinearMipMapNearestFilter,
    MirroredRepeatWrapping,
    NearestFilter,
    NearestMipMapLinearFilter,
    NearestMipMapNearestFilter,
    RepeatWrapping
} from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";


interface GLTFSceneModelLoaderConfig {
    [key: string]: any;
}

interface LoadOptions {
    entityId?: string;
    autoMetaModel?: boolean;
    globalizeObjectIds?: boolean;
    loadBuffer?: any;
    basePath?: string;
    handlenode?: any;
    backfaces?: boolean;
    [key: string]: any;
}

interface ParseContext {
    src: string;
    entityId?: string;
    metaModelJSON: any;
    autoMetaModel?: boolean;
    globalizeObjectIds?: boolean;
    metaObjects: any[];
    loadBuffer?: any;
    basePath?: string;
    handlenode?: any;
    backfaces: boolean;
    gltfData: any;
    scene: any;
    plugin: any;
    sceneModel: any;
    numObjects: number;
    nodes: any[];
    nextId: number;
    log: (msg: string) => void;
}

interface MaterialAttributes {
    color: Float32Array;
    opacity: number;
    metallic: number;
    roughness: number;
    doubleSided: boolean;
}

interface TextureSetConfig {
    id?: string;
    normalTextureId?: string;
    occlusionTextureId?: string;
    emissiveTextureId?: string;
    colorTextureId?: string;
    metallicRoughnessTextureId?: string;
    alphaCutoff?: number;
}

interface MeshConfig {
    id: string;
    textureSetId?: string;
    color?: Float32Array;
    opacity?: number;
    metallic?: number;
    roughness?: number;
    primitive?: string;
    localPositions?: any;
    positions?: Float64Array;
    normals?: any;
    uv?: any;
    indices?: any;
    origin?: any;
}

type CallbackFunction = () => void;
type ErrorCallback = (error: string) => void;

/**
 * @private
 */
class GLTFSceneModelLoader {

    constructor(cfg: GLTFSceneModelLoaderConfig = {}) {
        cfg = cfg || {};
    }

    load(plugin: any, src: string, metaModelJSON: any, options: LoadOptions, sceneModel: any, ok?: CallbackFunction, error?: ErrorCallback): void {
        options = options || {};
        // const newMetaModelJSON = metaModelJSON.map((obj: any) => {
        //     return {
        //         id: sceneModel.id + '__' + obj.objectId,
        //         parent: sceneModel.id + '__' + obj.parent,
        //         name: obj.name,
        //         ...obj
        //     }
        // })
        // newMetaModelJSON.unshift({
        //     id: sceneModel.id,
        //     type: "Default",
        //     name: sceneModel.id,
        //     parent: null
        // })


        console.log("Vô load metaModelJSON", metaModelJSON);

        loadGLTF(plugin, src, metaModelJSON, options, sceneModel, function () {
            core?.scheduleTask(function () {
                sceneModel.scene.fire("modelLoaded", sceneModel.id); // FIXME: Assumes listeners know order of these two events
                sceneModel.fire("loaded", true, false);
            });
            if (ok) {
                ok();
            }
        },
            function (msg: string) {
                plugin.error(msg);
                if (error) {
                    error(msg);
                }
                sceneModel.fire("error", msg);
            });
    }

    parse(plugin: any, gltf: any, metaModelJSON: any, options: LoadOptions, sceneModel: any, ok?: CallbackFunction, error?: ErrorCallback): void {
        options = options || {};
        parseGLTF(plugin, "", gltf, metaModelJSON, options, sceneModel, function () {
            sceneModel.scene.fire("modelLoaded", sceneModel.id); // FIXME: Assumes listeners know order of these two events
            sceneModel.fire("loaded", true, false);
            if (ok) {
                ok();
            }
        },
            function (msg: string) {
                sceneModel.error(msg);
                sceneModel.fire("error", msg);
                if (error) {
                    error(msg);
                }
            });
    }
}

function loadGLTF(plugin: any, src: string, metaModelJSON: any, options: LoadOptions, sceneModel: any, ok: CallbackFunction, error: ErrorCallback): void {
    console.log("Vô loadGLTF", metaModelJSON);
    const spinner = plugin.viewer.scene.canvas.spinner;
    spinner.processes++;
    const isGLB = (src.split('.').pop() === "glb");
    if (isGLB) {
        plugin.dataSource.getGLB(src, (arrayBuffer: ArrayBuffer) => { // OK
            options.basePath = getBasePath(src);
            parseGLTF(plugin, src, arrayBuffer, metaModelJSON, options, sceneModel, ok, error);
            spinner.processes--;
        },
            (err: string) => {
                spinner.processes--;
                error(err);
            });
    } else {
        plugin.dataSource.getGLTF(src, (gltf: ArrayBuffer) => { // OK
            options.basePath = getBasePath(src);
            parseGLTF(plugin, src, gltf, metaModelJSON, options, sceneModel, ok, error);
            spinner.processes--;
        },
            (err: string) => {
                spinner.processes--;
                error(err);
            });
    }
}

function getBasePath(src: string): string {
    const i = src.lastIndexOf("/");
    return (i !== 0) ? src.substring(0, i + 1) : "";
}

function parseGLTF(plugin: any, src: string, gltf: any, metaModelJSON: any, options: LoadOptions, sceneModel: any, ok: CallbackFunction, error: ErrorCallback): void {
    console.log("Vô parseGLTF", metaModelJSON);
    const spinner = plugin.viewer.scene.canvas.spinner;
    spinner.processes++;
    parse(gltf, GLTFLoader, {
        baseUri: options.basePath
    }).then((gltfData: any) => {
        const processedGLTF = postProcessGLTF(gltfData);
        console.log("processedGLTF", processedGLTF);
        const ctx: ParseContext = {
            src: src,
            entityId: options.entityId,
            metaModelJSON,
            autoMetaModel: options.autoMetaModel,
            globalizeObjectIds: options.globalizeObjectIds,
            metaObjects: [],
            loadBuffer: options.loadBuffer,
            basePath: options.basePath,
            handlenode: options.handlenode,
            backfaces: !!options.backfaces,
            gltfData: processedGLTF,
            scene: sceneModel.scene,
            plugin: plugin,
            sceneModel: sceneModel,
            numObjects: 0,
            nodes: [],
            nextId: 0,
            log: (msg: string) => {
                plugin.log(msg);
            }
        };
        loadTextures(ctx);
        loadMaterials(ctx);

        if (options.autoMetaModel) {
            // new id 

            ctx.metaObjects.push({
                id: sceneModel.id,
                type: "Default",
                name: sceneModel.id
            });
        }

        //Load default scene before finalizing
        loadDefaultScene(ctx);

        sceneModel.finalize();
        //Để lại xử lý sau này
        if (options.autoMetaModel && ctx.metaModelJSON) {
            let newMetaModelJSON: any
            if (Array.isArray(ctx.metaModelJSON)) {

                let data: any = []
                data = ctx.metaModelJSON.map((obj: any) => {
                    return {
                        id: sceneModel.id + '__' + obj.objectId,
                        parent: sceneModel.id,
                        name: obj.name,
                        type: "Default",
                    }
                })
                data.unshift({
                    id: sceneModel.id,
                    type: "Default",
                    name: sceneModel.id,
                    parent: null
                })
                // // Là array
                // newMetaModelJSON = {
                //     id: "1064",
                //     projectId: sceneModel.id,
                //     author: "xeokit",
                //     createdAt: new Date().toISOString(),
                //     schema: "IFC2X3",
                //     creatingApplication: "xeokit",
                //     metaObjects: data
                // }
                ctx.metaObjects = data
            }

            plugin.viewer.metaScene.createMetaModel(sceneModel.id, {
                metaObjects: ctx.metaObjects
            });

        }


        else if (options.autoMetaModel && !ctx.metaModelJSON) {
            plugin.viewer.metaScene.createMetaModel(sceneModel.id, {
                metaObjects: ctx.metaObjects
            });
        }
        plugin.viewer.metaScene.createMetaModel(sceneModel.id, {
            metaObjects: ctx.metaObjects
        });

        console.log("ctx", ctx);
        spinner.processes--;
        ok();
    }).catch((err: any) => {
        if (error) error(err);
    });
}

function loadTextures(ctx: ParseContext): void {
    const gltfData = ctx.gltfData;
    const textures = gltfData.textures;
    if (textures) {
        for (let i = 0, len = textures.length; i < len; i++) {
            loadTexture(ctx, textures[i]);
        }
    }
}

function loadTexture(ctx: ParseContext, texture: any): void {
    if (!texture.source || !texture.source.image) {
        return;
    }
    const textureId = `texture-${ctx.nextId++}`;

    let minFilter = NearestMipMapLinearFilter;
    switch (texture.sampler.minFilter) {
        case 9728:
            minFilter = NearestFilter;
            break;
        case 9729:
            minFilter = LinearFilter;
            break;
        case 9984:
            minFilter = NearestMipMapNearestFilter;
            break;
        case 9985:
            minFilter = LinearMipMapNearestFilter;
            break;
        case 9986:
            minFilter = NearestMipMapLinearFilter;
            break;
        case 9987:
            minFilter = LinearMipMapLinearFilter;
            break;
    }

    let magFilter = LinearFilter;
    switch (texture.sampler.magFilter) {
        case 9728:
            magFilter = NearestFilter;
            break;
        case 9729:
            magFilter = LinearFilter;
            break;
    }

    let wrapS = RepeatWrapping;
    switch (texture.sampler.wrapS) {
        case 33071:
            wrapS = ClampToEdgeWrapping;
            break;
        case 33648:
            wrapS = MirroredRepeatWrapping;
            break;
        case 10497:
            wrapS = RepeatWrapping;
            break;
    }

    let wrapT = RepeatWrapping;
    switch (texture.sampler.wrapT) {
        case 33071:
            wrapT = ClampToEdgeWrapping;
            break;
        case 33648:
            wrapT = MirroredRepeatWrapping;
            break;
        case 10497:
            wrapT = RepeatWrapping;
            break;
    }

    let wrapR = RepeatWrapping;
    switch (texture.sampler.wrapR) {
        case 33071:
            wrapR = ClampToEdgeWrapping;
            break;
        case 33648:
            wrapR = MirroredRepeatWrapping;
            break;
        case 10497:
            wrapR = RepeatWrapping;
            break;
    }
    ctx.sceneModel.createTexture({
        id: textureId,
        image: texture.source.image,
        flipY: !!texture.flipY,
        minFilter,
        magFilter,
        wrapS,
        wrapT,
        wrapR,
        encoding: sRGBEncoding
    });
    texture._textureId = textureId;
}

function loadMaterials(ctx: ParseContext): void {
    const gltfData = ctx.gltfData;
    const materials = gltfData.materials;
    if (!ctx.sceneModel._materialConfigs) ctx.sceneModel._materialConfigs = [];
    if (materials) {
        for (let i = 0, len = materials.length; i < len; i++) {
            const material = materials[i];
            material._textureSetId = loadTextureSet(ctx, material);
            material._attributes = loadMaterialAttributes(ctx, material);
            // Save a shallow copy of material config for later
            ctx.sceneModel._materialConfigs.push({
                ...material,
                _textureSetId: material._textureSetId,
                _attributes: material._attributes
            });
        }
    }
}

function loadTextureSet(ctx: ParseContext, material: any): string | null {
    const textureSetCfg: TextureSetConfig = {};
    if (material.normalTexture) {
        textureSetCfg.normalTextureId = material.normalTexture.texture._textureId;
    }
    if (material.occlusionTexture) {
        textureSetCfg.occlusionTextureId = material.occlusionTexture.texture._textureId;
    }
    if (material.emissiveTexture) {
        textureSetCfg.emissiveTextureId = material.emissiveTexture.texture._textureId;
    }

    switch (material.alphaMode) {
        case "OPAQUE":
            break;
        case "MASK":
            const alphaCutoff = material.alphaCutoff;
            // Default from the spec https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html#reference-material
            textureSetCfg.alphaCutoff = (alphaCutoff !== undefined) ? alphaCutoff : 0.5;
            break;
        case "BLEND":
            break;
        default:
            break;
    }

    const metallicPBR = material.pbrMetallicRoughness;
    if (material.pbrMetallicRoughness) {
        const pbrMetallicRoughness = material.pbrMetallicRoughness;
        const baseColorTexture = pbrMetallicRoughness.baseColorTexture || pbrMetallicRoughness.colorTexture;
        if (baseColorTexture) {
            if (baseColorTexture.texture) {
                textureSetCfg.colorTextureId = baseColorTexture.texture._textureId;
            } else {
                textureSetCfg.colorTextureId = ctx.gltfData.textures[baseColorTexture.index]._textureId;
            }
        }
        if (metallicPBR.metallicRoughnessTexture) {
            textureSetCfg.metallicRoughnessTextureId = metallicPBR.metallicRoughnessTexture.texture._textureId;
        }
    }
    const extensions = material.extensions;
    if (extensions) {
        const specularPBR = extensions["KHR_materials_pbrSpecularGlossiness"];
        if (specularPBR) {
            const specularTexture = specularPBR.specularTexture;
            if (specularTexture !== null && specularTexture !== undefined) {
                //  textureSetCfg.colorTextureId = ctx.gltfData.textures[specularColorTexture.index]._textureId;
            }
            const specularColorTexture = specularPBR.specularColorTexture;
            if (specularColorTexture !== null && specularColorTexture !== undefined) {
                textureSetCfg.colorTextureId = ctx.gltfData.textures[specularColorTexture.index]._textureId;
            }
        }
    }
    if (textureSetCfg.normalTextureId !== undefined ||
        textureSetCfg.occlusionTextureId !== undefined ||
        textureSetCfg.emissiveTextureId !== undefined ||
        textureSetCfg.colorTextureId !== undefined ||
        textureSetCfg.metallicRoughnessTextureId !== undefined) {
        textureSetCfg.id = `textureSet-${ctx.nextId++};`
        ctx.sceneModel.createTextureSet(textureSetCfg);
        return textureSetCfg.id;
    }
    return null;
}

function loadMaterialAttributes(ctx: ParseContext, material: any): MaterialAttributes {
    const extensions = material.extensions;
    const materialAttributes: MaterialAttributes = {
        color: new Float32Array([1, 1, 1, 1]),
        opacity: 1,
        metallic: 0,
        roughness: 1,
        doubleSided: true
    };
    if (extensions) {
        const specularPBR = extensions["KHR_materials_pbrSpecularGlossiness"];
        if (specularPBR) {
            const diffuseFactor = specularPBR.diffuseFactor;
            if (diffuseFactor !== null && diffuseFactor !== undefined) {
                materialAttributes.color.set(diffuseFactor);
            }
        }
        const common = extensions["KHR_materials_common"];
        if (common) {
            const technique = common.technique;
            const values = common.values || {};
            const blinn = technique === "BLINN";
            const phong = technique === "PHONG";
            const lambert = technique === "LAMBERT";
            const diffuse = values.diffuse;
            if (diffuse && (blinn || phong || lambert)) {
                if (!utils.isString(diffuse)) {
                    materialAttributes.color.set(diffuse);
                }
            }
            const transparency = values.transparency;
            if (transparency !== null && transparency !== undefined) {
                materialAttributes.opacity = transparency;
            }
            const transparent = values.transparent;
            if (transparent !== null && transparent !== undefined) {
                materialAttributes.opacity = transparent;
            }
        }
    }
    const metallicPBR = material.pbrMetallicRoughness;
    if (metallicPBR) {
        const baseColorFactor = metallicPBR.baseColorFactor;
        if (baseColorFactor) {
            materialAttributes.color[0] = baseColorFactor[0];
            materialAttributes.color[1] = baseColorFactor[1];
            materialAttributes.color[2] = baseColorFactor[2];
            materialAttributes.opacity = baseColorFactor[3];
        }
        const metallicFactor = metallicPBR.metallicFactor;
        if (metallicFactor !== null && metallicFactor !== undefined) {
            materialAttributes.metallic = metallicFactor;
        }
        const roughnessFactor = metallicPBR.roughnessFactor;
        if (roughnessFactor !== null && roughnessFactor !== undefined) {
            materialAttributes.roughness = roughnessFactor;
        }
    }
    materialAttributes.doubleSided = (material.doubleSided !== false);
    return materialAttributes;
}

function loadDefaultScene(ctx: ParseContext): void {
    const gltfData = ctx.gltfData;
    const scene = gltfData.scene || gltfData.scenes[0];
    if (!scene) {
        error(ctx, "glTF has no default scene");
        return;
    }

    const nodes = scene.nodes;
    if (!nodes) {
        return;
    }

    // Tăng tốc: gom meshIds bằng Map/Set, chỉ duyệt 1 lần, không xóa entity phụ
    (function accumulateMeshInstantes(nodes: any[]): void {
        nodes.forEach(node => {
            const mesh = node.mesh;
            if (mesh) {
                mesh.instances ||= 0;
                mesh.instances += 1;
            }
            if (node.children) {
                accumulateMeshInstantes(node.children);
            }
        });
    })(nodes);

    (function createSceneMeshesAndEntities(nodes: any[], parentMatrix: any): void {
        console.log('nodes',nodes);
        // Map gom meshIds cho mỗi entityId
        const entityMeshMap = new Map<string, Set<string>>();

        function traverse(nodes: any[], parentMatrix: any) {
            nodes.forEach(node => {
                const nodeName = node.name || 'node-undefined';
                const baseEntityId = `${ctx.sceneModel.id}__${nodeName}`;
                const matrix = parseNodeMatrix(node, parentMatrix);

                let meshIds: string[] = [];
                if (node.mesh) {
                    parseNodeMesh(node, ctx, matrix, meshIds);
                }

                if (!entityMeshMap.has(baseEntityId)) {
                    entityMeshMap.set(baseEntityId, new Set());
                }
                meshIds.forEach(id => entityMeshMap.get(baseEntityId)!.add(id));

                if (node.children) {
                    traverse(node.children, matrix);
                }
            });
        }

        traverse(nodes, parentMatrix);

        // Tạo entity duy nhất cho mỗi entityId
        if (!ctx.sceneModel._entityConfigs) ctx.sceneModel._entityConfigs = [];
        entityMeshMap.forEach((meshIdSet, baseEntityId) => {
            const meshIds = Array.from(meshIdSet);
            if (meshIds.length > 0) {
                const globalId = ctx.globalizeObjectIds ? math.globalizeObjectId(ctx.sceneModel.id, baseEntityId) : baseEntityId;
                const entityConfig = {
                    id: globalId,
                    meshIds: meshIds,
                    isObject: true
                };
                ctx.sceneModel.createEntity(entityConfig);
                ctx.sceneModel._entityConfigs.push({ ...entityConfig });
                if (ctx.autoMetaModel) {
                    ctx.metaObjects.push({
                        id: globalId,
                        type: "Default",
                        name: globalId,
                        parent: ctx.sceneModel.id
                    });
                }
            }
        });
    })(nodes, null);

}

/**
 * Parses transform at the given glTF node.
 *
 * @param node the glTF node
 * @param matrix Transform matrix from parent nodes
 * @returns Transform matrix for the node
 */
function parseNodeMatrix(node: any, matrix: any): any {
    let localMatrix: any;
    if (node.matrix) {
        localMatrix = node.matrix;
        if (matrix) {
            matrix = math.mulMat4(matrix, localMatrix, math.mat4());
        } else {
            matrix = localMatrix;
        }
    }
    if (node.translation) {
        localMatrix = math.translationMat4v(node.translation);
        if (matrix) {
            matrix = math.mulMat4(matrix, localMatrix, math.mat4());
        } else {
            matrix = localMatrix;
        }
    }
    if (node.rotation) {
        localMatrix = math.quaternionToMat4(node.rotation);
        if (matrix) {
            matrix = math.mulMat4(matrix, localMatrix, math.mat4());
        } else {
            matrix = localMatrix;
        }
    }
    if (node.scale) {
        localMatrix = math.scalingMat4v(node.scale);
        if (matrix) {
            matrix = math.mulMat4(matrix, localMatrix, math.mat4());
        } else {
            matrix = localMatrix;
        }
    }
    return matrix;
}

/**
 * Parses primitives referenced by the mesh belonging to the given node, creating XKTMeshes in the XKTModel.
 *
 * @param node glTF node
 * @param ctx Parsing context
 * @param matrix Matrix for the XKTMeshes
 * @param meshIds returns IDs of the new XKTMeshes
 */
function parseNodeMesh(node: any, ctx: ParseContext, matrix: any, meshIds: string[]): void {
    const mesh = node.mesh;
    if (!mesh) {
        return;
    }
    const numPrimitives = mesh.primitives.length;
    if (!ctx.sceneModel._meshConfigs) ctx.sceneModel._meshConfigs = [];
    if (numPrimitives > 0) {
        for (let i = 0; i < numPrimitives; i++) {
            const primitive = mesh.primitives[i];
            if (primitive.mode < 4) {
                continue;
            }
            const meshCfg: MeshConfig = {
                id: ctx.sceneModel.id + "." + ctx.numObjects++
            };
            const material = primitive.material;
            if (material) {
                meshCfg.textureSetId = material._textureSetId;
                meshCfg.color = material._attributes.color;
                meshCfg.opacity = material._attributes.opacity;
                meshCfg.metallic = material._attributes.metallic;
                meshCfg.roughness = material._attributes.roughness;
            } else {
                meshCfg.color = new Float32Array([1.0, 1.0, 1.0]);
                meshCfg.opacity = 1.0;
            }
            const backfaces = ((ctx.backfaces !== false) || (material && material.doubleSided !== false));
            switch (primitive.mode) {
                case 0: // POINTS
                    meshCfg.primitive = "points";
                    break;
                case 1: // LINES
                    meshCfg.primitive = "lines";
                    break;
                case 2: // LINE_LOOP
                    meshCfg.primitive = "lines";
                    break;
                case 3: // LINE_STRIP
                    meshCfg.primitive = "lines";
                    break;
                case 4: // TRIANGLES
                    meshCfg.primitive = backfaces ? "triangles" : "solid";
                    break;
                case 5: // TRIANGLE_STRIP
                    meshCfg.primitive = backfaces ? "triangles" : "solid";
                    break;
                case 6: // TRIANGLE_FAN
                    meshCfg.primitive = backfaces ? "triangles" : "solid";
                    break;
                default:
                    meshCfg.primitive = backfaces ? "triangles" : "solid";
            }
            const POSITION = primitive.attributes.POSITION;
            if (!POSITION) {
                continue;
            }
            meshCfg.localPositions = POSITION.value;
            meshCfg.positions = new Float64Array(meshCfg.localPositions.length);
            if (primitive.attributes.NORMAL) {
                meshCfg.normals = primitive.attributes.NORMAL.value;
            }
            if (primitive.attributes.TEXCOORD_0) {
                meshCfg.uv = primitive.attributes.TEXCOORD_0.value;
            }
            if (primitive.indices) {
                meshCfg.indices = primitive.indices.value;
            }
            if (matrix) {
                math.transformPositions3(matrix, meshCfg.localPositions, meshCfg.positions);
            } else { // eqiv to math.transformPositions3(math.identityMat4(), meshCfg.localPositions, meshCfg.positions);
                meshCfg.positions.set(meshCfg.localPositions);
            }
            const origin = math.vec3();
            const rtcNeeded = worldToRTCPositions(meshCfg.positions, meshCfg.positions, origin); // Small cellsize guarantees better accuracy
            if (rtcNeeded) {
                meshCfg.origin = origin;
            }
            ctx.sceneModel.createMesh(meshCfg);
            ctx.sceneModel._meshConfigs.push({ ...meshCfg });
            meshIds.push(meshCfg.id);
        }
    }
}

function error(ctx: ParseContext, msg: string): void {
    ctx.plugin.error(msg);
}

export { GLTFSceneModelLoader };