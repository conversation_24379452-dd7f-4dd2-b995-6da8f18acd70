import { DialogGeneral } from "../../../../widgets/dialog/dialog";
import GlobalStore from "../../../../core/globalStore/globalStore";
import { createUUID } from "../../../../utils/utils";

const ENumSetting = {
    bias: 0.5,
    intensity: 0.15,
    scale: 1,
    kernelRadius: 100,
    numSamples: 10,
    blendFactor: 1,
    blendCutoff: 0.3,
} as const;

enum ENameSetting {
    bias = "Occlusion Bias",
    intensity = "Occlusion Intensity",
    scale = "Occlusion Scale",
    kernelRadius = "Sample Radius",
    numSamples = "Number of Samples",
    blendFactor = "Blend Factor",
    blendCutoff = "Blend Cutoff",
}
type SettingKey = keyof typeof ENumSetting;

// Optional per-key slider ranges:
const ranges: Record<SettingKey, { min: number; max: number; step: number }> = {
    bias: { min: -2, max: 2, step: 0.1 },
    intensity: { min: 0, max: 1, step: 0.01 },
    scale: { min: 0.1, max: 5, step: 0.1 },
    kernelRadius: { min: 1, max: 200, step: 1 },
    numSamples: { min: 4, max: 100, step: 1 },
    blendFactor: { min: 0, max: 2, step: 0.1 },
    blendCutoff: { min: 0, max: 2, step: 0.01 },
};

export class ToolbarSetting {
    private _store = GlobalStore.getInstance().getAll();
    private _dialog: DialogGeneral | undefined;
    private _targetContainerId = this._store.containerId as string;
    private _idGeneral = createUUID();

    private _saoEnabled: boolean = false;
    private _saoBlur: boolean = true;
    private _edgeEnabled: boolean = false;


    //#region renderSettingModel
    containerBody: HTMLElement | null = null;
    private _listInput: ICreateInputOptions[] =
        (Object.keys(ENumSetting) as SettingKey[]).map((key) => ({
            id: `${key}-${this._idGeneral}`,                    // <-- key, not value
            label: ENameSetting[key],                           // label from enum
            defaultValue: ENumSetting[key],                     // default
            ...ranges[key],                                     // min/max/step
            callback: (value: string) => this.actionSAOInput(`${key}-${this._idGeneral}`, Number(value)),
        }));



    constructor() {
        this._createModal();

        this._dialog?.showModal(false)


        this.createSwitch(this.containerBody!);
        this.createListInput(this.containerBody!);

        this.saoEnabled = false
        this.saoBlur = true
    }

    get saoEnabled(): boolean {
        return this._saoEnabled;
    }

    set saoEnabled(value: boolean) {
        this._saoEnabled = value;
        if (this._store.viewer && this._store.viewer.scene.sao) {
            this._store.viewer.scene.sao.enabled = value;
        }
        if (!value) {
            this.containerBody?.querySelectorAll('.dpu-slider-container').forEach((container) => {

                (container as HTMLElement).style.pointerEvents = 'none';
                (container as HTMLElement).style.opacity = '0.5';
                (container as HTMLElement).style.cursor = 'not-allowed';
            });
        } else {
            this.containerBody?.querySelectorAll('.dpu-slider-container').forEach((container) => {
                (container as HTMLElement).style.removeProperty('pointer-events');
                (container as HTMLElement).style.removeProperty('opacity');
                (container as HTMLElement).style.removeProperty('cursor');
            });
        }

    }

    get saoBlur(): boolean {
        return this._saoBlur;
    }

    set saoBlur(value: boolean) {
        this._saoBlur = value;
        if (this._store.viewer && this._store.viewer.scene.sao) {
            this._store.viewer.scene.sao.blur = value;
        }
    }

    get edgeEnabled(): boolean {
        return this._edgeEnabled;
    }

    set edgeEnabled(value: boolean) {
        this._edgeEnabled = value;
        if (this._store.viewer && this._store.viewer.scene.sao) {
          
        }
    }

    //#region _createModal
    private _createModal() {
        this._dialog = new DialogGeneral(
            this._targetContainerId,
            this._handleCloseModal
        );
        this._dialog.innerHeader = "Setting";
        this._dialog.modal = false
        this._dialog.minimizeDialog = true;
        this._dialog.widthModal = '300px'
        this._dialog.heightModal = '500px'
        this._dialog.zIndex = 4000
        this._dialog.topModal = '5px'
        this._dialog.rightModal = '50px'
        // Lấy chuỗi HTML của tree view và gán cho innerBody
        this._dialog.innerBody = this.renderSettingModel();
        this._dialog.createDialog();



    }

    renderSettingModel = () => {
        if (this.containerBody) {
            this._clearContainer()
        } else {
            this.containerBody = document.createElement('div');
            this.containerBody.classList.add('dpu-setting-container');



        }
        return this.containerBody;
    }

    private _clearContainer = () => {
        if (this.containerBody) {
            this.containerBody.innerHTML = ""; // Xóa nội dung bên trong containerUl
        }
    };


    private _handleCloseModal = (status: boolean) => {
        if (!status) {
            this.notifyCallBack(status)
        }
    }
    // Gọi callback để thông báo thay đổi trạng thái
    notifyCallBack(status: boolean): void {
        if (this.onClosedCallback) {
            this.onClosedCallback(status); // Thông báo cho lớp cha
        } else {
            console.error("State change callback is not set.");
        }
    }

    // Gán callback từ lớp cha
    setClosedCallback(callback: (status: boolean) => void): void {
        this.onClosedCallback = callback;
    }

    // Thuộc tính để lưu callback
    private onClosedCallback: ((status: boolean) => void) | undefined;
    settingAction = (idElement: string) => {

    }


    //#region showHideModalSetting
    showHideModalSetting = (show: boolean) => {
        if (show) {

        } else {

        }
        this._dialog?.showModal(show)

    }

    //#region createInput
    createListInput = (container: HTMLElement) => {
        this._listInput.forEach((inputOption) => {
            createInput({ ...inputOption, containerDiv: container });
        })
    }

    //#region createSwitch
    createSwitch = (container: HTMLElement) => {
        const containerSwitch = document.createElement('div');
        containerSwitch.style.display = 'flex';
        containerSwitch.style.alignItems = 'center';
        containerSwitch.style.gap = '10px';
        containerSwitch.style.margin = '5px 0px 5px 0px';
        const sao = createSwitch({
            id: `sao-${this._idGeneral}`,
            defaultValue: this.saoEnabled,
            containerDiv: container,
            label: "SAO",
            callback: (value) => this.actionSwitch(`sao-${this._idGeneral}`, value)
        });

        containerSwitch.appendChild(sao);

        const blur = createSwitch({
            id: `blur-${this._idGeneral}`,
            defaultValue: this.saoBlur,
            containerDiv: container,
            label: "Blur",
            callback: (value) => this.actionSwitch(`blur-${this._idGeneral}`, value)
        });
        containerSwitch.appendChild(blur);

    

        container.appendChild(containerSwitch);
    }




    //#region actionInput
    actionSAOInput = (idElement: string, value: number) => {
        const sao = this._store.viewer?.scene.sao;
        if (!sao) return;

        // get the part before the first hyphen: "bias" from "bias-<uuid>"
        const dash = idElement.indexOf("-");
        const key = (dash >= 0 ? idElement.slice(0, dash) : idElement) as SettingKey;

        if (key in ENumSetting) {
            (sao as any)[key] = value;
        }
    };

    actionSwitch = (idElement: string, value: boolean) => {
        const sao = this._store.viewer?.scene.sao;

        switch (idElement) {
            case `sao-${this._idGeneral}`:
                if (sao) {
                    this.saoEnabled = value;

                }
                break;
            case `blur-${this._idGeneral}`:
                if (sao) {
                    this.saoBlur = value;
                }
                break;
        }
    }
}

interface ICreateInputOptions {
    id: string,
    defaultValue?: number,
    containerDiv?: HTMLElement,
    label?: string,
    callback: (value: string) => void,
    min?: number,
    max?: number,
    step?: number // Thêm bước nhảy
}
function createInput({
    id,
    defaultValue = 50,
    containerDiv,
    callback,
    min = 0,
    max = 100,
    step = 1,
    label
}: ICreateInputOptions) {
    const container = document.createElement('div');
    container.classList.add('dpu-slider-container');

    if (label) {
        const labelElem = document.createElement('label');
        labelElem.htmlFor = `${id}-slider`;
        labelElem.textContent = label;
        labelElem.classList.add('dpu-input-label');
        containerDiv?.appendChild(labelElem);
    }

    const slider = document.createElement('input');
    slider.type = 'range';
    slider.id = `${id}-slider`;
    slider.className = 'dpu-input-range dpu__slider';
    slider.min = min.toString();
    slider.max = max.toString();
    slider.step = step.toString();
    slider.value = defaultValue.toString();

    const input = document.createElement('input');
    input.type = 'number';
    input.id = `${id}-input`;
    input.className = 'dpu-input-text dpu__input-value';
    input.value = defaultValue.toString();
    input.min = min.toString();
    input.max = max.toString();
    input.step = step.toString();

    container.appendChild(slider);
    container.appendChild(input);

    slider.addEventListener('input', function () {
        input.value = slider.value;
        if (callback) callback(slider.value);
    });

    input.addEventListener('input', function () {
        let val = parseFloat(input.value);
        if (val < min) val = min;
        if (val > max) val = max;
        input.value = val.toString();
        slider.value = input.value;
        if (callback) callback(input.value);
    });

    containerDiv?.appendChild(container);
}



interface ICreateSwitchOptions {
    id: string;
    defaultValue?: boolean;
    containerDiv?: HTMLElement;
    label?: string;
    callback: (value: boolean) => void;
}

function createSwitch({
    id,
    defaultValue = false,
    containerDiv,
    label,
    callback
}: ICreateSwitchOptions) {
    const switchContainer = document.createElement('div');
    switchContainer.classList.add('dpu-switch-container');

    const switchLabel = document.createElement('span');

    switchLabel.textContent = label || '';
    switchContainer.appendChild(switchLabel);

    const labelElem = document.createElement('label');
    if (label) {

        labelElem.htmlFor = `${id}-switch`;

        labelElem.classList.add('dpu__switch');
        switchContainer.appendChild(labelElem);
    }

    const switchInput = document.createElement('input');
    switchInput.type = 'checkbox';
    switchInput.id = `${id}-switch`;
    switchInput.checked = defaultValue;

    const switchSlider = document.createElement('span');
    switchSlider.classList.add('dpu__switch-slider', 'round');

    labelElem.appendChild(switchInput);
    labelElem.appendChild(switchSlider);

    switchInput.addEventListener('change', function () {
        if (callback) callback(switchInput.checked);
    });
    return switchContainer
    // containerDiv?.appendChild(switchContainer);
}