import GlobalStore from "../../../core/globalStore/globalStore";
import { DataProperties } from "../dataProperties/data_properties";
import { Toolbar } from "./toolbar";

export class ToolbarDataProperties {
    private _dataProperties: DataProperties = new DataProperties();
    private isShowModal = false;
    private _toolbar: Toolbar | undefined;
    private _store = GlobalStore.getInstance().getAll();
    constructor(toolbar: Toolbar) {

        this._toolbar = toolbar;

    }

    toolBarActionDataProperties = (idElement: string) => {
        this.isShowModal = !this.isShowModal;
        this._dataProperties.showModal = this.isShowModal;
        if (this.isShowModal) {
            this._dataProperties.renderTableData();
        }
        this._dataProperties!.setClosedCallback((status: boolean) => {
            if (!status) {
                this.isShowModal = false; // Đảm bảo setter tự động xử lý khi đóng modal
                this._dataProperties.showModal = false
            }
        });
    }


}