/* Section Plane HTML Controls Styles */

.section-plane-control {
  position: absolute;
  width: 24px; /* Increased from 20px to 24px */
  height: 24px; /* Increased from 20px to 24px */
  border-radius: 50%;
  background-color: rgba(0, 218, 183, 0.8);
  border: 2px solid rgb(0, 218, 183);
  cursor: pointer;
  z-index: 1000;
  display: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  /* transition: all 0.2s ease; */
  user-select: none;
  pointer-events: auto;

  /* Center the control */
  transform: translate(-50%, -50%);
}

.section-plane-control:hover {
  /* background-color: rgba(255, 0, 0, 0.8) !important;
    border: 2px solid rgb(255, 0, 0) !important; */
  /* transform: translate(-50%, -50%) scale(1.2) !important; */
  scale: 1.2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

.section-plane-control:active {
  /* background-color: rgba(255, 100, 100, 0.9) !important;
  border: 2px solid rgb(255, 100, 100) !important; */
  /* transform: translate(-50%, -50%) scale(1.1) !important; */
  scale: 1.1;
}

/* Specific styles for different section plane types */
.section-plane-control[id*="left"] {
  border-color: rgb(220, 0, 0); /* Red for X-axis */
  background-color: rgba(220, 0, 0, 0.8);
}

.section-plane-control[id*="right"] {
  border-color: rgb(220, 0, 0); /* Red for X-axis */
  background-color: rgba(220, 0, 0, 0.8);
}

.section-plane-control[id*="top"] {
  border-color: rgb(0, 200, 95);
  background-color: rgba(0, 200, 95, 0.8);
}

.section-plane-control[id*="bottom"] {
  border-color: rgb(0, 200, 95);
  background-color: rgba(0, 200, 95, 0.8);
}

.section-plane-control[id*="front"] {
  border-color: rgb(0, 90, 255);
  background-color: rgba(0, 90, 255, 0.8);
}

.section-plane-control[id*="back"] {
  border-color: rgb(0, 90, 255);
  background-color: rgba(0, 90, 255, 0.8);
}

/* Custom color for Y_ROTATE arrow */
.section-plane-control[data-plane-type="Y_ROTATE"] {
  border-color: rgba(140, 140, 140, 1);
  background-color: rgba(240, 240, 240, 1);
}

/* Hover effects for specific types */
/* .section-plane-control[id*="left"]:hover,
.section-plane-control[id*="right"]:hover {
  background-color: rgba(255, 100, 100, 0.9) !important;
  border-color: rgb(255, 100, 100) !important;
}

.section-plane-control[id*="top"]:hover,
.section-plane-control[id*="bottom"]:hover {
  background-color: rgba(100, 255, 100, 0.9) !important;
  border-color: rgb(100, 255, 100) !important;
}

.section-plane-control[id*="front"]:hover,
.section-plane-control[id*="back"]:hover {
  background-color: rgba(100, 100, 255, 0.9) !important;
  border-color: rgb(100, 100, 255) !important;
} */

/* Animation for when controls appear */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.section-plane-control.show {
  animation: fadeInScale 0.3s ease-out;
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .section-plane-control {
    border-width: 1px;
  }
}

/* Focus styles for accessibility */
.section-plane-control:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Disabled state */
.section-plane-control.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Loading state */
.section-plane-control.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Arrow indicator using real element instead of ::after */
.section-control-arrow {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 0 !important;
  height: 0 !important;
  transform: translate(-50%, -50%); /* Will be overridden by inline style */
  opacity: 1 !important; /* Increased from 0.7 to 1 for better visibility */
  /* transition: transform 0.2s ease !important; */
  pointer-events: none !important; /* Arrow should not block clicks */
  z-index: 10 !important; /* Ensure arrow is on top */
  display: block !important; /* Force display */
  visibility: visible !important; /* Force visibility */
  filter: drop-shadow(
    0 0 2px rgba(0, 0, 0, 0.8)
  ) !important; /* Add shadow for better contrast */
}

/* Direction arrows for each plane type using real elements - Larger and more visible */
/* .section-control-arrow[data-arrow-type="left"] {
  border-top: 6px solid transparent !important;
  border-bottom: 6px solid transparent !important;
  border-right: 8px solid #ffffff !important;
}

.section-control-arrow[data-arrow-type="right"] {
  border-top: 6px solid transparent !important;
  border-bottom: 6px solid transparent !important;
  border-left: 8px solid #ffffff !important;
}

.section-control-arrow[data-arrow-type="top"] {
  border-left: 6px solid transparent !important;
  border-right: 6px solid transparent !important;
  border-bottom: 8px solid #ffffff !important;
}

.section-control-arrow[data-arrow-type="bottom"] {
  border-left: 6px solid transparent !important;
  border-right: 6px solid transparent !important;
  border-top: 8px solid #ffffff !important;
}

.section-control-arrow[data-arrow-type="front"] {
  border-left: 6px solid transparent !important;
  border-right: 6px solid transparent !important;
  border-top: 8px solid #ffffff !important;
}

.section-control-arrow[data-arrow-type="back"] {
  border-left: 6px solid transparent !important;
  border-right: 6px solid transparent !important;
  border-bottom: 8px solid #ffffff !important;
} */

/* Enhanced arrow visibility on hover */
.section-plane-control:hover .section-control-arrow {
  opacity: 1 !important;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8))
    drop-shadow(0 0 6px rgba(0, 0, 0, 1)) !important;
  /* transform: translate(-50%, -50%) scale(1.1) !important;  */
  scale: 1.1;
}

/* Arrow color variations for different plane types */
.section-plane-control[id*="left"] .section-control-arrow,
.section-plane-control[id*="right"] .section-control-arrow {
  filter: drop-shadow(0 0 2px rgba(255, 0, 0, 0.6))
    drop-shadow(0 0 3px rgba(0, 0, 0, 0.8)) !important;
}

.section-plane-control[id*="top"] .section-control-arrow,
.section-plane-control[id*="bottom"] .section-control-arrow {
  filter: drop-shadow(0 0 2px rgba(0, 255, 0, 0.6))
    drop-shadow(0 0 3px rgba(0, 0, 0, 0.8)) !important;
}

.section-plane-control[id*="front"] .section-control-arrow,
.section-plane-control[id*="back"] .section-control-arrow {
  filter: drop-shadow(0 0 2px rgba(0, 0, 255, 0.6))
    drop-shadow(0 0 3px rgba(0, 0, 0, 0.8)) !important;
}

/* Active state when dragging - make arrow super visible */
.section-plane-control:active .section-control-arrow {
  opacity: 1 !important;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 1))
    drop-shadow(0 0 8px rgba(0, 0, 0, 1)) !important;
  /* transform: translate(-50%, -50%) scale(1.2) !important; */
  scale: 1.2;
}

.section-control-arrow svg {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 15px;
  transform: translate(-50%, -50%);
  stroke-width: 70px;
  stroke: #fff;
}

.section-control-arrow.rotate svg {

  stroke: #2c2c2c;
  stroke-width: unset;

}

/* Alternative high-contrast arrow style for maximum visibility */
@media (prefers-contrast: high) {
  .section-control-arrow {
    filter: drop-shadow(0 0 1px #000000) drop-shadow(0 0 1px #000000)
      drop-shadow(0 0 1px #000000) !important;
  }
}
