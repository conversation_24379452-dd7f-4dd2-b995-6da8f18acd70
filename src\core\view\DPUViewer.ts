import { FastNavPlugin, GLTFLoaderPlugin, Viewer } from "@xeokit/xeokit-sdk";
import { createUUID } from "../../utils/utils";
import GlobalStore from "../globalStore/globalStore";

import { ContextMenu } from "./interaction/contextMenu";
import { SceneManager, XKTLoadConfig } from "./sceneManager/sceneManager";
import { Toolbar, ToolbarOption } from "./toolbar/toolbar";

import { Measure } from "./measure/measure";
import { Coordinates } from "./coordinates/coordinates";
import { SectionBox } from "./sections/sectionBox";
import { SectionSurfacePlanes } from "./sections/sectionPlane";
import { ModelConfig } from "./sceneManager/plugin/loadModel";
import { FooterView } from "./footer/footerView";
import { NavCube } from "./navCube/navCube";
import { EventHandler } from "./interaction/eventHandler";
import { EventHandlerMergeEntity } from "./interaction/eventHandlerMergeEntity";
import { IInputTreeLayerModel, INodeLayerModel, IObjTreeLayerModel } from "../../types/layerModel/layerModel";
import { TreeViewNode } from "@xeokit/xeokit-sdk/types/plugins/TreeViewPlugin/TreeViewNode";
import { ViewCullPlugin } from "../../widgets/viewCullPlugin/ViewCullPlugin";
import { SectionSplit } from "./sections/sectionSplit/sectionSplit";


export class DPUViewer {
    private _idGeneral = createUUID()
    // Lấy instance
    store = new GlobalStore({ idViewer: this._idGeneral });
    viewer: Viewer | undefined;
    private _gltfLoader: GLTFLoaderPlugin | undefined;
    private _eventHandler: EventHandler | EventHandlerMergeEntity | undefined;
    private _contextMenu: ContextMenu | undefined;
    private _sceneManager: SceneManager | undefined; // Chưa sử dụng, có thể xóa sau này
    private _toolbar: Toolbar | undefined; // Chưa sử dụng, có thể xóa sau này
    private _measure: Measure | undefined; // Chưa sử dụng, có thể xóa sau này
    private _coordinates: Coordinates | undefined; // Chưa sử dụng, có thể xóa sau này
    private _sectionPlane: SectionSurfacePlanes | undefined; // Chưa sử dụng, có thể xóa sau này
    private _sectionSplit: SectionSplit | undefined; // Chưa sử dụng, có thể xóa sau này
    private _optionToolbar: ToolbarOption = {
        layer: true,
        measure: true, // Mặc định bật Measure
        cordinates: true,
        section: true,
        dataProperties: true,
        setting: true,
        mergeEntityPick: true,


    }
    constructor(element: Element, token?: string, optionToolbar?: ToolbarOption) {
        //Set token in global store
        this.store.set("token", token)
        this._optionToolbar = { ...this._optionToolbar, ...optionToolbar };

        this._initializeView(element);
    }

    private _initializeView(element: Element) {


        const containerCanvas = document.createElement("div");
        containerCanvas.id = `dpu-container-viewer-${this._idGeneral}`;
        containerCanvas.className = "dpu-container-view";
        element.appendChild(containerCanvas);

        const createCanvas = document.createElement("canvas");
        createCanvas.id = `dpu-viewer-${this._idGeneral}`;
        createCanvas.className = "dpu-viewer";
        containerCanvas.appendChild(createCanvas);


        this.viewer = new Viewer({
            canvasId: `dpu-viewer-${this._idGeneral}`,
            dtxEnabled: false,


        });
        // Set far plane default camera distance
        this.viewer.scene.camera.perspective.near = 0.1;
        this.viewer.scene.camera.perspective.far = 100000;


        const cameraControl = this.viewer.cameraControl;
        cameraControl.panRightClick = false; // Prevents right-click-drag panning interfering with ContextMenus
        cameraControl.pointerEnabled = true; // Enables pointer events for camera control



        //makes interaction smoother  performance
        new FastNavPlugin(this.viewer, {
            hideEdges: true,
            hideSAO: true,
            hideColorTexture: true,
            hidePBR: false,

            hideTransparentObjects: false,
            scaleCanvasResolution: false,
            scaleCanvasResolutionFactor: 0.5,
            delayBeforeRestore: true,
            delayBeforeRestoreSeconds: 0.4
        });

        // this._viewer.scene.clearLights();
        // //Light for scene
        // new AmbientLight(this._viewer.scene, {
        //     color: [1.0, 1.0, 1.0], // trắng tinh
        //     intensity: 2.2        // tăng cường độ
        // });

        // //Sao for scene
        // const sao = this._viewer.scene.sao;
        // sao.enabled = true;
        // sao.blur =true;
        // sao.bias = -0.45;
        // sao.intensity = 0.20;
        // sao.scale=1.5;
        // sao.kernelRadius=70;
        // sao.numSamples = 100
        // sao.minResolution = 0

        const viewCullPlugin = new ViewCullPlugin(this.viewer, {
            maxTreeDepth: 10,
        });
        viewCullPlugin.enabled = true;

        // (this.viewer.scene as any).crossSections.sliceThickness = 0.05;
        // (this.viewer.scene as any).crossSections.sliceColor = [0.0, 0.0, 0.0, 1.0];
        console.log('viewCullPlugin', viewCullPlugin);

        this.viewer.scene.camera.worldAxis = [1, 0, 0, 0, 1, 0, 0, 0, -1];

        // Set camera control mouse buttons
        // Lưu vào store
        this.store.set("viewer", this.viewer);
        this.store.set("idGeneral", this._idGeneral);
        this.store.set("containerViewer", element);
        this.store.set("containerCanvas", containerCanvas);
        this.store.set("canvas", createCanvas);
        this.store.set("canvasId", `dpu-viewer-${this._idGeneral}`);
        this.store.set("containerId", `dpu-container-viewer-${this._idGeneral}`);
        // this._store.set("viewCullPlugin", viewCullPlugin);
        if (this._optionToolbar.mergeEntityPick) {
            this._eventHandler = new EventHandlerMergeEntity(); // Sử dụng EventHandlerMergeEntity để xử lý sự kiện kết hợp thực thể

        } else {

            this._eventHandler = new EventHandler(); // Sử dụng EventHandler để xử lý sự kiện
        }
        // this._eventHandler = new EventHandler(); // Chưa sử dụng, có thể xóa sau này
        this._contextMenu = new ContextMenu(); // Chưa sử dụng, có thể xóa sau này
        this._sceneManager = new SceneManager(); // Chưa sử dụng, có thể xóa sau này
        this._toolbar = new Toolbar(this._optionToolbar); // Chưa sử dụng, có thể xóa sau này
        this._measure = new Measure(); // Chưa sử dụng, có thể xóa sau này 
        this._coordinates = new Coordinates(); // Chưa sử dụng, có thể xóa sau này
        this._sectionPlane = new SectionSurfacePlanes(); // Chưa sử dụng, có thể xóa sau này

        new FooterView();
        new NavCube()

        // if (!this._sectionSplit) {

        //     this._sectionSplit = new SectionSplit()
        // }

        if (this._eventHandler) {
            this.store.set("eventHandler", this._eventHandler);
        }

        console.log("store", this.store.getAll());
    }

    private _queue: Promise<any> = Promise.resolve();  // Thay vì Promise<void>, sử dụng Promise<any> để linh hoạt
    // Hàm thêm tác vụ vào hàng đợi
    private enqueue<T>(task: () => Promise<T>): Promise<T> {
        this._queue = this._queue
            .then(() => task()) // Đảm bảo rằng các tác vụ được xử lý tuần tự
            .catch(err => {
                console.log(err);
                // throw err; // Ném lỗi để các tác vụ sau đó có thể xử lý lỗi
            });
        return this._queue as Promise<T>;  // Ép kiểu trả về Promise<T>
    }

    public gltfLoad = ({ name, modelSrc }: { name: string, modelSrc: string }) => {

        this._sceneManager?.gtlfLoad(name, modelSrc);

    }
    public cityJsonLoad = async ({ name, modelSrc }: { name: string, modelSrc: string }) => {
        const respone = await fetch(modelSrc);
        const data = await respone.json();
        console.log(data);
        this._sceneManager?.cityJsonLoad(name, data);
    }


    //#region loadModelFromTree
    loadModelFromTree = async (data: IInputTreeLayerModel) => {
        const listTree: INodeLayerModel[] = []
        let dataModel: IObjTreeLayerModel[] = []
        if (data.url) {
            // If data.obj is a string, assign it directly
            const response = await fetch(data.url!);
            dataModel = await response.json();
        } else {
            dataModel = data.arrObj ?? [];
            console.log("dataModel", dataModel);
        }

        // Sort: items with null/undefined URL first
        if (Array.isArray(dataModel)) {
            dataModel.sort((a, b) => {
                // Guard when an element itself can be undefined/null
                if (a == null && b != null) return -1;
                if (a != null && b == null) return 1;

                const aUrl = a?.url as string | null | undefined;
                const bUrl = b?.url as string | null | undefined;

                // Treat null, undefined, and empty string as "no URL" and move to the top
                const aNoUrl = aUrl == null || aUrl === "";
                const bNoUrl = bUrl == null || bUrl === "";

                if (aNoUrl && !bNoUrl) return -1;
                if (!aNoUrl && bNoUrl) return 1;
                return 0; // keep relative order otherwise
            });
        }

        for (const item of dataModel || []) {
            const newNode: INodeLayerModel = {
                id: item.id,
                title: item.name,
                url: item.url,
                isShow: item.isShow || true,
                isFolder: item.isFolder || false,
                metaSrc: item.metaSrc || "",
                primitive: item.primitive || "",
                children: [],
                nodeXeokit: undefined, // Chưa có node xeokit, sẽ được cập nhật sau khi load model
            };
            const parentNode = listTree.find(node => node.id === item.parentId || node.id === item.parentID);
            if (parentNode) {
                parentNode.children?.push(newNode);
                newNode.parent = parentNode;
            }

            if (!item.isFolder) {


                let dataSrc
                let linkFile
                let linkFileLoadLoad: string[] = []
                if (item.linkFile) {
                    const responseLinkFile = await fetch(item.linkFile);
                    linkFile = await responseLinkFile.json();
                    const u = new URL(item.linkFile)
                    u.pathname = u.pathname.replace(/[^/]+$/, ""); // bỏ segment cuối
                    const base = u.origin + u.pathname;            // không giữ query/hash
                    console.log(base); // .../bim/5897/V0/
                    linkFile.childrens?.forEach((child: any) => {
                        linkFileLoadLoad.push(base + child.geometry)

                    })


                    console.log("linkFileLoadLoad", linkFileLoadLoad);
                    console.log("linkFile", linkFile);
                    console.log('item', item);
                }
                if (item.primitive) {

                    dataSrc = await handleCallApi(item.primitive!, this.store.getAll().token!);
                }

                for (const src of linkFileLoadLoad) {
                    const idChild = createUUID();
                    await this._sceneManager?.loadModel({
                        id: idChild,
                        src: src,
                        name: newNode.title,
                        metaModelSrc: dataSrc?.data ?? newNode.metaSrc ?? "",
                        position: linkFile?.transform ? [linkFile?.transform[0], (linkFile?.transform[2]), linkFile?.transform[1]] : [0, 0, 0],
                        scale: [1, 1, -1],
                        autoMetaModel: true,
                    }, (e) => {
                        console.log('e', e);
                        // e.rotationMatrix[10] = -1
                        // e.scale = [1, -1, 1],//Dùng scale để đảo mô hình
                        listTree.push(newNode);
                        // console.log('newNode', newNode);
                        // const findParentNode = listTree.find(node => node.id === newNode.id );
                        // console.log("findParentNode", findParentNode);
                        // if (parentNode) {
                        //     parentNode.children?.push(newNode);
                        //     newNode.parent = parentNode;
                        // }

                        const treePlugin = (this.store.getAll().treeLayerModel?.treeViewPlugin) as any;
                        const nodeId = idChild
                        if (treePlugin._objectNodes[nodeId]) {
                            newNode.nodeXeokit = treePlugin._objectNodes[nodeId];
                            const findParentNode = listTree.find(node => node.id === newNode.parent?.id);

                            pushNodeXeokitToAllAncestors(treePlugin._objectNodes[nodeId], findParentNode);

                        }
                        if (parentNode) {
                            parentNode.nodeXeokit = treePlugin._objectNodes[nodeId];
                            pushNodeXeokitToAllAncestors(treePlugin._objectNodes[nodeId], parentNode);

                        }
                        // const nodeId = newNode.id
                        // if (treePlugin._objectNodes[nodeId]) {
                        //     newNode.nodeXeokit = treePlugin._objectNodes[nodeId];
                        //     const findParentNode = listTree.find(node => node.id === newNode.parent?.id);

                        //     pushNodeXeokitToAllAncestors(treePlugin._objectNodes[nodeId], findParentNode);

                        // }
                        // if (parentNode) {
                        //     parentNode.nodeXeokit = treePlugin._objectNodes[nodeId];
                        //     pushNodeXeokitToAllAncestors(treePlugin._objectNodes[nodeId], parentNode);

                        // }
                        console.log(nodeId, treePlugin._objectNodes[nodeId]);
                        // console.log(parentNode?.id, parentNode);
                        treePlugin._objectNodes[nodeId]?.children.map((node: any) => {
                            const newNodeChild: INodeLayerModel = {
                                id: "",
                                title: "",
                                children: [],
                                nodeXeokit: undefined,
                                parent: null,
                                url: "",
                                isShow: parentNode?.isShow || true,
                                isFolder: false,
                                metaSrc: "",
                                expand: false,
                                level: 0,
                                primitive: ""
                            }
                            newNodeChild.id = node.objectId;
                            // newNodeChild.id = node.nodeId;
                            newNodeChild.title = node.title;
                            newNodeChild.parent = newNode;
                            newNodeChild.nodeXeokit = node;


                            newNode.children?.push(newNodeChild);
                            listTree.push(newNodeChild);
                        })


                        this.store.getAll().treeLayerModel?.mapNewList(listTree);

                        console.log("load xong");
                    });
                }

            } else {
                listTree.push(newNode);
            }

        }
        console.log("listTree", listTree);
        return listTree;
    }

    private _totalModels = 0;
    private _loadedModels = 0
    //#region XKTLoad
    XKTLoad = (config: XKTLoadConfig, okFunc?: (model: any) => void) => {
        const listTree: INodeLayerModel[] = [];
        const treeViewNode = (this.store.getAll().treeLayerModel?.treeViewPlugin) as any;
        const canvas = this.store.getAll().canvas as HTMLCanvasElement;

        this._sceneManager?.XKTLoad(config, (e) => {


            this._totalModels++;
            this._renderLoadedModel();
            treeViewNode.addModel(config.id as string, {
                rootName: config.name,
            });
            console.time('XKT_Load');
            const rootNode = treeViewNode._rootNodes.find((node: any) => node.nodeId.includes(treeViewNode._id + "-" + config.id));

            // Dynamic batch size: hạ về 30 khi tương tác canvas, tăng dần lên max 200 nếu không tương tác
            const entries = Object.entries((treeViewNode as any)._nodeNodes);
            const BATCH_SIZE = 30
            let batchSize = BATCH_SIZE;
            let index = 0;
            const self = this;
            let interaction = false;
            let interactionTimeout: any = null;

            function onCanvasInteract() {
                // Chỉ tính tương tác khi drag hoặc wheel hoặc wheel down
                let isDrag = false;
                let isWheel = false;
                let isWheelDown = false;
                // event: MouseEvent | WheelEvent
                const e = arguments[0] as MouseEvent | WheelEvent;
                if (e.type === 'mousemove' && 'buttons' in e && (e as MouseEvent).buttons === 1) {
                    isDrag = true;
                }
                if (e.type === 'wheel') {
                    isWheel = true;
                    if ('deltaY' in e && (e as WheelEvent).deltaY > 0) {
                        isWheelDown = true;
                    }
                }
                if (isDrag || isWheel || isWheelDown) {
                    batchSize = BATCH_SIZE;
                    interaction = true;
                    if (interactionTimeout) clearTimeout(interactionTimeout);
                    interactionTimeout = setTimeout(() => {
                        interaction = false;
                    }, 500);
                }
            }
            canvas.addEventListener('mousemove', onCanvasInteract);
            // mousemove: chỉ khi đang drag (giữ chuột)
            canvas.addEventListener('mousemove', onCanvasInteract as EventListener);
            // wheel: bất kỳ cuộn chuột nào
            canvas.addEventListener('wheel', onCanvasInteract as EventListener);



            let batchRunning = true;
            let batchInterval: any = null;
            function processBatch() {
                if (!batchRunning) return;
                if (!interaction && batchSize < 1500) {
                    batchSize = Math.min(batchSize + 200, 1500);
                    // console.log('Tăng batchSize lên', batchSize);
                }
                let processed = 0;
                const miniBatch = 10;
                while (index < entries.length && processed < batchSize) {
                    for (let i = 0; i < miniBatch && index < entries.length && processed < batchSize; i++, index++, processed++) {
                        const [key, value]: [string, any] = entries[index];
                        const newNodeChild: INodeLayerModel = {
                            id: value.nodeId ?? createUUID(),
                            title: value.title,
                            children: value.children || [],
                            nodeXeokit: value,
                            parent: null,
                            url: "",
                            isShow: true,
                            isFolder: false,
                            metaSrc: "",
                            expand: false,
                            level: 0,
                            primitive: "",
                        };
                        if (key !== rootNode.nodeId) {
                            if (value.parent) {
                                const parentNode = listTree.find(node => node.id === value.parent.nodeId);
                                if (parentNode) {
                                    newNodeChild.parent = parentNode;
                                }
                            }
                        }
                        listTree.push(newNodeChild);
                    }
                    if (interaction && batchSize === BATCH_SIZE) break;
                }
                if (index < entries.length) {
                    if (document.visibilityState === "visible") {
                        if (batchInterval) {
                            clearInterval(batchInterval);
                            batchInterval = null;
                        }
                        window.requestAnimationFrame(processBatch);
                    } else {
                        if (!batchInterval) {
                            batchInterval = setInterval(processBatch, 50);
                        }
                    }
                } else {
                    batchRunning = false;
                    if (batchInterval) {
                        clearInterval(batchInterval);
                        batchInterval = null;
                    }
                    document.removeEventListener('visibilitychange', onVisibilityChange);
                    canvas.removeEventListener('mousemove', onCanvasInteract);
                    canvas.removeEventListener('mousedown', onCanvasInteract);
                    canvas.removeEventListener('wheel', onCanvasInteract);
                    canvas.removeEventListener('touchstart', onCanvasInteract);
                    if (interactionTimeout) clearTimeout(interactionTimeout);
                    console.timeEnd('XKT_Load');
                    console.time('Refresh');
                    self.store.getAll().treeLayerModel?.mapNewList(listTree);
                    self._loadedModels++;
                    self._renderLoadedModel();
                    console.timeEnd('Refresh');
                }
            }

            function onVisibilityChange() {
                if (document.visibilityState === "visible" && batchInterval) {
                    clearInterval(batchInterval);
                    batchInterval = null;
                    window.requestAnimationFrame(processBatch);
                }
                // Khi tab bị ẩn, interval sẽ tự động chạy trong processBatch
            }
            document.addEventListener('visibilitychange', onVisibilityChange);
            processBatch();

            console.timeEnd('XKT_Load');

            console.time('Refresh');
            this.store.getAll().treeLayerModel?.mapNewList(listTree);
            console.timeEnd('Refresh');

        })





        // okFunc
    }


    //
    private _containerLoad: HTMLElement | undefined
    private _processingElement: HTMLElement | undefined
    private _renderLoadedModel = () => {
        const containerView = this.store.getAll().containerViewer
        if (!containerView) return;
        if (!this._containerLoad) {
            this._containerLoad = document.createElement("div");
            this._containerLoad.id = `dpu-container-load-${this._idGeneral}`;
            this._containerLoad.className = "dpu-waiting-load-model";
            this._processingElement = document.createElement("div");
            this._processingElement.className = "dpu-processing-load-model";
            this._processingElement.id = `dpu-processing-load-${this._idGeneral}`;
            this._containerLoad.appendChild(this._processingElement);
            containerView.appendChild(this._containerLoad);
        }
        if (this._loadedModels < this._totalModels) {
            if (this._processingElement) {
                this._processingElement.style.width = `${(this._loadedModels / this._totalModels) * 100}%`;
            }
        } else {
            if (this._containerLoad) {
                containerView.removeChild(this._containerLoad);
                this._containerLoad = undefined;
                this._processingElement = undefined;
            }
        }

    }


    public loadModel = async (modelConfig: ModelConfig, okFunc?: (model: any) => void) => {
        await this._sceneManager?.loadModel(modelConfig, () => {

            okFunc
        });


    }

    loadIFC = async ({ modelId, modelSrc, pathWasm }: { modelId: string, modelSrc: string, pathWasm: string }) => {
        await this._sceneManager?.ifcLoad({ modelId, modelSrc, pathWasm });
    }
    loadIFCv2 = async ({ modelId, modelSrc }: { modelId: string, modelSrc: string }) => {
        await this._sceneManager?.ifcLoadV2({ modelId, modelSrc });
    }

    //#region flyToLocation
    flyToLocation = (position: [number, number, number]) => {
        this.viewer?.cameraFlight.flyTo({

            duration: 1,
            eye: position,
        });
    }

    //#region getCameraView
    getCameraView = () => {
        return this._sceneManager?.getCameraView() || {};
    }

    //#region setCameraView
    setCameraView = (data: {
        cameraEye: [number, number, number] | undefined,
        cameraLook: [number, number, number] | undefined,
        cameraUp: [number, number, number] | undefined,
    }) => {
        this._sceneManager?.setCameraView(data);
    }



    //#region createSectionPlane
    createSectionPlane = (inputObjSection: {
        pos: [number, number, number];
        dir: [number, number, number];
        active: boolean;
    }) => {

        this._sectionPlane?.createSectionPlane(inputObjSection);
    }





    XKTLoadManifest = ({ modelId, manifestSrc }: { modelId: string, manifestSrc: string }) => {
        return this._sceneManager?.XKTLoadManifest({ modelId, manifestSrc });
    }

    zoomByIds = (ids: string[]) => {
        //Show off
        this.viewer?.scene.setObjectsVisible(this.viewer.scene.objectIds, false);

        //Show by ids
        this.viewer?.scene.setObjectsVisible(ids, true);

        this.viewer?.cameraFlight.flyTo({
            aabb: this.viewer?.scene.getAABB(ids),
            duration: 0.5
        })
    }
}
const handleCallLinkFile = async (linkFile: string, token: string) => {

}

const handleCallApi = async (primitive: string, token: string) => {
    try {
        const response = await fetch(
            `https://translateservicedev.corebim.com/queryTranslateApi/Translate/GetIFCStructure`,
            {
                method: "POST",
                headers: {
                    'accept': 'text/plain', // Đảm bảo 'accept' header giống với cURL
                    'Authorization': `Bearer ${token}`, // Token phải được truyền đúng
                    'Content-Type': 'application/json' // Thay thế 'application/json-patch+json' thành 'application/json'
                },
                body: JSON.stringify({
                    primitives: [
                        primitive
                    ]
                })
            }
        );
        const result = await response.json();
        return result;
    } catch (e) {
        return undefined;
    }


}

const pushNodeXeokitToAllAncestors = (xeokitNode: TreeViewNode, parentNode?: INodeLayerModel) => {
    if (!parentNode) return;
    if (!parentNode.listNodesXeokit) parentNode.listNodesXeokit = [];
    // Chỉ push nếu chưa tồn tại
    if (!parentNode.listNodesXeokit.some(node => node.objectId === xeokitNode.objectId)) {
        parentNode.listNodesXeokit.push(xeokitNode);
    }
    // Đệ quy tiếp tục lên cha
    if (parentNode.parent) {
        pushNodeXeokitToAllAncestors(xeokitNode, parentNode.parent as INodeLayerModel);
    }
}