.dpu-context-menu {
  font-size: 14px;
  position: absolute;
  display: none;
  z-index: 9999;
  background-color: var(--background-color-1);
  padding: 4px;
  border-radius: 5px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text-1);
}

.dpu-context-menu ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.dpu-context-menu li {
  padding: 8px 10px;
  cursor: pointer;
}

.dpu-context-menu li:hover {
  background-color: var(--color-hover-1);
}

/* ----------------------------------------------------------------------------------------------------------*/
/* ContextMenu */
/* ----------------------------------------------------------------------------------------------------------*/

.xeokit-context-menu {
  user-select: none;
  font-family: inherit;
  font-size: 14px;
  background: var(--background-color-1) !important;
  border: none !important;
  padding: 4px;
  border-radius: 5px !important;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2) !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text-1);
}

.xeokit-context-menu ul {
  list-style: none;
  margin-left: 0;
  padding: 0;
}

.xeokit-context-menu-item {
  list-style-type: none;
  padding: 5px 6px;
  cursor: pointer;
}

.xeokit-context-menu-item:hover {
  background-color: var(--color-hover-1);
}

.xeokit-context-menu-item span {
  display: block;
}

.xeokit-context-menu .disabled {
  color: gray;
  cursor: default;
}

.xeokit-context-menu .disabled:hover {
  background-color: var(--color-hover-1);
  cursor: no-drop;
  
}

.xeokit-context-menu-item-separator {
  background: rgb(187, 187, 187);
  height: 1px;
  width: 100%;
}
