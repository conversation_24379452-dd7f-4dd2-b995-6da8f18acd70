// using ES6 modules
import Split from 'split.js';
import GlobalStore, { StoreConfig } from '../../../../core/globalStore/globalStore';
import { createUUID } from '../../../../utils/utils';
import { SceneModel, Viewer } from '@xeokit/xeokit-sdk';
import { DPUViewer } from '../../DPUViewer';

interface MeshConfig {
    id: string;
    textureSetId?: string;
    color?: Float32Array;
    opacity?: number;
    metallic?: number;
    roughness?: number;
    primitive?: string;
    localPositions?: any;
    positions?: Float64Array;
    normals?: any;
    uv?: any;
    indices?: any;
    origin?: any;
    edgeIndices?: any;

}


export class SectionSplit {
    // Chuyển Uint8Array hoặc mảng 0..255 về mảng 0..1

    private _globalStore: GlobalStore | undefined
    private _store: StoreConfig | undefined
    private _idGeneral = createUUID();
    viewerSplit: Viewer | undefined;
    constructor(globalStore: GlobalStore) {
        this.initSplit()
        this._globalStore = globalStore
        this._store = globalStore.getAll()
        this._globalStore?.set('sectionSplit', this);
    }



    initSplit = () => {
        const containerView = this._store?.containerViewer
        const token = this._store?.token
        if (!containerView) return;
        containerView?.classList.add('dpu-split');
        const newSplitDiv = document.createElement('div');
        newSplitDiv.id = 'dpu-section-split-' + this._idGeneral;
        newSplitDiv.className = 'dpu-section-split-container';
        containerView?.appendChild(newSplitDiv);


        // const containerViewSplitId = viewerSplit.store.getAll().containerId
        const containerViewId = this._store?.containerId
        // if (!containerViewSplitId) return;

        Split([`#${containerViewId}`, `#${newSplitDiv.id}`],)

        const viewerSplit = new DPUViewer(newSplitDiv, token, {
            layer: false,
            measure: true,
            cordinates: true,
            section: false,
            dataProperties: false,
            setting: false,
            mergeEntityPick: true,

        });
    }

    // private _conatainerViewSplit: HTMLElement = document.createElement('div')
    // private _canvasViewSplit: HTMLCanvasElement = document.createElement('canvas')
    // initSplit = () => {
    //     this._conatainerViewSplit.id = 'dpu-container-view-split-' + this._idGeneral;
    //     this._canvasViewSplit.id = 'dpu-viewer-split-' + this._idGeneral;
    //     this._conatainerViewSplit.appendChild(this._canvasViewSplit);
    //     this._store.containerViewer?.classList.add('dpu-split');
    //     this._store.containerViewer?.appendChild(this._conatainerViewSplit);

    //     this._canvasViewSplit.className = "dpu-viewer";
    //     this.viewerSplit = new Viewer({
    //         canvasId: this._canvasViewSplit.id,
    //         transparent: true,
    //     });
    //     Split([`#${this._store.containerId}`, `#${this._conatainerViewSplit.id}`])
    // }

    createEntity = () => {
        const models = this._store?.viewer?.scene.models;
        console.log('model', models);
        for (const key in models) {
            const model = models[key] as any;

            const sceneModel = new SceneModel(this.viewerSplit?.scene, {
                id: model.id,
                position: [0, 0, 0],
                scale: [1, 1, -1],
                isModel: true,
                edges: true,


            }) as any;

            console.time('createMesh');

            // model._meshConfigs.forEach((cfg: MeshConfig) => {
            //     sceneModel.createMesh({
            //         id: cfg.id,
            //         primitive: (cfg.primitive === 'points' || cfg.primitive === 'lines' || cfg.primitive === 'triangles' ? cfg.primitive : 'triangles'),
            //         positions: (cfg.positions ? Array.from(cfg.positions) : []),
            //         normals: cfg.normals || [],
            //         colors: [],
            //         color: cfg.color ? Array.from(cfg.color) : [],
            //         uv: cfg.uv || [],
            //         indices: cfg.indices ? Array.from(cfg.indices) : [],
            //         edgeIndices: cfg.edgeIndices ? Array.from(cfg.edgeIndices) : [],
            //         origin: cfg.origin || [],
            //         geometryId: cfg.id,
            //         positionsCompressed: [],
            //         positionsDecodeMatrix: [],
            //         normalsCompressed: [],
            //         colorsCompressed: [],
            //         uvCompressed: [],
            //         uvDecodeMatrix: [],

            //     })
            // })
            model._meshConfigs.forEach((cfg: any) => {
                sceneModel.createMesh({
                    id: cfg.id,
                    primitive: cfg.primitive,
                    positions: cfg.positions,
                    color: convertColorTo01(cfg.color),
                    normals: cfg.normals,
                    origin: cfg.origin,
                    edgeIndices: cfg.edgeIndices,
                    indices: cfg.indices,
                    uv: cfg.uv || [],
                })
            })
            console.timeEnd('createMesh');

            console.time('createEntity');
            model._entityConfigs.forEach((cfg: any) => {
                sceneModel.createEntity({
                    id: cfg.id,
                    meshIds: cfg.meshIds,
                    isObject: cfg.isObject,
                })
            });
            console.timeEnd('createEntity');

            sceneModel.finalize();
        }


    }

}

const convertColorTo01 = (color: any): number[] => {
    if (!color) return [];
    if (Array.isArray(color)) {
        // Nếu đã là mảng số thực (0..1) thì trả về luôn
        if (color.length && typeof color[0] === 'number' && color[0] <= 1) return color;
        return color.map((v: number) => v / 255);
    }
    if (color instanceof Uint8Array) {
        return Array.from(color).map((v: number) => v / 255);
    }
    return color;
}