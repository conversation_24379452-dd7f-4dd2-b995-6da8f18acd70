.dpu-dialog-popup-setting {
  display: block;
  position: absolute;

  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);

  /* Black w/ opacity */
}

.dpu-dialog-popup-setting.not-modal {
  background-color: unset;
  /* position: unset; */
  pointer-events: none;
}
/* Modal Content */
.dpu-dialog-content-popup {
  font-size: 14px;
  position: absolute;
  pointer-events: auto;
  /* top: 50%; */
  /* left: 50%; */
  
  background-color: var(--background-color-1);
  /* margin: auto; */
  padding: 0;
 color:var(--color-text-1) ;
  width: 400px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  user-select: none;
  border-radius: 4px;
}

/* The Close Button */
.dpu-dialog-close {
  font-size: 16px;
  font-weight: bold;
  color: var(--color-icon-1);
  cursor: pointer;
}

.dpu-dialog-header {
  padding: 5px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color-1);
}

.dpu-dialog-header.with-cursor {
  cursor: all-scroll;
}

.dpu-dialog-body {
  height: calc(100% - 55px);
  overflow: auto;
}
.dpu-dialog-footer {

  padding: 0px 5px 0px 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--border-color-1);
}

.dpu-dialog-resize {
  height: 12px;
  width: 12px;
  float: right;
  cursor: nw-resize;
  margin: 0px 2px 2px 0;
}
.dpu-dialog-resize svg {
  fill: var(--color-icon-1);
}
