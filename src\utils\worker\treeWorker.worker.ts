// treeWorker.worker.ts
self.onmessage = function (event) {
  const { type, objectId, listParentNode } = event.data;
  if (type === "expandNodes") {
    // Xử lý logic mở rộng node cha: tr<PERSON> về tất cả key của listParentNode
    const expandedKeys = Array.isArray(listParentNode)
      ? listParentNode.map(([key]) => key)
      : [];
    self.postMessage({ type: "expandNodesResult", expandedKeys });
  }
  // ... các tác vụ kh<PERSON>c
};
export default null;