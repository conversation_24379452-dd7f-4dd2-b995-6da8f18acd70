import GlobalStore from "../../globalStore/globalStore";
import { ToolbarSetting } from "../dialog/setting/toolbarSetting";
import { Toolbar } from "./toolbar";

export class Setting {
    private _store = GlobalStore.getInstance().getAll();
    private _toolbar: Toolbar;
    private _settingToolbar: ToolbarSetting | undefined;

    constructor(toolbar: Toolbar) {
        this._toolbar = toolbar;

        this._settingToolbar = new ToolbarSetting();
    }

    //#region settingAction
    private _activeBtnSetting = false
    settingAction = (idElement: string) => {
        this._activeBtnSetting = !this._activeBtnSetting
        if (!this._activeBtnSetting) {
            this._toolbar.activeBtn(idElement, false);
            this._settingToolbar?.showHideModalSetting(false);
        } else {
            this._toolbar.activeBtn(idElement, true);
            this._settingToolbar?.showHideModalSetting(true);
        }

        this._settingToolbar?.setClosedCallback((status: boolean) => {
            if (!status) {
                this._toolbar.activeBtn(idElement, false)
                this._activeBtnSetting = false;
            }
        });
    }

}