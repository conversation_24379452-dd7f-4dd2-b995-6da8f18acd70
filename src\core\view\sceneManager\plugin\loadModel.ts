import { OBJLoaderPlugin, STLLoaderPlugin, <PERSON>er, XKTLoaderPlugin } from "@xeokit/xeokit-sdk";
import GlobalStore, { StoreConfig } from "../../../globalStore/globalStore";
import { CustomizedGLTFLoaderPlugin } from "./CustomizedGLTFLoaderPlugin";
import { GLTFLoaderPlugin } from "./customGLTFLoaderPluginv2/GLTFLoaderPlugin";




/**
 * Model config
 */
export interface ModelConfig {
    id?: string;
    name?: string;
    src?: string; // url of the model
    position?: number[];
    rotation?: number[];
    scale?: number[];
    edges?: boolean; // if we want to generate and show edges to the modle
    visible?: boolean; // default value is true. won't load a model when invisible
    performance?: boolean; // Whether to use performance mode. Default value is true.
    metaModelSrc?: string; // url of the meta model
    format?: string; // format of the model
    autoMetaModel?: boolean; // Turn it on and get TreeView
    rotationMatrix?: number[]; // The rotation matrix to rotate the model
}

export class ModelLoader {
    private _globalStore: GlobalStore | undefined
    private _store: StoreConfig | undefined
    private _viewer: Viewer | undefined;
    constructor(store: GlobalStore) {
        this._globalStore = store
        this._store = store.getAll()
        this._viewer = this._store.viewer;
    }
    // eslint-disable-next-line
    private _loaders: {
        [format: string]: { constructorFunc: any; loader?: any; is2d?: boolean };
    } = {
            // gltf: { constructorFunc: CustomizedGLTFLoaderPlugin },
            gltf: { constructorFunc: GLTFLoaderPlugin },
            obj: { constructorFunc: OBJLoaderPlugin },
            xkt: { constructorFunc: XKTLoaderPlugin },
            stl: { constructorFunc: STLLoaderPlugin },
            dxf: { constructorFunc: XKTLoaderPlugin, is2d: true },
            // glb: { constructorFunc: GLTFLoaderPlugin },
            glb: { constructorFunc: GLTFLoaderPlugin },
        };

    loadModel = async (
        modleCfg: ModelConfig,
        okFunc?: (model: any) => void,
        isFirstLoad?: boolean | true
    ) => {

        let format: string = "glb";
        let config: any = {
            id: modleCfg.id,
            name: modleCfg.name,
            src: modleCfg.src,
            metaModelSrc: modleCfg.metaModelSrc,
            edges: true,
            performance: modleCfg.performance !== false,
            position: modleCfg.position || [0, 0, 0],
            scale: modleCfg.scale || [1, 1, 1],
            rotation: modleCfg.rotation || [0, 0, 0],
            autoMetaModel: modleCfg.autoMetaModel || false,
        };


        const src = config.src;
        if (!config.src) {
            console.error(`[Viewer] Invalid "src"!`);
            return;
        }

        const loader = this.getLoader(format);


        if (!loader) {
            console.error(`[Viewer] Failed to get loader for ${config.src}`);
            return;
        }

        // if (this.is2d(format) && FontManager.instance().isEmpty()) {
        //     console.error("[Viewer] Dxf files need fonts");
        // }

        if (!config.id) {
            const id = this.getUniqulModelId(config.name);
            config.id = id;


        }

        console.log(`[Viewer] Loading model id "${config.id}"...`);
        const startTime = Date.now();

        const m = loader.load({
            id: config.id,
            src: config.src,
            metaModelSrc: config.metaModelSrc,
            position: config.position,
            scale: config.scale,
            rotation: config.rotation,
            edges: config.edges !== false,
            performance: modleCfg.performance !== false,
            autoMetaModel: config.autoMetaModel,
        });
        m.on("loaded", () => {
            console.log(`[Viewer] Loaded "${config.id}" in ${(Date.now() - startTime) / 1000}s`);

            // this._store.treeLayerModel?.mapNewList()
            // Re-apply transform to ensure it sticks after loader finalization
            try {
                if (Array.isArray(config.position)) {
                    (m as any).position = config.position;
                }
                if (Array.isArray(config.rotation)) {
                    (m as any).rotation = config.rotation;
                }
                if (Array.isArray(config.scale)) {
                    (m as any).scale = config.scale;
                }
                if (Array.isArray(config.origin)) {
                    (m as any).origin = config.origin;
                }
            } catch (e) {
                console.warn("[Viewer] Failed to re-apply model transform:", e);
            }

            m.edges = true;//when loaded, set edges to true
            console.debug(`[Viewer] Model ${config.id} transform:`, {
                position: (m as any).position,
                rotation: (m as any).rotation,
                scale: (m as any).scale,
                origin: (m as any).origin,
            });
            okFunc && okFunc(m);
        });



    }
    //#region getLoader
    /**
     * Gets a loader by given file format
     */
    private getLoader(format: any) {
        format = format.toLowerCase();
        const obj = this._loaders[format];
        if (!obj) {
            return undefined;
        }
        if (!obj.loader && obj.constructorFunc) {
            obj.loader = new obj.constructorFunc(this._viewer);
        }
        return obj.loader;
    }


    private getUniqulModelId(prefix?: string): string {
        const DEFAULT_PREFIX = "model_id";
        const modelIds = this._viewer?.scene.modelIds;
        let newId = prefix || `${DEFAULT_PREFIX}_1`;
        let i = 1;
        while (modelIds?.find((id: string) => id === newId)) {
            newId = prefix ? `${prefix}_${i}` : `${DEFAULT_PREFIX}_${i}`;
            i++;
        }
        return newId;
    }




}



