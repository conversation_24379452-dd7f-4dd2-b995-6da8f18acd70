import { createUUID } from "../../../utils/utils";

//#enum enums
export enum ToolTipArrowType {
    Top = "dynamic-tooltip-arrow-top",
    Bottom = "dynamic-tooltip-arrow-bottom",
    Left = "dynamic-tooltip-arrow-left",
    Right = "dynamic-tooltip-arrow-right",
    TopLeft = "dynamic-tooltip-arrow-topLeft",
    TopRight = "dynamic-tooltip-arrow-topRight",
    BottomLeft = "dynamic-tooltip-arrow-bottomLeft",
    BottomRight = "dynamic-tooltip-arrow-bottomRight"
}

export interface IPropertiesToolTipButton {
    containerViewId: string,
    htmlElement: HTMLElement,
    toolTip?: string,
    delayShow?: number

}

export class DynamicToolTip {
    private _input: IPropertiesToolTipButton
    private tooltip: HTMLDivElement | null = null;
    private currentTooltipTarget: HTMLElement | null = null; // Phần tử hiện đang hiển thị tooltip
    private timeoutId: number | null = null; // ID của timeout
    private IdGenneralTooltip = createUUID()
    constructor(input: IPropertiesToolTipButton) {
        this._input = input

        this._createToolTip()
    }

    private _createToolTip = () => {
        const btn = this._input.htmlElement;
        btn.setAttribute('data-tooltip', this._input.toolTip ?? "");

        // Chỉ gắn event nếu chưa từng gắn
        if (!btn.hasAttribute('data-tooltip-initialized')) {
            btn.addEventListener('mouseenter', (event) => this._handleMouseEnter(event, this._input.delayShow ?? 1000));
            btn.addEventListener('mouseleave', this._handleMouseLeave);
            btn.setAttribute('data-tooltip-initialized', 'true');
        }
    }

    // Hàm xử lý khi di chuột vào phần tử có `data-tooltip`
    private _handleMouseEnter = (event: MouseEvent, delay: number) => {
        let target = event.target as HTMLElement;

        // Duyệt tìm thẻ cha nếu thẻ con không chứa `data-tooltip`
        while (target && !target.hasAttribute('data-tooltip')) {
            target = target.parentElement as HTMLElement;
        }

        // Kiểm tra nếu tìm thấy thẻ cha có `data-tooltip` và nó không phải là phần tử hiện tại
        if (target && target !== this.currentTooltipTarget) {
            this.currentTooltipTarget = target;

            if (this.tooltip) {
                this.hideTooltip();
            }
            // Thêm độ trễ 1 giây trước khi hiển thị tooltip
            if (this.timeoutId) {
                clearTimeout(this.timeoutId); // Hủy timeout cũ nếu tồn tại
            }

            // Thêm độ trễ 1 giây trước khi hiển thị tooltip
            this.timeoutId = window.setTimeout(() => {
                this.showTooltip(target);
            }, delay); // Độ trễ 1000ms
        }
    }

    // Hàm xóa tooltip khỏi DOM
    hideTooltip = () => {
        // Tìm tất cả các phần tử có id là 'dynamic-tooltip'
        const tooltips = document.querySelectorAll(`#dynamic-tooltip-${this.IdGenneralTooltip}`);

        // Xóa từng tooltip nếu tồn tại trong DOM
        tooltips.forEach((tooltip) => {
            tooltip.parentNode?.removeChild(tooltip);
        });

        // Đặt lại giá trị của tooltip và các biến liên quan
        this.tooltip = null;
        this.currentTooltipTarget = null; // Xóa tham chiếu đến phần tử hiện tại

        if (this.timeoutId) {
            clearTimeout(this.timeoutId); // Hủy thời gian chờ nếu có
            this.timeoutId = null;
        }
    }


    // Hàm tạo tooltip với nội dung và vị trí
    showTooltip = (target: HTMLElement) => {
        const tooltipText = target.getAttribute('data-tooltip');
        const toolTipArrowPosition = target.getAttribute('tooltip-arrow-position');

        if (!tooltipText || !toolTipArrowPosition) return;
        const containerId = document.querySelector(`#${this._input.containerViewId}`) as HTMLElement
        // Xóa tooltip cũ nếu còn tồn tại
        const oldTooltip = containerId.querySelector(`#dynamic-tooltip-${this.IdGenneralTooltip}`);
        if (oldTooltip) {
            oldTooltip.parentNode?.removeChild(oldTooltip);
        }
        // // Tạo tooltip mới
        this.tooltip = document.createElement('div');
        this.tooltip.className = `dynamic-tooltip ${toolTipArrowPosition}`;
        this.tooltip.id = `dynamic-tooltip-${this.IdGenneralTooltip}`;
        this.tooltip.innerText = tooltipText;

        // Thêm tooltip vào `body`
        containerId!.appendChild(this.tooltip);
        const topOffset = this._getOffsetTopRelativeToParent(target, containerId);
        // // Lấy vị trí và kích thước của phần tử mục tiêu
        const rect = target.getBoundingClientRect();
        const tooltipWidth = this.tooltip.offsetWidth;


        switch (toolTipArrowPosition) {
            case ToolTipArrowType.Right:
                this.tooltip.style.left = `${rect.left - tooltipWidth - 10}px`;
                this.tooltip.style.top = `${topOffset + target.offsetHeight / 2 - this.tooltip.offsetHeight / 2}px`;
                break;
            case ToolTipArrowType.Left:
                this.tooltip.style.left = `${rect.right + 10}px`;
                this.tooltip.style.top = `${topOffset + target.offsetHeight / 2 - this.tooltip.offsetHeight / 2}px`;
                break;
            case ToolTipArrowType.Bottom:
                this.tooltip.style.left = `${rect.left + rect.width / 2 - this.tooltip.offsetWidth / 2}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop - rect.height - 10}px`;
                break;

            case ToolTipArrowType.Top:
                this.tooltip.style.left = `${rect.left + rect.width / 2 - this.tooltip.offsetWidth / 2}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop + rect.height + 10}px`;
                break;
            case ToolTipArrowType.TopLeft:
                this.tooltip.style.left = `${rect.right - this.tooltip.offsetWidth}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop + rect.height + 10}px`;
                break;
            case ToolTipArrowType.TopRight:
                this.tooltip.style.left = `${rect.left}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop + rect.height + 10}px`;
                break;
            case ToolTipArrowType.BottomLeft:
                this.tooltip.style.left = `${rect.right - this.tooltip.offsetWidth}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop - rect.height - 10}px`;
                break;
            case ToolTipArrowType.BottomRight:
                this.tooltip.style.left = `${rect.left}px`;
                this.tooltip.style.top = `${topOffset + target.offsetTop - rect.height - 10}px`;
                break;
            default:
                break;
        }




    }

    private _getOffsetTopRelativeToParent(child: HTMLElement, parent: HTMLElement): number {
        let offset = 0;
        let el: HTMLElement | null = child;
        while (el && el !== parent) {
            offset += el.offsetTop;
            el = el.offsetParent as HTMLElement;
        }
        return offset;
    }


    // Hàm xử lý khi di chuột ra khỏi phần tử có `data-tooltip`
    private _handleMouseLeave = (event: any) => {
        const target = event.target;

        if (target && target.hasAttribute('data-tooltip') || this.tooltip) {
            if (this.timeoutId) {
                clearTimeout(this.timeoutId); // Hủy timeout nếu rời chuột trước khi tooltip hiển thị
                this.timeoutId = null;
            }
            this.hideTooltip(); // Gọi hàm để xóa tooltip ngay lập tức khi rời chuột
        }
    };


}