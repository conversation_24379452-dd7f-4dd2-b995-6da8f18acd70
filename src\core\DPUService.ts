import { DPUServiceConfig } from "../types/DPUService/DPUService";
import GlobalStore from "./globalStore/globalStore";
import { DPUViewer } from "./view/DPUViewer";

export type ServiceConfig = {
    containerId: string;
    token: string;
}

export class DPUService {

    initialize(service: ServiceConfig) {
        return this._initCheckToken(service.containerId, service.token)
    }
    private _initCheckToken = async (idViewer: string, token: string) => {
        const containerView = document.querySelector(`#${idViewer}`);
        if (!containerView) {
            console.error("Container view not found");
            return;
        }

        const isValidToken = await this._checkToken(idViewer, token);
        if (isValidToken) {
            GlobalStore.init({
                idViewer: idViewer,
            })
            GlobalStore.getInstance().set("token", token);
            const dpuView = new DPUViewer(containerView, token);
            return dpuView; // T<PERSON><PERSON> về dpuView nếu token hợp lệ
        } else {
            console.error("Token không hợp lệ");
            return null; // Trả về null nếu token không hợp lệ
        }

    };


    private _checkToken = async (containerId: string, token: string) => {
        const apiUrl = `https://translateservicedev.corebim.com/authApi/System/ValidateToken`;

        try {
            const response = await fetch(apiUrl, {
                method: "GET",
                headers: {
                    "accept": "*/*",
                    "Authorization": `Bearer ${token}`,
                },
            });
            return response.ok;
        } catch (error) {
            // const validToken = new ValidToken(containerId)
            console.error("Lỗi khi kiểm tra token:", error);
            return false;
        }
    }

}


// export class DPUService {

//     initialize(service: ServiceConfig) {
//         return this._initCheckToken(service.containerId, service.token)
//     }
//     private _initCheckToken = async (idViewer: string, token: string) => {
//         const containerView = document.querySelector(`#${idViewer}`);
//         if (!containerView) {
//             console.error("Container view not found");
//             return;
//         }

//         const isValidToken = true
//         if (isValidToken) {
//             GlobalStore.init({
//                 idViewer: idViewer,
//             })
//             GlobalStore.getInstance().set("token", token);
//             const dpuView = new DPUViewer(containerView, token);
//             return dpuView; // Trả về dpuView nếu token hợp lệ
//         } else {
//             console.error("Token không hợp lệ");
//             return null; // Trả về null nếu token không hợp lệ
//         }

//     };



// }