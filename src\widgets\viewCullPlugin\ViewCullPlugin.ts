
import {Frustum, setFrustum, Plugin, math, frustumIntersectsAABB3 } from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";

import { getObjectCullStates } from "@xeokit/xeokit-sdk/src/plugins/lib/culling/ObjectCullStates.js";
const MAX_KD_TREE_DEPTH = 8; // Increase if greater precision needed

const kdTreeDimLength: Float32Array = new Float32Array(3);

/**
 * {@link Viewer} plugin that performs view frustum culling to accelerate rendering performance.
 *
 * For each {@link Entity} that represents an object, ````ViewCullPlugin```` will automatically
 * set {@link Entity#culled}````false```` whenever it falls outside our field of view.
 *
 * When culled, an ````Entity```` is not processed by xeokit's renderer.
 *
 * Internally, ````ViewCullPlugin```` organizes {@link Entity}s in
 * a [bounding volume hierarchy](https://en.wikipedia.org/wiki/Bounding_volume_hierarchy), implemented as
 * a [kd-tree](https://en.wikipedia.org/wiki/K-d_tree).
 *
 * On each {@link Scene} "tick" event, ````ViewCullPlugin```` searches the kd-tree using a frustum generated from
 * the {@link Camera}, marking each ````Entity```` **culled** if it falls outside the frustum.
 *
 * Use ````ViewCullPlugin```` by simply adding it to your ````Viewer````:
 *
 * ````javascript
 * const viewer = new Viewer({
 *    canvasId: "myCanvas",
 *    transparent: true
 * });
 *
 * const viewCullPlugin = new ViewCullPlugin(viewer, {
 *    maxTreeDepth: 20
 * });
 *
 * const xktLoader = new XKTLoaderPlugin(viewer);
 *
 * const model = xktLoader.load({
 *      id: "myModel",
 *      src: "./models/xkt/OTCConferenceCenter.xkt"
 * });
 * ````
 */
interface KDNode {
    aabb: number[];
    intersection: number;
    intersects?: number;
    left?: KDNode;
    right?: KDNode;
    objects?: number[];
}

interface ModelInfo {
    model: any;
    onDestroyed: any;
}

class ViewCullPlugin extends Plugin {
    private _objectCullStates: any;
    private _maxTreeDepth: number;
    private _modelInfos: { [id: string]: ModelInfo };
    private _frustum: any;
    private _kdRoot: KDNode | null;
    private _frustumDirty: boolean;
    private _kdTreeDirty: boolean;
    private _onViewMatrix: any;
    private _onProjMatrix: any;
    private _onModelLoaded: any;
    private _onSceneTick: any;
    private _enabled: boolean = true;
    public viewer: any;

    constructor(viewer: any, cfg: { id?: string; maxTreeDepth?: number } = {}) {
        super(cfg.id || "ViewCull", viewer);
        this.viewer = viewer;
        this._objectCullStates = getObjectCullStates(viewer.scene);
        this._maxTreeDepth = cfg.maxTreeDepth || MAX_KD_TREE_DEPTH;
        this._modelInfos = {};
        this._frustum = new Frustum();
        this._kdRoot = null;
        this._frustumDirty = false;
        this._kdTreeDirty = false;
        this._onViewMatrix = viewer.scene.camera.on("viewMatrix", () => {
            this._frustumDirty = true;
        });
        this._onProjMatrix = viewer.scene.camera.on("projMatMatrix", () => {
            this._frustumDirty = true;
        });
        this._onModelLoaded = viewer.scene.on("modelLoaded", (modelId: string) => {
            const model = this.viewer.scene.models[modelId];
            if (model) {
                this._addModel(model);
            }
        });
        this._onSceneTick = viewer.scene.on("tick", () => {
            this._doCull();
        });
    }

    set enabled(enabled: boolean) {
        this._enabled = enabled;
    }

    get enabled(): boolean {
        return this._enabled;
    }

    private _addModel(model: any) {
        const modelInfo: ModelInfo = {
            model: model,
            onDestroyed: model.on("destroyed", () => {
                this._removeModel(model);
            })
        };
        this._modelInfos[model.id] = modelInfo;
        this._kdTreeDirty = true;
    }

    private _removeModel(model: any) {
        const modelInfo = this._modelInfos[model.id];
        if (modelInfo) {
            modelInfo.model.off(modelInfo.onDestroyed);
            delete this._modelInfos[model.id];
            this._kdTreeDirty = true;
        }
    }

    private _doCull() {
        const cullDirty = (this._frustumDirty || this._kdTreeDirty);
        if (this._frustumDirty) {
            this._buildFrustum();
        }
        if (this._kdTreeDirty) {
            this._buildKDTree();
        }
        if (cullDirty) {
            const kdNode = this._kdRoot;
            if (kdNode) {
                // console.log('kdNode',kdNode);
                this._visitKDNode(kdNode);

            }
        }
    }

    private _buildFrustum() {
        const camera = this.viewer.scene.camera;
        setFrustum(this._frustum, camera.viewMatrix, camera.projMatrix);
        this._frustumDirty = false;
    }

    private _buildKDTree() {
        const viewer = this.viewer;
        const scene = viewer.scene;
        const depth = 0;
        this._kdRoot = {
            aabb: scene.getAABB(),
            intersection: Frustum.INTERSECT
        };
        // console.log('this._objectCullStates.numObjects',this._objectCullStates.numObjects);
        for (let objectIdx = 0, len = this._objectCullStates.numObjects; objectIdx < len; objectIdx++) {
            const entity = this._objectCullStates.objects[objectIdx];
            this._insertEntityIntoKDTree(this._kdRoot, entity, objectIdx, depth + 1);
        }
        this._kdTreeDirty = false;
    }

    private _insertEntityIntoKDTree(kdNode: KDNode, entity: any, objectIdx: number, depth: number) {
        
        const entityAABB = entity.aabb;
        // @ts-ignore: math is likely a global or imported elsewhere
        if (depth >= this._maxTreeDepth) {
            kdNode.objects = kdNode.objects || [];
            kdNode.objects.push(objectIdx);
            math.expandAABB3(kdNode.aabb, entityAABB);
            return;
        }
        if (kdNode.left) {
            if (math.containsAABB3(kdNode.left.aabb, entityAABB)) {
                this._insertEntityIntoKDTree(kdNode.left, entity, objectIdx, depth + 1);
                return;
            }
        }
        if (kdNode.right) {
            if (math.containsAABB3(kdNode.right.aabb, entityAABB)) {
                this._insertEntityIntoKDTree(kdNode.right, entity, objectIdx, depth + 1);
                return;
            }
        }
        const nodeAABB = kdNode.aabb;
        kdTreeDimLength[0] = nodeAABB[3] - nodeAABB[0];
        kdTreeDimLength[1] = nodeAABB[4] - nodeAABB[1];
        kdTreeDimLength[2] = nodeAABB[5] - nodeAABB[2];
        let dim = 0;
        if (kdTreeDimLength[1] > kdTreeDimLength[dim]) {
            dim = 1;
        }
        if (kdTreeDimLength[2] > kdTreeDimLength[dim]) {
            dim = 2;
        }
        if (!kdNode.left) {
            const aabbLeft = nodeAABB.slice();
            aabbLeft[dim + 3] = ((nodeAABB[dim] + nodeAABB[dim + 3]) / 2.0);
            kdNode.left = {
                aabb: aabbLeft,
                intersection: Frustum.INTERSECT
            };
            if (math.containsAABB3(aabbLeft, entityAABB)) {
                this._insertEntityIntoKDTree(kdNode.left, entity, objectIdx, depth + 1);
                return;
            }
        }
        if (!kdNode.right) {
            const aabbRight = nodeAABB.slice();
            aabbRight[dim] = ((nodeAABB[dim] + nodeAABB[dim + 3]) / 2.0);
            kdNode.right = {
                aabb: aabbRight,
                intersection: Frustum.INTERSECT
            };
            if (math.containsAABB3(aabbRight, entityAABB)) {
                this._insertEntityIntoKDTree(kdNode.right, entity, objectIdx, depth + 1);
                return;
            }
        }
        kdNode.objects = kdNode.objects || [];
        kdNode.objects.push(objectIdx);
        math.expandAABB3(kdNode.aabb, entityAABB);
    }

    private _visitKDNode(kdNode: KDNode, intersects: number = Frustum.INTERSECT) {

        if (intersects !== Frustum.INTERSECT && kdNode.intersects === intersects) {
            return;
        }
        if (intersects === Frustum.INTERSECT) {
            intersects = frustumIntersectsAABB3(this._frustum, kdNode.aabb);
            kdNode.intersects = intersects;
        }
        const culled = (intersects === Frustum.OUTSIDE);
        const objects = kdNode.objects;
        if (objects && objects.length > 0) {
            for (let i = 0, len = objects.length; i < len; i++) {
                const objectIdx = objects[i];
                this._objectCullStates.setObjectViewCulled(objectIdx, culled);
            }
        }
        if (kdNode.left) {
            this._visitKDNode(kdNode.left, intersects);
        }
        if (kdNode.right) {
            this._visitKDNode(kdNode.right, intersects);
        }
    }

    /**
     * @private
     */
    send(name: string, value: any) {
        // No-op
    }

    destroy() {
        // If Plugin has destroy, call it
        if (typeof super.destroy === "function") {
            super.destroy();
        }
        this._clear();
        const scene = this.viewer.scene;
        const camera = scene.camera;
        scene.off(this._onModelLoaded);
        scene.off(this._onSceneTick);
        camera.off(this._onViewMatrix);
        camera.off(this._onProjMatrix);
    }

    private _clear() {
        for (let modelId in this._modelInfos) {
            const modelInfo = this._modelInfos[modelId];
            modelInfo.model.off(modelInfo.onDestroyed);
        }
        this._modelInfos = {};
        this._kdRoot = null;
        this._kdTreeDirty = true;
    }
}

export { ViewCullPlugin };
