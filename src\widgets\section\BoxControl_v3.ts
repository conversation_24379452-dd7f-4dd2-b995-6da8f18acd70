/* eslint-disable @typescript-eslint/no-explicit-any */
import { addPrefix, CommonUtils } from "../../utils/CommonUtils";
import { GeometryUtils } from "../../utils/GeometryUtils";
import { SECTION_BOX_ID } from "../../utils/Consts";
import './section-controls.css'
import {
    buildCylinderGeometry,
    buildTorusGeometry,
    EdgeMaterial,
    EmphasisMaterial,
    math,
    Mesh,
    Node,
    PhongMaterial,
    ReadableGeometry,
} from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";
import GlobalStore from "../../core/globalStore/globalStore";

/**
 * Left/Right: x direction
 * Top/Bottom: y direction
 * Front/Back: z direction
 */
export enum BoxSectionPlaneType {
    LEFT = "Left",
    RIGHT = "Right",
    TOP = "Top",
    BOTTOM = "Bottom",
    FRONT = "Front",
    BACK = "Back",
}

const DRAG_ACTIONS = {
    none: -1,
    xTranslate: 0,
    yTranslate: 1,
    zTranslate: 2,
    xRotate: 3,
    yRotate: 4,
    zRotate: 5,
};

interface PickedComponent {
    mesh: any; // local coordinate. Transforms to world coordinates through the rootNode.
    sectionPlane?: any; // world coordinate.
}

const zeroVec = new Float64Array([0, 0, 1]);
const quat = new Float64Array(4);
const NO_STATE_INHERIT = false;
const EPSILON2 = 0.01;
const SCALE_RADIO = 0.005;
const ROTATION_SCALE_RADIO = 0.6;
const MIN_WIDTH = 0.5;
const DRAG_ROTATION_RATE = 135;
const GEOMETRY_RADIUS = 0.5; // Geometric default initial size
const DEFAULT_COLOR = [0.0, 0.855, 0.718]; // Default colors of all Mesh
const AXIS_SUFFIX = "_Axis";
const ARROW_SUFFIX = "_Arrow";
const CURVE_TYPE = "yCurve";
const HANDLE_TYPE = "Handle";

export class BoxControl {
    private _viewer: any;
    private _visible = false;
    private _rootNode: any; // Root of Node graph that represents this control in the 3D scene
    private _meshes: Map<string, any> = new Map(); // Meshes that are always visible
    private _hideableMeshes: Map<string, any> = new Map(); // Meshes displayed momentarily for affordance
    private _originAABB: number[] = [];
    private _currentAABB: number[] = []; // aabb without rotation
    private _sectionPlaneMap = new Map<BoxSectionPlaneType, any>();

    private _sceneSubIds: number[] = []; // event subscription ids of the scene class, used to un-register events
    private _cameraControlSubIds: number[] = []; // event subscription ids of the cameraControl class, used to un-register events
    private _inputSubIds: number[] = []; // event subscription ids of the input class, used to un-register events
    private _store = GlobalStore.getInstance().getAll();


    private _elementVieweer = this._store.containerCanvas
    private _htmlControls: Map<BoxSectionPlaneType, HTMLElement> = new Map();
    constructor(owner: any) {
        this._viewer = owner.viewer;

        this.createNodes();
        this.bindEvents();
    }

    private worldToScreen = (worldPos: number[]): number[] | null => {
        const camera = this._store?.viewer!.camera;
        const canvas = this._store?.canvas!;

        // Combine matrices như xeokit làm
        const pvMatrix = math.mat4();
        math.mulMat4(camera.projMatrix, camera.viewMatrix, pvMatrix);

        // Transform world to clip space
        const clipPos = math.vec4([worldPos[0], worldPos[1], worldPos[2], 1.0]);
        math.transformVec4(pvMatrix, clipPos, clipPos);

        // Perspective divide
        if (Math.abs(clipPos[3]) < 0.0001) return null;

        const ndcX = clipPos[0] / clipPos[3];
        const ndcY = clipPos[1] / clipPos[3];
        const ndcZ = clipPos[2] / clipPos[3];

        // Validate NDC coordinates
        // if (ndcZ < -1 || ndcZ > 1 || Math.abs(ndcX) > 1 || Math.abs(ndcY) > 1) {
        //     return null;
        // }

        // Convert to screen coordinates
        const screenX = (ndcX + 1) * 0.5 * canvas.clientWidth;
        const screenY = (1 - ndcY) * 0.5 * canvas.clientHeight;

        return [screenX, screenY];
    }

    /**
     * Create HTML controls for all 6 section planes
     */
    private createAllHtmlControls(): void {
        if (!this._elementVieweer) {
            console.warn('Container canvas element not found');
            return;
        }

        // Create controls for all 6 section plane types
        Object.values(BoxSectionPlaneType).forEach((type) => {
            this.createHtmlControl(type);
        });
    }

    /**
     * Create HTML control for specific section plane
     */
    private createHtmlControl(type: BoxSectionPlaneType): void {
        if (!this._elementVieweer) return;

        // Create circular div element
        const controlDiv = document.createElement('div');
        controlDiv.id = `section-control-${type.toLowerCase()}`;
        controlDiv.className = 'section-plane-control';
        controlDiv.setAttribute('data-plane-type', type);

        // Add drag functionality
        this.setupHtmlControlDragging(controlDiv, type);

        // Add to container
        this._elementVieweer.appendChild(controlDiv);
        this._htmlControls.set(type, controlDiv);
    }

    /**
     * Setup dragging functionality for HTML control
     */
    private setupHtmlControlDragging(element: HTMLElement, type: BoxSectionPlaneType): void {
        let isDragging = false;
        let startX = 0;
        let startY = 0;

        const onMouseDown = (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            // Disable camera control
            this._viewer.cameraControl.pointerEnabled = false;
            element.style.cursor = 'grabbing';

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        };

        const onMouseMove = (e: MouseEvent) => {
            if (!isDragging) return;

            e.preventDefault();
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            // Handle section plane movement
            this.handleHtmlControlDrag(type, deltaX, deltaY);

            startX = e.clientX;
            startY = e.clientY;
        };

        const onMouseUp = (e: MouseEvent) => {
            e.preventDefault();
            isDragging = false;

            // Re-enable camera control
            this._viewer.cameraControl.pointerEnabled = true;
            element.style.cursor = 'pointer';
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            this._lastCanvasPosition = [deltaX, deltaY];
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        };

        element.addEventListener('mousedown', onMouseDown);
    }
    private _lastCanvasPosition: [number, number] | null = null;

    /**
     * Handle dragging of HTML control to move section plane
     */
    private handleHtmlControlDrag(type: BoxSectionPlaneType, deltaX: number, deltaY: number): void {
        let dragAction = DRAG_ACTIONS.none;
        const sectionPlane = this._sectionPlaneMap.get(type);



        if (!sectionPlane) return;

        const camera = this._viewer.camera;

        // Get camera vectors for proper world space movement
        const viewMatrix = camera.viewMatrix;
        const rightVec = [viewMatrix[0], viewMatrix[4], viewMatrix[8]]; // Camera right vector
        const upVec = [viewMatrix[1], viewMatrix[5], viewMatrix[9]];    // Camera up vector

        // Calculate dynamic sensitivity based on camera distance
        const cameraPos = camera.eye;
        const planePos = sectionPlane.pos;
        const distance = math.lenVec3(math.subVec3(cameraPos, planePos, math.vec3()));

        // Dynamic sensitivity: closer = more precise, farther = faster movement
        const baseSensitivity = 0.1; // Reduced sensitivity for better control
        const distanceScale = Math.max(0.1, Math.min(2, distance / 100)); // Better scaling range
        const sensitivity = baseSensitivity * distanceScale;

        const screenMovement = [
            deltaX * sensitivity,
            -deltaY * sensitivity  // Invert Y for correct direction
        ];

        // Convert screen movement to world movement
        const worldMovement = [
            rightVec[0] * screenMovement[0] + upVec[0] * screenMovement[1],
            rightVec[1] * screenMovement[0] + upVec[1] * screenMovement[1],
            rightVec[2] * screenMovement[0] + upVec[2] * screenMovement[1]
        ];

        // Get section plane direction (normal) - this is already in world coordinates
        const planeDir = sectionPlane.dir;

        // Project world movement onto plane normal to get movement along plane
        const movementAlongNormal = math.dotVec3(worldMovement, planeDir);

        // Apply movement along the plane's normal direction
        const worldDelta = [
            planeDir[0] * movementAlongNormal,
            planeDir[1] * movementAlongNormal,
            planeDir[2] * movementAlongNormal
        ];

        // Update section plane position
        const newPos = [
            sectionPlane.pos[0] + worldDelta[0],
            sectionPlane.pos[1] + worldDelta[1],
            sectionPlane.pos[2] + worldDelta[2]
        ];

        sectionPlane.pos = newPos;

        // Check if we have rotation
        const hasRotation = Math.abs(this._rootNode.quaternion[0]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[1]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[2]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[3] - 1) > 0.001;


        console.log('sectionPlane:', sectionPlane);


        const xBaseAxis = math.vec3([1, 0, 0]);
        const yBaseAxis = math.vec3([0, 1, 0]);
        const zBaseAxis = math.vec3([0, 0, 1]);
        if (!this._lastCanvasPosition) {
            this._lastCanvasPosition = [deltaX, deltaY];
        }
        // const lastCanvasPos = [deltaX, deltaY];
        console.log(type);
        switch (type) {
            case BoxSectionPlaneType.BOTTOM:
            case BoxSectionPlaneType.TOP:
                // Handle translation
                dragAction = DRAG_ACTIONS.zTranslate;
                dragAction = DRAG_ACTIONS.xTranslate;
                this._dragTranslateSectionPlane(zBaseAxis, this._lastCanvasPosition as number[], [deltaX, deltaY], dragAction, sectionPlane, type);

                break;
            case BoxSectionPlaneType.LEFT:
            case BoxSectionPlaneType.RIGHT:
                dragAction = DRAG_ACTIONS.xTranslate;
                this._dragTranslateSectionPlane(xBaseAxis, this._lastCanvasPosition as number[], [deltaX, deltaY], dragAction, sectionPlane, type);

                break;
            case BoxSectionPlaneType.FRONT:
            case BoxSectionPlaneType.BACK:
                dragAction = DRAG_ACTIONS.yTranslate;
                this._dragTranslateSectionPlane(yBaseAxis, this._lastCanvasPosition as number[], [deltaX, deltaY], dragAction, sectionPlane, type);
                break;
        }
        console.log('dragAction:', dragAction);


        // if (hasRotation) {
        //     // When rotated, recalculate AABB from all section plane positions
        //     // This ensures all meshes form a complete box
        //     this.recalculateAABBFromRotatedSectionPlanes();

        //     // Rebuild all meshes to reflect the new box dimensions
        //     // HTML controls will be automatically updated by setBoxMeshPosition
        //     this.setBoxMeshPosition(this._currentAABB, false, true); // skipSync = true to avoid overwriting section plane positions
        // } else {




        //     // No rotation - use simple AABB update
        //     this.updateAABBFromSectionPlane(type, newPos);

        //     // Rebuild mesh to reflect new position
        //     this.setBoxMeshPosition(this._currentAABB);

        //     // Update root node position to center of new AABB
        //     this.updateRootNodePosition();

        //     // Update HTML control position
        //     this.updateHtmlControlPosition(type);
        // }

        this.updateHtmlControlPosition(type);


        // Force refresh meshes to ensure they stay visible
        this.refreshMeshes();
    }
    //#region _dragTranslateSectionPlane
    private _dragTranslateSectionPlane(
        baseAxis: number[],
        from: number[],
        to: number[],
        dragAction?: any,
        sectionPlane?: any,
        sectionPlaneType?: any
    ): void {
        const p1 = math.vec3();
        const p2 = math.vec3();
        const localVec3 = math.vec3();
        const worldAxis = math.vec4();



        this._localToWorldVec(baseAxis, worldAxis);
        const planeNormal = this._getTranslationPlane(worldAxis);

        if (!this._getPointerPlaneIntersect(from, planeNormal, p1, dragAction, sectionPlane)) {
            return;
        }
        if (!this._getPointerPlaneIntersect(to, planeNormal, p2, dragAction, sectionPlane)) {
            return;
        }

        math.subVec3(p2, p1);

        // Calculate the mesh offset
        this._worldToLocalVec(p2, localVec3);
        let dot = math.dotVec3(localVec3, baseAxis);
        const offset = [
            baseAxis[0] * dot,
            baseAxis[1] * dot,
            baseAxis[2] * dot
        ];

        const position = [...sectionPlane.pos]; // local coordinate
        math.addVec3(position, offset);
        console.log('position', position);
        if (sectionPlane) {
            if (this._renovatePoint(position, sectionPlaneType)) {
                this._localToWorldVec(sectionPlane.pos, position, false);
                math.addVec3(position, this._rootNode.position);
                sectionPlane.pos = position;
            } else {
                // Calculate the section plane offset
                dot = math.dotVec3(p2, worldAxis);
                const offset = [
                    worldAxis[0] * dot,
                    worldAxis[1] * dot,
                    worldAxis[2] * dot
                ];

                const position = sectionPlane.pos;
                math.addVec3(position, offset);
                sectionPlane.pos = position; // world coordinate
            }
        }

        // TODO: remove it
        // Compares the position of mesh and section plane.
        //     this.localToWorldVec(this.lastPicked.mesh.position, worldAxis, false);
        // math.addVec3(worldAxis, this.rootNode.position);
        // math.subVec3(worldAxis, this.lastPicked.sectionPlane.pos);
        // dot = math.dotVec3(worldAxis, math.normalizeVec3(this.lastPicked.sectionPlane.dir));

        // // x axis
        const sectionPlaneX = this._sectionPlaneMap.get(BoxSectionPlaneType.RIGHT);
        const mesh = this._meshes.get(BoxSectionPlaneType.RIGHT).parent;
        this._localToWorldVec(mesh.position, worldAxis, false);
        math.addVec3(worldAxis, this._rootNode.position);
        math.subVec3(worldAxis, sectionPlaneX.pos);
        const dot2 = math.dotVec3(worldAxis, math.normalizeVec3(sectionPlaneX.dir));
    }

    //#region _localToWorldVec
    private _localToWorldVec = (() => {
        const mat = math.mat4();
        return (localVec: any, worldVec: any, normalize = true) => {
            math.quaternionToMat4(this._rootNode.quaternion, mat);
            math.transformVec3(mat, localVec, worldVec);
            if (normalize) {
                math.normalizeVec3(worldVec);
            }
            return worldVec;
        };
    })();
    //#region _worldToLocalVec
    private _worldToLocalVec = (() => {
        const mat = math.mat4();
        return (worldVec: any, localVec: any) => {
            math.quaternionToMat4(this._rootNode.quaternion, mat);
            math.inverseMat4(mat);
            math.transformVec3(mat, worldVec, localVec);
            //math.normalizeVec3(worldVec);
            return localVec;
        };
    })();




    //#region _getTranslationPlane
    private _getTranslationPlane = (() => {
        const planeNormal = math.vec3();
        return (worldAxis: any) => {
            const absX = Math.abs(worldAxis[0]);
            if (absX > Math.abs(worldAxis[1]) && absX > Math.abs(worldAxis[2])) {
                math.cross3Vec3(worldAxis, [0, 1, 0], planeNormal);
            } else {
                math.cross3Vec3(worldAxis, [1, 0, 0], planeNormal);
            }
            math.cross3Vec3(planeNormal, worldAxis, planeNormal);
            math.normalizeVec3(planeNormal);
            return planeNormal;
        };
    })();

    private _getPointerPlaneIntersect(
        mouse: number[],
        axis: number[],                 // pháp tuyến mặt phẳng làm việc (world)
        dest: any,                      // out: điểm giao trong HỆ TRỤC mặt phẳng (so với origin)
        dragAction?: any,    // truyền vào để phân biệt rotate/translate
        sectionPlane?: any,
        offset: number = 0,
    ): boolean {
        const canvas = this._store.canvas!;
        const camera = this._store.viewer?.camera!;

        // temp
        const dir = math.vec4([0, 0, 0, 1]);
        const matrix = math.mat4();

        // NDC -> world (điểm trên near plane)
        dir[0] = (mouse[0] / canvas.width) * 2.0 - 1.0;
        dir[1] = -((mouse[1] / canvas.height) * 2.0 - 1.0);
        dir[2] = 0.0;
        dir[3] = 1.0;

        math.mulMat4(camera.projMatrix, camera.viewMatrix, matrix);
        math.inverseMat4(matrix);
        math.transformVec4(matrix, dir, dir);
        math.mulVec4Scalar(dir, 1.0 / dir[3]); // điểm A trên tia ở world

        const rayO = camera.eye;           // origin của tia
        math.subVec4(dir, rayO, dir);      // dir = (A - eye)

        // Chọn origin của mặt phẳng làm việc
        // - Khi rotate: dùng tâm hộp
        // - Khi translate: ưu tiên tâm mặt đang kéo nếu có
        let origin: number[] = [0, 0, 0];
        if (dragAction !== undefined && dragAction >= DRAG_ACTIONS.xRotate) {
            origin = this._rootNode.position as number[];
        }

        else if (sectionPlane) {
            origin = sectionPlane.pos as number[];
        }

        // Tính giao tia với mặt phẳng: n.(X - origin) + offset = 0
        // => n.X + d = 0, với d = -n.origin - offset
        const n = axis; // giả định axis đã là world-normal phù hợp
        const d = -math.dotVec3(origin, n) - offset;
        const nd = math.dotVec3(n, dir);

        if (Math.abs(nd) <= 0.005) {
            // tia gần song song mặt phẳng
            return false;
        }

        const t = -(math.dotVec3(n, rayO) + d) / nd;

        // dest = rayO + t * dir - origin
        math.mulVec3Scalar(dir, t, dest);
        math.addVec3(dest, rayO);
        math.subVec3(dest, origin, dest);

        return true;
    }
    //#region   _renovatePoint
    private _renovatePoint = (point: number[], dragPlaneType: any) => {
        const rootNodePosition = [...this._rootNode.position];
        const currentAABB = this._currentAABB;
        const currentAABBCenter = [
            (currentAABB[3] + currentAABB[0]) / 2.0,
            (currentAABB[4] + currentAABB[1]) / 2.0,
            (currentAABB[5] + currentAABB[2]) / 2.0,
        ];
        const currentPosition = math.vec3();
        const rootNodePositionOffset = math.vec3();

        math.addVec3(point, currentAABBCenter, currentPosition);
        let pointChanged = false;
        const originAABB = this._originAABB;

        //axis:0,1,2 are the x, y, and z axes respectively.
        const limitAabb = (axis: number, positive: boolean) => {
            const aabbIndex = axis + (positive ? 3 : 0);
            let needChanged = positive
                ? originAABB[aabbIndex] < currentPosition[axis]
                : originAABB[aabbIndex] > currentPosition[axis];
            if (needChanged) {
                point[axis] = originAABB[aabbIndex] - currentAABBCenter[axis];
                pointChanged = true;
            } else {
                const currentAabbIndex = axis + (positive ? 0 : 3);
                const aabbValue = currentAABB[currentAabbIndex] + (positive ? 1 : -1) * MIN_WIDTH;
                //Limit the minimum width of aabb in the x direction.
                const minMaxFunc = positive ? Math.max : Math.min;
                currentAABB[aabbIndex] = minMaxFunc(aabbValue, point[axis] + currentAABBCenter[axis]);
                needChanged = positive ? currentPosition[axis] < aabbValue : currentPosition[axis] > aabbValue;
                if (needChanged) {
                    point[axis] = aabbValue - currentAABBCenter[axis];
                    pointChanged = true;
                }
            }
        };

        switch (dragPlaneType) {
            case BoxSectionPlaneType.RIGHT:
                limitAabb(0, true);
                break;
            case BoxSectionPlaneType.LEFT:
                limitAabb(0, false);
                break;
            case BoxSectionPlaneType.TOP:
                limitAabb(1, true);
                break;
            case BoxSectionPlaneType.BOTTOM:
                limitAabb(1, false);
                break;
            case BoxSectionPlaneType.FRONT:
                limitAabb(2, true);
                break;
            case BoxSectionPlaneType.BACK:
                limitAabb(2, false);
                break;
        }

        this._setBoxMeshPosition(currentAABB);

        //Calculate new position of the root node.
        const newCenter = this._rootNode.position;
        math.subVec3(newCenter, currentAABBCenter, rootNodePositionOffset);
        this._localToWorldVec(rootNodePositionOffset, currentPosition, false);
        this._rootNode.position = math.addVec3(rootNodePosition, currentPosition);

        return pointChanged;
    };

    //#region _setBoxMeshPosition
    private _setBoxMeshPosition(value: number[], changePlaneMeshDirection = false) {
        const aabb = value;

        const xWidth = aabb[3] - aabb[0];
        const yWidth = aabb[4] - aabb[1];
        const zWidth = aabb[5] - aabb[2];
        const center = [(aabb[3] + aabb[0]) / 2.0, (aabb[4] + aabb[1]) / 2.0, (aabb[5] + aabb[2]) / 2.0];

        //TODO. This code will be optimized later.
        const setMeshMatrix = (sectionPlane: any, mesh: any) => {
            let node = mesh;
            if (mesh.parent) {
                node = mesh.parent;
            }
            if (changePlaneMeshDirection) {
                node.quaternion = math.vec3PairToQuaternion(zeroVec, sectionPlane.dir, quat);
            }
            switch (sectionPlane.id) {
                case BoxSectionPlaneType.RIGHT:
                    mesh.scale = [zWidth, yWidth, 1];
                    node.position = [xWidth / 2.0, 0, 0];
                    break;
                case BoxSectionPlaneType.LEFT:
                    mesh.scale = [zWidth, yWidth, 1];
                    node.position = [-xWidth / 2.0, 0, 0];
                    break;
                case BoxSectionPlaneType.TOP:
                    mesh.scale = [xWidth, zWidth, 1];
                    node.position = [0, yWidth / 2.0, 0];
                    break;
                case BoxSectionPlaneType.BOTTOM:
                    mesh.scale = [xWidth, zWidth, 1];
                    node.position = [0, -yWidth / 2.0, 0];
                    break;
                case BoxSectionPlaneType.FRONT:
                    mesh.scale = [xWidth, yWidth, 1];
                    node.position = [0, 0, zWidth / 2.0];
                    break;
                case BoxSectionPlaneType.BACK:
                    mesh.scale = [xWidth, yWidth, 1];
                    node.position = [0, 0, -zWidth / 2.0];
                    break;
                default:
                    break;
            }
        };
        const meshes = this._meshes as Map<string, any>;
        const sectionPlaneMap = this._sectionPlaneMap;
        for (const [key, sectionPlane] of sectionPlaneMap) {
            if (meshes.has(key)) {
                const mesh = meshes.get(key);
                setMeshMatrix(sectionPlane, mesh);
            }
        }

        meshes.get(CURVE_TYPE).parent.position = [-xWidth / 2.0, -yWidth / 2.0, -zWidth / 2.0];

        this._rootNode.position = center;
    }

    /**
     * Update single mesh position from section plane position (for rotated case)
     */
    private updateSingleMeshPositionFromSectionPlane(type: BoxSectionPlaneType, worldPos: number[]): void {
        if (!this._rootNode || !this._meshes) return;

        const mesh = this._meshes.get(type);
        if (!mesh || !mesh.parent) return;

        // Convert world position to local position relative to rootNode
        const center = this._rootNode.position;
        const worldOffset = [
            worldPos[0] - center[0],
            worldPos[1] - center[1],
            worldPos[2] - center[2]
        ];

        // Transform world offset to local space using inverse rotation
        const rotMatrix = math.mat4();
        math.quaternionToMat4(this._rootNode.quaternion, rotMatrix);
        const invMatrix = math.mat4();
        math.inverseMat4(rotMatrix, invMatrix);

        const localPos = math.vec3();
        math.transformVec3(invMatrix, worldOffset, localPos);

        // Update mesh parent position directly without triggering full rebuild
        mesh.parent.position = [localPos[0], localPos[1], localPos[2]];

        // Ensure mesh stays visible
        mesh.visible = true;
        if (mesh.parent) {
            mesh.parent.visible = true;
        }

        // Force refresh to ensure proper visibility
        this.refreshMeshes();
    }

    /**
     * Update AABB based on section plane movement
     */
    private updateAABBFromSectionPlane(type: BoxSectionPlaneType, newPos: number[]): void {
        if (!this._currentAABB || !this._rootNode) return;

        // For non-rotated case, convert world position to local position relative to rootNode center
        const center = this._rootNode.position;
        const localPos = [
            newPos[0] - center[0],
            newPos[1] - center[1],
            newPos[2] - center[2]
        ];

        // Update AABB based on local position - ensure proper min/max order
        switch (type) {
            case BoxSectionPlaneType.LEFT:
                this._currentAABB[0] = center[0] + localPos[0]; // Update min X
                // Ensure min < max
                if (this._currentAABB[0] > this._currentAABB[3]) {
                    const temp = this._currentAABB[0];
                    this._currentAABB[0] = this._currentAABB[3];
                    this._currentAABB[3] = temp;
                }
                break;
            case BoxSectionPlaneType.RIGHT:
                this._currentAABB[3] = center[0] + localPos[0]; // Update max X
                // Ensure min < max
                if (this._currentAABB[0] > this._currentAABB[3]) {
                    const temp = this._currentAABB[0];
                    this._currentAABB[0] = this._currentAABB[3];
                    this._currentAABB[3] = temp;
                }
                break;
            case BoxSectionPlaneType.BOTTOM:
                this._currentAABB[1] = center[1] + localPos[1]; // Update min Y
                // Ensure min < max
                if (this._currentAABB[1] > this._currentAABB[4]) {
                    const temp = this._currentAABB[1];
                    this._currentAABB[1] = this._currentAABB[4];
                    this._currentAABB[4] = temp;
                }
                break;
            case BoxSectionPlaneType.TOP:
                this._currentAABB[4] = center[1] + localPos[1]; // Update max Y
                // Ensure min < max
                if (this._currentAABB[1] > this._currentAABB[4]) {
                    const temp = this._currentAABB[1];
                    this._currentAABB[1] = this._currentAABB[4];
                    this._currentAABB[4] = temp;
                }
                break;
            case BoxSectionPlaneType.BACK:
                this._currentAABB[2] = center[2] + localPos[2]; // Update min Z
                // Ensure min < max
                if (this._currentAABB[2] > this._currentAABB[5]) {
                    const temp = this._currentAABB[2];
                    this._currentAABB[2] = this._currentAABB[5];
                    this._currentAABB[5] = temp;
                }
                break;
            case BoxSectionPlaneType.FRONT:
                this._currentAABB[5] = center[2] + localPos[2]; // Update max Z
                // Ensure min < max
                if (this._currentAABB[2] > this._currentAABB[5]) {
                    const temp = this._currentAABB[2];
                    this._currentAABB[2] = this._currentAABB[5];
                    this._currentAABB[5] = temp;
                }
                break;
        }
    }

    /**
     * Recalculate AABB from all section plane positions when rotated
     */
    private recalculateAABBFromRotatedSectionPlanes(): void {
        if (!this._sectionPlaneMap || !this._rootNode) return;

        // Get rotation matrix to transform world positions back to local space
        const rotMatrix = math.mat4();
        math.quaternionToMat4(this._rootNode.quaternion, rotMatrix);
        const invMatrix = math.mat4();
        math.inverseMat4(rotMatrix, invMatrix);

        // Transform all section plane positions to local space
        const localPositions = new Map<BoxSectionPlaneType, number[]>();

        for (const [type, sectionPlane] of this._sectionPlaneMap) {
            const worldPos = sectionPlane.pos;
            const centerToPlane = math.subVec3(worldPos, this._rootNode.position, math.vec3());
            const localPos = math.vec3();
            math.transformVec3(invMatrix, centerToPlane, localPos);
            localPositions.set(type, [localPos[0], localPos[1], localPos[2]]);
        }

        // Update AABB based on local positions
        const leftPos = localPositions.get(BoxSectionPlaneType.LEFT);
        const rightPos = localPositions.get(BoxSectionPlaneType.RIGHT);
        const bottomPos = localPositions.get(BoxSectionPlaneType.BOTTOM);
        const topPos = localPositions.get(BoxSectionPlaneType.TOP);
        const backPos = localPositions.get(BoxSectionPlaneType.BACK);
        const frontPos = localPositions.get(BoxSectionPlaneType.FRONT);

        if (leftPos && rightPos && bottomPos && topPos && backPos && frontPos) {
            // Create AABB from the section plane positions
            // Since these are local positions relative to center, we need to add center back
            const center = this._rootNode.position;

            // this._currentAABB = [
            //     center[0] + leftPos[0],    // min X (left plane)
            //     center[1] + bottomPos[1],  // min Y (bottom plane)
            //     center[2] + backPos[2],    // min Z (back plane)
            //     center[0] + rightPos[0],   // max X (right plane)
            //     center[1] + topPos[1],     // max Y (top plane)
            //     center[2] + frontPos[2]    // max Z (front plane)
            // ];

            // Ensure proper min/max order
            if (this._currentAABB[0] > this._currentAABB[3]) {
                const temp = this._currentAABB[0];
                this._currentAABB[0] = this._currentAABB[3];
                this._currentAABB[3] = temp;
            }
            if (this._currentAABB[1] > this._currentAABB[4]) {
                const temp = this._currentAABB[1];
                this._currentAABB[1] = this._currentAABB[4];
                this._currentAABB[4] = temp;
            }
            if (this._currentAABB[2] > this._currentAABB[5]) {
                const temp = this._currentAABB[2];
                this._currentAABB[2] = this._currentAABB[5];
                this._currentAABB[5] = temp;
            }

            // Update rootNode position to new center of AABB
            const newCenter = [
                (this._currentAABB[3] + this._currentAABB[0]) / 2.0,
                (this._currentAABB[4] + this._currentAABB[1]) / 2.0,
                (this._currentAABB[5] + this._currentAABB[2]) / 2.0
            ];
            this._rootNode.position = newCenter;
        }
    }

    /**
     * Update root node position to center of current AABB
     */
    private updateRootNodePosition(): void {
        if (!this._currentAABB || !this._rootNode) return;

        const aabb = this._currentAABB;
        const center = [
            (aabb[3] + aabb[0]) / 2.0,
            (aabb[4] + aabb[1]) / 2.0,
            (aabb[5] + aabb[2]) / 2.0
        ];

        this._rootNode.position = center;
    }

    /**
     * Update HTML control position based on section plane position
     */
    private updateHtmlControlPosition(type: BoxSectionPlaneType): void {
        const controlDiv = this._htmlControls.get(type);
        const mesh = this._meshes.get(type);

        if (!controlDiv || !mesh || !mesh.parent) return;

        // Calculate world position of mesh center
        const meshLocalPos = mesh.parent.position;
        const rootNodePos = this._rootNode.position;

        // Transform mesh local position to world position
        const rotMatrix = math.mat4();
        math.quaternionToMat4(this._rootNode.quaternion, rotMatrix);
        const worldOffset = math.vec3();
        math.transformVec3(rotMatrix, meshLocalPos, worldOffset);

        // Get final world position of mesh center
        const worldPos = [
            rootNodePos[0] + worldOffset[0],
            rootNodePos[1] + worldOffset[1],
            rootNodePos[2] + worldOffset[2]
        ];

        // Convert to screen coordinates
        const screenPos = this.worldToScreen(worldPos);

        if (screenPos) {
            controlDiv.style.left = `${screenPos[0]}px`;
            controlDiv.style.top = `${screenPos[1]}px`;
            controlDiv.style.display = 'block';
        } else {
            controlDiv.style.display = 'none';
        }
    }

    /**
     * Update all HTML control positions
     */
    private updateAllHtmlControlPositions(): void {
        this._htmlControls.forEach((_, type) => {
            this.updateHtmlControlPosition(type);
        });
    }

    /**
     * Force refresh all mesh positions and visibility
     */
    private refreshMeshes(): void {
        if (!this._visible) return;

        this._meshes.forEach((mesh: any) => {
            if (mesh.id && mesh.id.includes('SECTION_BOX')) {
                // Ensure section plane meshes are visible
                mesh.visible = true;
                mesh.edges = true;
                if (mesh.parent) {
                    mesh.parent.visible = true;
                }
            }
        });

        // Always hide arrows
        // this._hideableMeshes.forEach((mesh: any) => (mesh.visible = false));
    }

    initSectionPlanes(sectionPlaneMap: Map<BoxSectionPlaneType, any>, aabb: number[]) {
        if (!this._meshes) {
            return;
        }

        this._sectionPlaneMap = sectionPlaneMap;

        // Create HTML controls for all section planes
        this.createAllHtmlControls();

        this.rebuildBoxMesh(aabb);
    }

    rebuildBoxMesh(value: number[]) {
        if (value.length !== 6 || this._sectionPlaneMap.size === 0) {
            return;
        }

        this._originAABB = [...value];
        this._currentAABB = [...value];

        this.setBoxMeshPosition(this._originAABB, true);
    }

    // Change the aabb of box mesh
    private setBoxMeshPosition(value: number[], changePlaneMeshDirection = false, skipSync = false) {
        const aabb = value;

        const xWidth = aabb[3] - aabb[0];
        const yWidth = aabb[4] - aabb[1];
        const zWidth = aabb[5] - aabb[2];
        const center = [(aabb[3] + aabb[0]) / 2.0, (aabb[4] + aabb[1]) / 2.0, (aabb[5] + aabb[2]) / 2.0];

        // Check if we have rotation
        const hasRotation = Math.abs(this._rootNode.quaternion[0]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[1]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[2]) > 0.001 ||
            Math.abs(this._rootNode.quaternion[3] - 1) > 0.001;

        const setMeshMatrix = (sectionPlane: any, mesh: any) => {
            let node = mesh;
            if (mesh.parent) {
                node = mesh.parent;
            }

            // Ensure mesh stays visible
            mesh.visible = true;
            if (mesh.parent) {
                mesh.parent.visible = true;
            }

            // Get the original local direction (before rotation) for mesh orientation
            const originalDirections: Map<BoxSectionPlaneType, number[]> = new Map();
            originalDirections.set(BoxSectionPlaneType.RIGHT, [-1, 0, 0]);
            originalDirections.set(BoxSectionPlaneType.LEFT, [1, 0, 0]);
            originalDirections.set(BoxSectionPlaneType.TOP, [0, -1, 0]);
            originalDirections.set(BoxSectionPlaneType.BOTTOM, [0, 1, 0]);
            originalDirections.set(BoxSectionPlaneType.FRONT, [0, 0, -1]);
            originalDirections.set(BoxSectionPlaneType.BACK, [0, 0, 1]);

            // Update orientation when requested or when has rotation
            if (changePlaneMeshDirection || hasRotation) {
                // Use original local direction since mesh inherits rootNode rotation
                const localDir = originalDirections.get(sectionPlane.id);
                if (localDir) {
                    node.quaternion = math.vec3PairToQuaternion(zeroVec, localDir, quat);
                }
            }

            // Always use AABB-based positioning for consistent behavior
            // This ensures meshes stay in the right relative positions
            const safeXWidth = Math.max(0.1, xWidth);
            const safeYWidth = Math.max(0.1, yWidth);
            const safeZWidth = Math.max(0.1, zWidth);

            switch (sectionPlane.id) {
                case BoxSectionPlaneType.RIGHT:
                    mesh.scale = [safeZWidth, safeYWidth, 1];
                    node.position = [safeXWidth / 2.0, 0, 0];
                    break;
                case BoxSectionPlaneType.LEFT:
                    mesh.scale = [safeZWidth, safeYWidth, 1];
                    node.position = [-safeXWidth / 2.0, 0, 0];
                    break;
                case BoxSectionPlaneType.TOP:
                    mesh.scale = [safeXWidth, safeZWidth, 1];
                    node.position = [0, safeYWidth / 2.0, 0];
                    break;
                case BoxSectionPlaneType.BOTTOM:
                    mesh.scale = [safeXWidth, safeZWidth, 1];
                    node.position = [0, -safeYWidth / 2.0, 0];
                    break;
                case BoxSectionPlaneType.FRONT:
                    mesh.scale = [safeXWidth, safeYWidth, 1];
                    node.position = [0, 0, safeZWidth / 2.0];
                    break;
                case BoxSectionPlaneType.BACK:
                    mesh.scale = [safeXWidth, safeYWidth, 1];
                    node.position = [0, 0, -safeZWidth / 2.0];
                    break;
                default:
                    break;
            }
        };

        const meshes = this._meshes as Map<string, any>;
        const sectionPlaneMap = this._sectionPlaneMap;
        for (const [key, sectionPlane] of sectionPlaneMap) {
            if (meshes.has(key)) {
                const mesh = meshes.get(key);
                setMeshMatrix(sectionPlane, mesh);
            }
        }

        meshes.get(CURVE_TYPE).parent.position = [-xWidth / 2.0, -yWidth / 2.0, -zWidth / 2.0];

        this._rootNode.position = center;

        // After updating mesh positions, sync section plane positions (unless skipped)
        if (!skipSync) {
            this.syncSectionPlanesWithMeshes();
        }

        // Always update HTML control positions after mesh positions change
        this.updateAllHtmlControlPositions();

        const screenPos = this.worldToScreen(center);
    }

    /**
     * Sync section plane positions with mesh positions (important after AABB changes)
     */
    private syncSectionPlanesWithMeshes(): void {
        if (!this._rootNode || !this._sectionPlaneMap) return;

        const meshes = this._meshes;
        const rotMatrix = math.mat4();
        math.quaternionToMat4(this._rootNode.quaternion, rotMatrix);
        const center = this._rootNode.position;

        // Original local directions for each plane type
        const originalDirections: Map<BoxSectionPlaneType, number[]> = new Map();
        originalDirections.set(BoxSectionPlaneType.RIGHT, [-1, 0, 0]);
        originalDirections.set(BoxSectionPlaneType.LEFT, [1, 0, 0]);
        originalDirections.set(BoxSectionPlaneType.TOP, [0, -1, 0]);
        originalDirections.set(BoxSectionPlaneType.BOTTOM, [0, 1, 0]);
        originalDirections.set(BoxSectionPlaneType.FRONT, [0, 0, -1]);
        originalDirections.set(BoxSectionPlaneType.BACK, [0, 0, 1]);

        for (const [key, sectionPlane] of this._sectionPlaneMap) {
            const mesh = meshes.get(key);
            if (mesh && mesh.parent) {
                // Transform mesh local position to world position
                const localPos = mesh.parent.position;
                const worldOffset = math.vec3();
                math.transformVec3(rotMatrix, localPos, worldOffset);

                // Add center to get final world position
                sectionPlane.pos = [
                    center[0] + worldOffset[0],
                    center[1] + worldOffset[1],
                    center[2] + worldOffset[2]
                ];

                // Update section plane direction with rotation
                const localDir = originalDirections.get(key);
                if (localDir) {
                    const worldDir = math.vec3();
                    math.transformVec3(rotMatrix, localDir, worldDir);
                    sectionPlane.dir = [worldDir[0], worldDir[1], worldDir[2]];
                }
            }
        }

        // Update HTML control positions after syncing section planes
        this.updateAllHtmlControlPositions();
    }

    setVisible(visible = true) {
        if (this._visible === visible) {
            return;
        }
        this._visible = visible;

        // Hiện 6 mặt phẳng với transparent (chỉ cạnh)
        this._meshes.forEach((mesh: any) => {
            if (mesh.id && mesh.id.includes('SECTION_BOX')) { // plane meshes
                mesh.visible = visible;
                mesh.edges = true; // Hiện cạnh
                // Ensure parent node is also visible when mesh is visible
                if (visible && mesh.parent) {
                    mesh.parent.visible = true;
                }
            } else {
                mesh.visible = visible; // Các mesh khác (rotation control)
            }
        });

        // Show/hide HTML controls instead of 3D arrows
        this._htmlControls.forEach((controlDiv) => {
            controlDiv.style.display = visible ? 'block' : 'none';
        });

        // Update HTML control positions when becoming visible
        if (visible) {
            this.updateAllHtmlControlPositions();
        }

        // Hide 3D arrows - always keep them hidden
        this._hideableMeshes.forEach((mesh: any) => (mesh.visible = false));
    }

    getVisible() {
        return this._visible;
    }

    /**
     * Sets if this Control is culled. This is called by SectionPlanesPlugin to
     * temporarily hide the Control while a snapshot is being taken by Viewer#getSnapshot().
     */
    setCulled(culled: boolean) {
        this._meshes.forEach((mesh: any) => (mesh.culled = culled));
    }

    private createMeshes(shapes: { [key: string]: any }, materials: { [key: string]: any }) {
        const entityParams = {
            collidable: false, // Don't participate in the calculation of the scene aabb when collidable is false.
            clippable: false,
            // visible: true,
            visible: false,
        };

        const axisParmas = {
            geometry: shapes.arrowHandle,
            material: materials.axis,
            highlightMaterial: materials.highlightAxis,
            pickable: true,


            ...entityParams,
            matrix: (() => {
                const translate = math.translateMat4c(0, GEOMETRY_RADIUS, 0, math.identityMat4());
                const rotate = math.rotationMat4v(-90 * math.DEGTORAD, [1, 0, 0], math.identityMat4());
                return math.mulMat4(rotate, translate, math.identityMat4());
            })(),
        };

        const arrowHeadParmas = {

            geometry: shapes.arrowHead,
            material: materials.axis,
            highlightMaterial: materials.highlightAxis,
            pickable: true,
            ...entityParams,
            matrix: (() => {
                const translate = math.translateMat4c(0, 4 * GEOMETRY_RADIUS, 0, math.identityMat4());
                const rotate = math.rotationMat4v(-90 * math.DEGTORAD, [1, 0, 0], math.identityMat4());
                return math.mulMat4(rotate, translate, math.identityMat4());
            })(),
        };

        const planeParams = {
            geometry: shapes.plane,
            material: materials.pickable,
            edges: true,
            edgeMaterial: materials.planeEdge,
            highlightMaterial: materials.planeHighlighted,
            pickable: true,
            ...entityParams,
        };
        const rootNode = this._rootNode;
        Object.values(BoxSectionPlaneType).forEach((type) => {
            const node = new Node(rootNode, { collidable: false });
            rootNode.addChild(node, NO_STATE_INHERIT);
            // plane
            this._meshes.set(
                type,
                node.addChild(new Mesh(node, { id: addPrefix(type)(SECTION_BOX_ID), ...planeParams }), NO_STATE_INHERIT)
            );

            // translate control
            const arrowNode = new Node(node, { collidable: false });
            node.addChild(arrowNode, NO_STATE_INHERIT);
            // Set ID cho axis (handle) mesh
            this._hideableMeshes.set(
                CommonUtils.joinStrings(type, AXIS_SUFFIX),
                arrowNode.addChild(new Mesh(arrowNode, {
                    ...axisParmas,
                    id: addPrefix(`${type}_${AXIS_SUFFIX}`)(SECTION_BOX_ID)
                }), NO_STATE_INHERIT)
            );
            // Set ID cho arrow head mesh
            this._hideableMeshes.set(
                CommonUtils.joinStrings(type, ARROW_SUFFIX),
                arrowNode.addChild(new Mesh(arrowNode, {
                    ...arrowHeadParmas,
                    id: addPrefix(`${type}_${ARROW_SUFFIX}`)(SECTION_BOX_ID)
                }))
            );

        });

        // Rotation control
        const childNode = new Node(rootNode, { collidable: false });
        rootNode.addChild(childNode, NO_STATE_INHERIT);

        this._meshes.set(
            CURVE_TYPE,
            childNode.addChild(
                new Mesh(childNode, {
                    geometry: shapes.curve,
                    material: materials.axis,
                    highlighted: true,
                    highlightMaterial: materials.highlightAxis,
                    rotation: [-90, 0, 90],
                    pickable: false,
                    ...entityParams,
                }),
                NO_STATE_INHERIT
            )
        );

        const rotationArrowParams = {
            geometry: shapes.arrowHead,
            material: materials.axis,
            highlighted: true,
            highlightMaterial: materials.highlightAxis,
            pickable: true,
            ...entityParams,
        };
        // The arrow points to z direction
        this._meshes.set(
            CommonUtils.joinStrings(CURVE_TYPE, ARROW_SUFFIX, "1"),
            childNode.addChild(
                new Mesh(childNode, {
                    ...rotationArrowParams,
                    matrix: (() => {
                        const translate = math.translateMat4c(
                            -GEOMETRY_RADIUS * 10,
                            0,
                            GEOMETRY_RADIUS * 2,
                            math.identityMat4()
                        );
                        const rotate = math.rotationMat4v(90 * math.DEGTORAD, [1, 0, 0], math.identityMat4());
                        return math.mulMat4(translate, rotate, math.identityMat4());
                    })(),
                }),
                NO_STATE_INHERIT
            )
        );
        // The arrow points to x direction
        this._meshes.set(
            CommonUtils.joinStrings(CURVE_TYPE, ARROW_SUFFIX, "2"),
            childNode.addChild(
                new Mesh(childNode, {
                    ...rotationArrowParams,
                    matrix: (() => {
                        const translate = math.translateMat4c(
                            GEOMETRY_RADIUS * 2,
                            0,
                            -GEOMETRY_RADIUS * 10,
                            math.identityMat4()
                        );
                        const rotate = math.rotationMat4v(-90 * math.DEGTORAD, [0, 0, 1], math.identityMat4());
                        return math.mulMat4(translate, rotate, math.identityMat4());
                    })(),
                }),
                NO_STATE_INHERIT
            )
        );

        this._meshes.set(
            CommonUtils.joinStrings(CURVE_TYPE, HANDLE_TYPE),
            childNode.addChild(
                new Mesh(childNode, {
                    geometry: shapes.curveHandle,
                    material: materials.pickable,
                    rotation: [-90, 0, 90],
                    pickable: true,
                    ...entityParams,
                }),
                NO_STATE_INHERIT
            )
        );
    }

    /**
     * Builds the Entities that represent this control.
     */
    private createNodes() {
        const scene = this._viewer.scene;

        this._rootNode = new Node(scene, {
            collidable: false,
        });
        const rootNode = this._rootNode;

        const createCylinderGeometry = (
            radiusTop: number,
            radiusBottom: number,
            height: number,
            radialSegments = 80,
            heightSegments = 8,
            openEnded = false
        ) =>
            new ReadableGeometry(
                rootNode,
                buildCylinderGeometry({ radiusTop, radiusBottom, radialSegments, heightSegments, height, openEnded })
            );
        const createTorusGeometry = (radius: number, tube: number, arc: number, radialSegments = 80, tubeSegments = 8) =>
            new ReadableGeometry(rootNode, buildTorusGeometry({ radius, tube, radialSegments, tubeSegments, arc }));

        // Reusable geometries
        const shapes: { [key: string]: any } = {
            plane: new ReadableGeometry(
                rootNode,
                GeometryUtils.buildPlaneGeometry({ width: 2 * GEOMETRY_RADIUS, height: 2 * GEOMETRY_RADIUS, isClockwise: true })
            ),
            arrowHead: createCylinderGeometry(0.001, GEOMETRY_RADIUS * 2, GEOMETRY_RADIUS * 4), // radiusTop cannot be 0, should be a bug!
            // TODO.Expanding the geometry is good for being selected
            arrowHandle: createCylinderGeometry(GEOMETRY_RADIUS, GEOMETRY_RADIUS, GEOMETRY_RADIUS * 2),
            curve: createTorusGeometry(GEOMETRY_RADIUS * 10, GEOMETRY_RADIUS / 2.0, (Math.PI * 2.0) / 4.0),
            curveHandle: createTorusGeometry(GEOMETRY_RADIUS * 10, GEOMETRY_RADIUS, (Math.PI * 2.0) / 4.0),
        };

        const createPhongMaterial = (color: number[], lineWidth = 2) =>
            new PhongMaterial(rootNode, {
                diffuse: color,
                emissive: color,
                ambient: [0, 0, 0],
                specular: [1, 1, 1],
                shininess: 80,
                opacity: 0.6,
                lineWidth,
            });

        // Reusable materials
        const materials: { [key: string]: any } = {
            pickable: new PhongMaterial(rootNode, {
                // Invisible material for pickable handles, which define a pickable 3D area
                diffuse: [1, 1, 0],
                alpha: 0, // Invisible
                alphaMode: "blend",
            }),
            axis: createPhongMaterial(DEFAULT_COLOR),
            highlightAxis: new EmphasisMaterial(rootNode, {
                // Emphasis for red rotation affordance hoop
                edges: false,
                fill: true,
                fillColor: [1, 0, 0],
                fillAlpha: 0.6,
            }),
            planeEdge: new EdgeMaterial(rootNode, {
                edgeColor: DEFAULT_COLOR,
                edgeAlpha: 1.0,
            }),
            planeHighlighted: new EmphasisMaterial(rootNode, {
                edges: true,
                fill: true,
                fillColor: DEFAULT_COLOR,
                fillAlpha: 0.1,
            }),
        };

        this.createMeshes(shapes, materials);
    }

    private bindEvents() {
        const DRAG_ACTIONS = {
            none: -1,
            xTranslate: 0,
            yTranslate: 1,
            zTranslate: 2,
            xRotate: 3,
            yRotate: 4,
            zRotate: 5,
        };

        let grabbing = false;

        let lastPicked: PickedComponent | undefined;
        let lastHideableMesh: any = undefined;

        let nextDragAction = DRAG_ACTIONS.none; // As we hover grabbed an arrow or hoop, self is the action we would do if we then dragged it.
        let dragAction = DRAG_ACTIONS.none; // Action we're doing while we drag an arrow or hoop.
        let dragPlaneType = BoxSectionPlaneType.LEFT;
        const lastCanvasPos = math.vec2();

        const xBaseAxis = math.vec3([1, 0, 0]);
        const yBaseAxis = math.vec3([0, 1, 0]);
        const zBaseAxis = math.vec3([0, 0, 1]);

        const cameraControl = this._viewer.cameraControl;
        const canvas = this._viewer.scene.canvas.canvas;
        const camera = this._viewer.camera;

        const rootNode = this._rootNode;
        const localToWorldVec = (() => {
            const mat = math.mat4();
            return (localVec: any, worldVec: any, normalize = true) => {
                math.quaternionToMat4(rootNode.quaternion, mat);
                math.transformVec3(mat, localVec, worldVec);
                if (normalize) {
                    math.normalizeVec3(worldVec);
                }
                return worldVec;
            };
        })();

        const worldToLocalVec = (() => {
            const mat = math.mat4();
            return (worldVec: any, localVec: any) => {
                math.quaternionToMat4(rootNode.quaternion, mat);
                math.inverseMat4(mat);
                math.transformVec3(mat, worldVec, localVec);
                //math.normalizeVec3(worldVec);
                return localVec;
            };
        })();

        const getTranslationPlane = (() => {
            const planeNormal = math.vec3();
            return (worldAxis: any) => {
                const absX = Math.abs(worldAxis[0]);
                if (absX > Math.abs(worldAxis[1]) && absX > Math.abs(worldAxis[2])) {
                    math.cross3Vec3(worldAxis, [0, 1, 0], planeNormal);
                } else {
                    math.cross3Vec3(worldAxis, [1, 0, 0], planeNormal);
                }
                math.cross3Vec3(planeNormal, worldAxis, planeNormal);
                math.normalizeVec3(planeNormal);
                return planeNormal;
            };
        })();

        const currentPosition = math.vec3();
        const rootNodePositionOffset = math.vec3();

        const renovatePoint = (point: number[]) => {
            const rootNodePosition = [...rootNode.position];
            const currentAABB = this._currentAABB;
            const currentAABBCenter = [
                (currentAABB[3] + currentAABB[0]) / 2.0,
                (currentAABB[4] + currentAABB[1]) / 2.0,
                (currentAABB[5] + currentAABB[2]) / 2.0,
            ];

            math.addVec3(point, currentAABBCenter, currentPosition);
            let pointChanged = false;
            const originAABB = this._originAABB;

            //axis:0,1,2 are the x, y, and z axes respectively.
            const limitAabb = (axis: number, positive: boolean) => {
                const aabbIndex = axis + (positive ? 3 : 0);
                const currentAabbIndex = axis + (positive ? 0 : 3);
                const aabbValue = currentAABB[currentAabbIndex] + (positive ? 1 : -1) * MIN_WIDTH;
                // Chỉ kiểm tra giới hạn MIN_WIDTH, không giới hạn bởi originAABB
                const minMaxFunc = positive ? Math.max : Math.min;
                currentAABB[aabbIndex] = minMaxFunc(aabbValue, point[axis] + currentAABBCenter[axis]);
                const needChanged = positive ? currentPosition[axis] < aabbValue : currentPosition[axis] > aabbValue;
                if (needChanged) {
                    point[axis] = aabbValue - currentAABBCenter[axis];
                    pointChanged = true;
                }
            };

            switch (dragPlaneType) {
                case BoxSectionPlaneType.RIGHT:
                    limitAabb(0, true);
                    break;
                case BoxSectionPlaneType.LEFT:
                    limitAabb(0, false);
                    break;
                case BoxSectionPlaneType.TOP:
                    limitAabb(1, true);
                    break;
                case BoxSectionPlaneType.BOTTOM:
                    limitAabb(1, false);
                    break;
                case BoxSectionPlaneType.FRONT:
                    limitAabb(2, true);
                    break;
                case BoxSectionPlaneType.BACK:
                    limitAabb(2, false);
                    break;
            }

            this.setBoxMeshPosition(currentAABB);

            //Calculate new position of the root node.
            const newCenter = rootNode.position;
            math.subVec3(newCenter, currentAABBCenter, rootNodePositionOffset);
            localToWorldVec(rootNodePositionOffset, currentPosition, false);
            rootNode.position = math.addVec3(rootNodePosition, currentPosition);

            return pointChanged;
        };

        const dragTranslateSectionPlane = (() => {
            const p1 = math.vec3();
            const p2 = math.vec3();
            const localVec3 = math.vec3();
            const worldAxis = math.vec4();
            return (baseAxis: number[], from: number[], to: number[]) => {
                if (!lastPicked) {
                    return;
                }
                localToWorldVec(baseAxis, worldAxis);
                const planeNormal = getTranslationPlane(worldAxis);
                if (!getPointerPlaneIntersect(from, planeNormal, p1)) {
                    return;
                }
                if (!getPointerPlaneIntersect(to, planeNormal, p2)) {
                    return;
                }

                math.subVec3(p2, p1);

                //Calculate the mesh offset
                worldToLocalVec(p2, localVec3);
                let dot = math.dotVec3(localVec3, baseAxis);
                const offset = [baseAxis[0] * dot, baseAxis[1] * dot, baseAxis[2] * dot];

                const position = [...lastPicked.mesh.position]; // local coordinate

                math.addVec3(position, offset);
                if (lastPicked.sectionPlane) {
                    if (renovatePoint(position)) {

                        localToWorldVec(lastPicked.mesh.position, position, false);
                        math.addVec3(position, rootNode.position);
                        lastPicked.sectionPlane.pos = position;
                    } else {
                        //Calculate the section plane offset

                        dot = math.dotVec3(p2, worldAxis);
                        // World coordinate offset
                        const offset = [worldAxis[0] * dot, worldAxis[1] * dot, worldAxis[2] * dot];

                        const position = lastPicked.sectionPlane.pos;
                        math.addVec3(position, offset);
                        lastPicked.sectionPlane.pos = position; // world coordinate
                    }
                }

                //TODO: remove it
                //Compares the position of mesh and section plane.
                localToWorldVec(lastPicked.mesh.position, worldAxis, false);
                math.addVec3(worldAxis, rootNode.position);
                math.subVec3(worldAxis, lastPicked.sectionPlane.pos);
                dot = math.dotVec3(worldAxis, math.normalizeVec3(lastPicked.sectionPlane.dir));

                // x axis
                const sectionPlane = this._sectionPlaneMap.get(BoxSectionPlaneType.RIGHT);
                const mesh = this._meshes.get(BoxSectionPlaneType.RIGHT).parent;
                localToWorldVec(mesh.position, worldAxis, false);
                math.addVec3(worldAxis, rootNode.position);
                math.subVec3(worldAxis, sectionPlane.pos);
                const dot2 = math.dotVec3(worldAxis, math.normalizeVec3(sectionPlane.dir));
            };
        })();

        // Get the intersection point of ray and plane
        const getPointerPlaneIntersect = (() => {
            const dir = math.vec4([0, 0, 0, 1]);
            const matrix = math.mat4();
            return (mouse: number[], axis: number[], dest: any, offset = 0) => {
                dir[0] = (mouse[0] / canvas.width) * 2.0 - 1.0;
                dir[1] = -((mouse[1] / canvas.height) * 2.0 - 1.0);
                dir[2] = 0.0;
                dir[3] = 1.0;
                math.mulMat4(camera.projMatrix, camera.viewMatrix, matrix); // Unproject norm device coords to view coords
                math.inverseMat4(matrix);
                math.transformVec4(matrix, dir, dir);
                math.mulVec4Scalar(dir, 1.0 / dir[3]); // This is now point A on the ray in world space
                const rayO = camera.eye; // The direction
                math.subVec4(dir, rayO, dir);
                //const origin = self._sectionPlane.pos; // Plane origin:
                let origin = [0, 0, 0];
                if (dragAction >= DRAG_ACTIONS.xRotate) {
                    origin = rootNode.position; //[(aabb[3] + aabb[0]) / 2.0, (aabb[4] + aabb[1]) / 2.0, (aabb[5] + aabb[2]) / 2.0];
                } else if (lastPicked?.sectionPlane) {
                    origin = lastPicked?.sectionPlane.pos;
                }
                const d = -math.dotVec3(origin, axis) - offset;
                const dot = math.dotVec3(axis, dir);
                if (Math.abs(dot) > 0.005) {
                    const t = -(math.dotVec3(axis, rayO) + d) / dot;
                    math.mulVec3Scalar(dir, t, dest);
                    math.addVec3(dest, rayO);
                    math.subVec3(dest, origin, dest);
                    return true;
                }
                return false;
            };
        })();

        const dragRotateSectionPlane = (baseAxis: number[], from: number[], to: number[]) => {
            const canvasBoundary = this._viewer.scene.canvas.boundary;
            const canvasWidth = canvasBoundary[2] - canvasBoundary[0];
            const incDegrees = ((to[0] - from[0]) / canvasWidth) * DRAG_ROTATION_RATE;

            rootNode.rotate(baseAxis, incDegrees);
            rotateSectionPlane();
        };

        const rotateSectionPlane = (() => {
            const dir = math.vec3();
            const mat = math.mat4();
            const directions: Map<BoxSectionPlaneType, number[]> = new Map();
            directions.set(BoxSectionPlaneType.RIGHT, [-1, 0, 0]);
            directions.set(BoxSectionPlaneType.LEFT, [1, 0, 0]);
            directions.set(BoxSectionPlaneType.TOP, [0, -1, 0]);
            directions.set(BoxSectionPlaneType.BOTTOM, [0, 1, 0]);
            directions.set(BoxSectionPlaneType.FRONT, [0, 0, -1]);
            directions.set(BoxSectionPlaneType.BACK, [0, 0, 1]);
            return () => {
                const center = rootNode.position;
                math.quaternionToMat4(rootNode.quaternion, mat);
                const meshes = this._meshes;

                for (const [key, sectionPlane] of this._sectionPlaneMap) {
                    const planeDir = directions.get(key);

                    // Update section plane direction with rotation
                    math.transformVec3(mat, planeDir, dir);
                    sectionPlane.dir = [dir[0], dir[1], dir[2]];

                    // Get mesh - mesh inherits rootNode rotation automatically
                    // We need to ensure mesh orientation matches section plane direction
                    const mesh = meshes.get(key);
                    if (mesh && mesh.parent) {
                        // Since mesh is child of rootNode, it already inherits the rotation
                        // We just need to set the correct local orientation
                        // Keep the original local direction for the mesh
                        mesh.parent.quaternion = math.vec3PairToQuaternion(zeroVec, planeDir, quat);
                    }

                    // Update section plane world position from current mesh local position
                    const meshPos = mesh.parent.position;
                    math.transformVec3(mat, meshPos, dir);
                    math.addVec3(dir, center);
                    sectionPlane.pos = [dir[0], dir[1], dir[2]];
                }

                // Update HTML control positions after rotation
                this.updateAllHtmlControlPositions();

                // Force refresh meshes to ensure they stay visible
                this.refreshMeshes();
            };
        })();

        {
            // Keep gizmo screen size constant
            const tempVec3a = math.vec3([0, 0, 0]);
            let lastDist = -1;
            const scene = this._viewer.scene;
            const auxiliaryNode = this._meshes.get(CURVE_TYPE).parent;
            this._sceneSubIds.push(
                scene.on("tick", () => {
                    if (!this._visible) {
                        return;
                    }
                    const dist = Math.abs(math.lenVec3(math.subVec3(scene.camera.eye, rootNode.position, tempVec3a)));

                    if (Math.abs(dist - lastDist) > EPSILON2) {
                        let size = 1.0;
                        if (camera.projection === "perspective") {
                            const worldSize = Math.tan(camera.perspective.fov * math.DEGTORAD) * dist;
                            size = SCALE_RADIO * worldSize;
                        } else if (camera.projection === "ortho") {
                            size = SCALE_RADIO * camera.ortho.scale;
                        }
                        const rotationControlSize = ROTATION_SCALE_RADIO * size;
                        auxiliaryNode.scale = [rotationControlSize, rotationControlSize, rotationControlSize];
                        Object.values(BoxSectionPlaneType).forEach((type) => {
                            this._hideableMeshes.get(CommonUtils.joinStrings(type, AXIS_SUFFIX)).parent.scale = [
                                size,
                                size,
                                size,
                            ];
                        });
                        lastDist = dist;
                    }

                    // Update HTML control positions on each tick
                    if (this._visible && this._htmlControls.size > 0) {
                        this.updateAllHtmlControlPositions();
                        // Ensure meshes stay visible
                        this.refreshMeshes();
                    }
                })
            );
        }

        {
            const pointerEnabled = cameraControl.pointerEnabled;
            const input = this._viewer.scene.input;
            const cursor = canvas.style.cursor;
            let isMouseDown = false;

            const meshes = this._meshes;
            const hideableMeshes = this._hideableMeshes;

            const hoverEnter = (hit: any) => {

                if (!this._visible) {
                    return;
                }
                if (isMouseDown) {
                    return;
                }
                canvas.style.cursor = cursor;
                nextDragAction = DRAG_ACTIONS.none;
                grabbing = false;
                if (lastPicked && lastPicked.mesh) {
                    lastPicked.mesh.highlighted = false;
                }
                // if (lastHideableMesh) {
                //     lastHideableMesh.visible = false;
                // }
                const sectionPlaneMap = this._sectionPlaneMap;
                const picked: PickedComponent = { mesh: undefined };
                let hideableMesh: any = undefined;
                const meshId = hit.entity.id;
                // console.log(`Hover enter: ${meshId}`);
                const getId = (...args: string[]) => hideableMeshes.get(CommonUtils.joinStrings(...args)).id;
                switch (meshId) {
                    case getId(BoxSectionPlaneType.RIGHT, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.RIGHT, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.RIGHT;
                        nextDragAction = DRAG_ACTIONS.xTranslate;
                        break;
                    case getId(BoxSectionPlaneType.LEFT, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.LEFT, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.LEFT;
                        nextDragAction = DRAG_ACTIONS.xTranslate;
                        break;
                    case getId(BoxSectionPlaneType.TOP, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.TOP, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.TOP;
                        nextDragAction = DRAG_ACTIONS.yTranslate;
                        break;
                    case getId(BoxSectionPlaneType.BOTTOM, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.BOTTOM, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.BOTTOM;
                        nextDragAction = DRAG_ACTIONS.yTranslate;
                        break;
                    case getId(BoxSectionPlaneType.FRONT, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.FRONT, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.FRONT;
                        nextDragAction = DRAG_ACTIONS.zTranslate;
                        break;
                    case getId(BoxSectionPlaneType.BACK, ARROW_SUFFIX):
                    case getId(BoxSectionPlaneType.BACK, AXIS_SUFFIX):
                        dragPlaneType = BoxSectionPlaneType.BACK;
                        nextDragAction = DRAG_ACTIONS.zTranslate;
                        break;
                    case meshes.get(BoxSectionPlaneType.RIGHT).id:
                        dragPlaneType = BoxSectionPlaneType.RIGHT;
                        break;
                    case meshes.get(BoxSectionPlaneType.LEFT).id:
                        dragPlaneType = BoxSectionPlaneType.LEFT;
                        break;
                    case meshes.get(BoxSectionPlaneType.TOP).id:
                        dragPlaneType = BoxSectionPlaneType.TOP;
                        break;
                    case meshes.get(BoxSectionPlaneType.BOTTOM).id:
                        dragPlaneType = BoxSectionPlaneType.BOTTOM;
                        break;
                    case meshes.get(BoxSectionPlaneType.FRONT).id:
                        dragPlaneType = BoxSectionPlaneType.FRONT;
                        break;
                    case meshes.get(BoxSectionPlaneType.BACK).id:
                        dragPlaneType = BoxSectionPlaneType.BACK;
                        break;
                    case meshes.get(CommonUtils.joinStrings(CURVE_TYPE, HANDLE_TYPE)).id:
                        nextDragAction = DRAG_ACTIONS.yRotate;
                        break;
                    default:
                        nextDragAction = DRAG_ACTIONS.none;
                        return;
                }

                if (nextDragAction >= DRAG_ACTIONS.none && nextDragAction < DRAG_ACTIONS.xRotate) {
                    picked.sectionPlane = sectionPlaneMap.get(dragPlaneType);
                    const mesh = meshes.get(dragPlaneType);
                    picked.mesh = nextDragAction === DRAG_ACTIONS.none ? mesh : mesh.parent; // parent node of the picked section plane.
                    hideableMesh = hideableMeshes.get(CommonUtils.joinStrings(dragPlaneType, ARROW_SUFFIX)).parent;
                }



                // Chỉ highlight arrow mesh khi hover vào arrow
                if (nextDragAction !== DRAG_ACTIONS.none && nextDragAction < DRAG_ACTIONS.xRotate) {
                    canvas.style.cursor = "pointer";

                    // Highlight cả axis (handle) và arrow cùng lúc
                    const arrowMesh = hideableMeshes.get(CommonUtils.joinStrings(dragPlaneType, ARROW_SUFFIX));
                    const axisMesh = hideableMeshes.get(CommonUtils.joinStrings(dragPlaneType, AXIS_SUFFIX));

                    if (arrowMesh) {
                        arrowMesh.highlighted = true;
                    }
                    if (axisMesh) {
                        axisMesh.highlighted = true;
                    }
                }
                // if (picked.mesh) {
                //     picked.mesh.highlighted = true;
                // }
                lastPicked = picked;

                // if (hideableMesh) {
                //     hideableMesh.visible = true;
                // }
                lastHideableMesh = hideableMesh;

                if (nextDragAction !== DRAG_ACTIONS.none) {
                    grabbing = true;
                }
            };

            const hoverOff = () => {
                if (!this._visible) {
                    return;
                }
                canvas.style.cursor = cursor;
                grabbing = false;
                // Reset highlight cho cả arrow và axis
                if (lastPicked && dragPlaneType) {
                    const arrowMesh = hideableMeshes.get(CommonUtils.joinStrings(dragPlaneType, ARROW_SUFFIX));
                    const axisMesh = hideableMeshes.get(CommonUtils.joinStrings(dragPlaneType, AXIS_SUFFIX));

                    if (arrowMesh) {
                        arrowMesh.highlighted = false;
                    }
                    if (axisMesh) {
                        axisMesh.highlighted = false;
                    }
                }
                if (lastPicked && lastPicked.mesh) {
                    lastPicked.mesh.highlighted = false;
                    lastPicked.mesh = undefined;
                    lastPicked.sectionPlane = undefined;
                }
                lastPicked = undefined;
                nextDragAction = DRAG_ACTIONS.none;

                // if (lastHideableMesh) {
                //     lastHideableMesh.visible = false;
                // }
                lastHideableMesh = undefined;
            };

            const mousedown = (canvasPos: number[]) => {
                if (!this._visible) {
                    return;
                }
                if (!grabbing) {
                    return;
                }
                //mouse down left
                if (!input.mouseDownLeft) {
                    return;
                }

                cameraControl.pointerEnabled = false;
                isMouseDown = true;
                dragAction = nextDragAction;
                lastCanvasPos[0] = canvasPos[0];
                lastCanvasPos[1] = canvasPos[1];
            };

            const mousemove = (canvasPos: number[]) => {
                if (!this._visible) {
                    return;
                }
                if (!isMouseDown) {
                    return;
                }
                console.log('dragAction:', dragAction);
                switch (dragAction) {
                    case DRAG_ACTIONS.xTranslate:
                        dragTranslateSectionPlane(xBaseAxis, lastCanvasPos, canvasPos);
                        break;
                    case DRAG_ACTIONS.yTranslate:
                        dragTranslateSectionPlane(yBaseAxis, lastCanvasPos, canvasPos);
                        break;
                    case DRAG_ACTIONS.zTranslate:
                        dragTranslateSectionPlane(zBaseAxis, lastCanvasPos, canvasPos);
                        break;
                    // case DRAG_ACTIONS.xRotate:
                    //     dragRotateSectionPlane(xBaseAxis, lastCanvasPos, canvasPos);
                    //     break;
                    case DRAG_ACTIONS.yRotate:
                        dragRotateSectionPlane(yBaseAxis, lastCanvasPos, canvasPos);
                        break;
                    // case DRAG_ACTIONS.zRotate:
                    //     dragRotateSectionPlane(zBaseAxis, lastCanvasPos, canvasPos);
                    //     break;
                }
                lastCanvasPos[0] = canvasPos[0];
                lastCanvasPos[1] = canvasPos[1];
            };

            const mouseup = () => {
                if (!this._visible) {
                    return;
                }
                if (!isMouseDown) {
                    return;
                }
                cameraControl.pointerEnabled = pointerEnabled;
                isMouseDown = false;
                grabbing = false;
            };

            this._cameraControlSubIds.push(cameraControl.on("hoverEnter", hoverEnter, this));
            this._cameraControlSubIds.push(cameraControl.on("hoverOff", hoverOff, this));
            this._inputSubIds.push(input.on("mousedown", mousedown, this));
            this._inputSubIds.push(input.on("mousemove", mousemove, this));
            this._inputSubIds.push(input.on("mouseup", mouseup, this));
        }
    }

    destroy() {
        this.unbindEvents();
        this.destroyNodes();
        this.destroyHtmlControls();
    }

    /**
     * Destroy HTML controls
     */
    private destroyHtmlControls(): void {
        this._htmlControls.forEach((controlDiv) => {
            if (controlDiv.parentNode) {
                controlDiv.parentNode.removeChild(controlDiv);
            }
        });
        this._htmlControls.clear();
    }

    private unbindEvents() {
        const viewer = this._viewer;
        const cameraControl = viewer.cameraControl;
        const input = viewer.scene.input;

        this._sceneSubIds.forEach((subId: number) => viewer.scene.off(subId));
        this._cameraControlSubIds.forEach((subId: number) => cameraControl.off(subId));
        this._inputSubIds.forEach((subId: number) => input.off(subId));
        this._sceneSubIds = [];
        this._cameraControlSubIds = [];
        this._inputSubIds = [];
    }

    private destroyNodes() {
        this._rootNode.destroy();
        this._meshes.clear();
        this._hideableMeshes.clear();
    }
}