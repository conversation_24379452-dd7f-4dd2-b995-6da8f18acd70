import { NavCubePlugin } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";

export class NavCube {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer = this._store.viewer!;
    navCubePlugin: NavCubePlugin | undefined;
    constructor() {
        this._viewer = this._store.viewer!;
        if (!this._viewer) {
            console.error("Viewer not initialized");
            return;
        }
        GlobalStore.getInstance().set("navCube", this);

        this.createCanvasCube();

    }

    createCanvasCube = () => {
        const canvasCube = document.createElement("canvas");
        this._store.containerCanvas?.appendChild(canvasCube);


        if (canvasCube) {
            canvasCube.style.position = "absolute";
            canvasCube.style.right = "-15px";
            canvasCube.style.bottom = "15px";
            canvasCube.style.width = "120px";
            canvasCube.style.height = "120px";
            canvasCube.style.zIndex = "2000"; // Đ<PERSON>m bảo nó nằm trên các thành phần khác

            this.navCubePlugin = new NavCubePlugin(this._viewer, {
                canvasId: `dpu-nav-cube-${this._store.containerId}`,
                canvasElement: canvasCube,
                color: "lightgray",
                id: `dpu-nav-cube-${this._store.containerId}`,
            })
        }


    }



}