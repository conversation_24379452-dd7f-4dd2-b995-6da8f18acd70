import { BoxControlV2, BoxSectionPlaneType } from "../../../widgets/section/BoxControl_v2";
import GlobalStore from "../../../core/globalStore/globalStore";
import { MathUtil } from "../../../utils/MathUtil";
import { buildBoxLinesGeometryFromAABB, Mesh, PhongMaterial, ReadableGeometry, SectionPlane, } from "@xeokit/xeokit-sdk";
import { createUUID } from "../../../utils/utils";



const AABB_MAGNIFICATAION = 1.1;

export class SectionBoxV2 {
    private _store = GlobalStore.getInstance().getAll();

    private _sectionPlaneMap = new Map<BoxSectionPlaneType, any>();
    private _active: boolean = true;
    private _aabb?: number[];
    private _control: BoxControlV2;
    private _generalId = createUUID();
    boxSection: Mesh | undefined;

    constructor() {

        this._control = new BoxControlV2(this);
        // this.initSectionBox();
    }


    // Changes the aabb range of box
    set aabb(value: number[]) {
        if (value[3] < value[0] || value[4] < value[1] || value[5] < value[2]) {
            return;
        }

        this._aabb = [...value];
        MathUtil.expandAABB(this._aabb, AABB_MAGNIFICATAION);

        this._createBoxMesh()
    }


    private _createBoxMesh = () => {
        const viewer = this._store.viewer;
        this._store.sectionSurfacePlanes?.sectionPlanes.setOverviewVisible(true);
        if (this.boxSection) {
            this.boxSection.geometry.destroy();
        }
        this.boxSection = new Mesh(viewer?.scene!, {
            geometry: new ReadableGeometry(viewer?.scene!, buildBoxLinesGeometryFromAABB({
                id: `section-box-${this._generalId}`,
                aabb: this._aabb,
            })),
            material: new PhongMaterial(viewer?.scene!, {
                emissive: [0, 1,],
                
            })
        });
        const active = this._active;
        const aabb = this._aabb as number[];
        const createSectionPlane = (id: BoxSectionPlaneType, pos: number[], dir: number[]) => {
            const plane = new SectionPlane(viewer!.scene, { id: id, pos: pos, dir: dir, active });
            this._sectionPlaneMap.set(id, plane);

        };

        createSectionPlane(BoxSectionPlaneType.RIGHT, [aabb[3], aabb[4], aabb[5]], [-1, 0, 0]); // positive x axis
        createSectionPlane(BoxSectionPlaneType.TOP, [aabb[3], aabb[4], aabb[5]], [0, -1, 0]); // positive y axis
        createSectionPlane(BoxSectionPlaneType.FRONT, [aabb[3], aabb[4], aabb[5]], [0, 0, -1]); // positive z axis
        createSectionPlane(BoxSectionPlaneType.LEFT, [aabb[0], aabb[1], aabb[2]], [1, 0, 0]); // negative x axis
        createSectionPlane(BoxSectionPlaneType.BOTTOM, [aabb[0], aabb[1], aabb[2]], [0, 1, 0]); // negative y axis
        createSectionPlane(BoxSectionPlaneType.BACK, [aabb[0], aabb[1], aabb[2]], [0, 0, 1]); // 

    }

}


