import { DialogGeneral } from "../../../widgets/dialog/dialog";
import GlobalStore from "../../../core/globalStore/globalStore";
import { getMessage } from "../../../language";
import { IconArrowTriangle } from "../../../utils/icon";
import { Property, PropertySet } from "@xeokit/xeokit-sdk";

export class DataProperties {
    private _store = GlobalStore.getInstance().getAll();
    private dialog: DialogGeneral | undefined;
    private onClosedCallback: ((status: boolean) => void) | undefined;
    private _showModal: boolean = false;

    constructor() {
        this.createModalDataElement()

        this.dialog?.showModal(false)
        GlobalStore.getInstance().set("dataProperties", this);

    }




    get showModal(): boolean {
        return this._showModal;
    }

    set showModal(show: boolean) {
        this._showModal = show;
        if (show) {
            // You can add logic when showing the modal here

        } else {
            // this.removeAllClickEvents();
        }
        this.dialog?.showModal(show);
    }

    // Gán callback từ lớp cha
    setClosedCallback(callback: (status: boolean) => void): void {
        this.onClosedCallback = callback;
    }


    notifyCallBack(status: boolean): void {
        if (this.onClosedCallback) {
            this.onClosedCallback(status); // Thông báo cho lớp cha
            if (!status) {

                // this.removeAllClickEvents();
            }
        } else {
            console.error("State change callback is not set.");
        }
    }

    private handleCloseModal = (status: boolean) => {
        if (!status) {
            this.notifyCallBack(status)

        }
    }
    createModalDataElement = () => {
        this.dialog = new DialogGeneral(
            this._store.containerId!,
            this.handleCloseModal
        );
        this.dialog.innerHeader = getMessage("element-properties-title-dialog");
        this.dialog.modal = false
        this.dialog.minimizeDialog = true;
        this.dialog.widthModal = "300px"
        this.dialog.heightModal = "500px"
        this.dialog.innerBody = this.renderTableData();
        this.dialog.rightModal = '42px'
        this.dialog.topModal = '5px'
        this.dialog.createDialog();
    }
    //#region clearContainer
    private clearContainer = () => {
        if (this.containerDiv) {
            this.containerDiv.innerHTML = '';
        }
    }

    private containerDiv: HTMLDivElement | undefined
    // renderTableData = () => {

    //     if (this.containerDiv) {
    //         this.clearContainer()
    //     } else {
    //         this.containerDiv = document.createElement('div')
    //         this.containerDiv.className = 'dpu-table-container';
    //     }

    //     const propertiesSet = this._store.viewer?.scene.selectedObjectIds
    //     if (propertiesSet === undefined || propertiesSet.length === 0) {
    //         // Trường hợp không có dữ liệu
    //         const noDataDiv = document.createElement('div');
    //         noDataDiv.className = 'no-data';
    //         noDataDiv.textContent = getMessage("noData");
    //         this.containerDiv.appendChild(noDataDiv);
    //         return this.containerDiv;
    //     }


    //     propertiesSet.forEach((id) => {

    //         const masterNode = this._createMasterNode(id);
    //         this.containerDiv!.appendChild(masterNode);




    //     })
    //     return this.containerDiv;
    // }
    // //#region _createMasterNode
    // private _createMasterNode = (id: string) => {
    //     const pluginTree = this._store.treeLayerModel?.treeViewPlugin;
    //     const nodeName = (pluginTree as any)._objectNodes[id];

    //     const masterNodeRow = document.createElement('div');
    //     masterNodeRow.className = 'dpu-container-node-master-row hidden';
    //     const row = document.createElement('div');

    //     row.className = 'dpu-row-table node';
    //     row.innerHTML = `
    //             <div class="content-key">${nodeName.title}</div><div class ="dpu-arrow">${IconArrowTriangle()}</div>
    //             `;
    //     row.setAttribute('row-target-collapse', nodeName.nodeId);

    //     masterNodeRow.appendChild(row);
    //     // const containerTable = document.createElement('div');
    //     // containerTable.className = 'container-table';
    //     // // Tạo bảng
    //     // const table = document.createElement('table');
    //     // table.className = 'info-table';

    //     // // Tạo tbody
    //     // const tbody = document.createElement('tbody');
    //     const metaObject = this._store.viewer?.metaScene.metaObjects[id];
    //     const propertySets = metaObject?.propertySets;
    //     propertySets?.forEach((propertySet) => {
    //         if (!propertySet) return;
    //         const masterRowProperty = this._createMasterRow(propertySet)
    //         masterNodeRow.appendChild(masterRowProperty);

    //     })

    //     row.addEventListener('click', () => {
    //         // Kiểm tra xem dòng có lớp "open" hay không và thay đổi
    //         masterNodeRow.classList.toggle('hidden');


    //     });
    //     // containerTable.appendChild(table);
    //     // table.appendChild(tbody);
    //     // masterRow.appendChild(containerTable);


    //     return masterNodeRow


    // }

    // //#region _createMasterRow
    // private _createMasterRow = (propertySet: PropertySet) => {
    //     const masterRow = document.createElement('div');
    //     masterRow.className = 'dpu-container-master-row';
    //     const row = document.createElement('div');

    //     row.className = 'dpu-row-table';
    //     row.innerHTML = `
    //             <div class="content-key">${propertySet.name}</div><div class ="dpu-arrow">${IconArrowTriangle()}</div>
    //             `;
    //     row.setAttribute('row-target-collapse', propertySet.id);
    //     // Thêm sự kiện click để thay đổi lớp "open"
    //     row.addEventListener('click', () => {
    //         // Kiểm tra xem dòng có lớp "open" hay không và thay đổi
    //         masterRow.classList.toggle('open');
    //         if (masterRow.classList.contains('open')) {
    //             table.style.display = "none"
    //         } else {
    //             table.style.removeProperty('display')
    //         }

    //     });
    //     masterRow.appendChild(row);
    //     const containerTable = document.createElement('div');
    //     containerTable.className = 'container-table';
    //     // Tạo bảng
    //     const table = document.createElement('table');
    //     table.className = 'info-table';

    //     // Tạo tbody
    //     const tbody = document.createElement('tbody');
    //     propertySet.properties.forEach((property) => {
    //         tbody.appendChild(this._createRow(property));
    //     })

    //     containerTable.appendChild(table);
    //     table.appendChild(tbody);
    //     masterRow.appendChild(containerTable);


    //     return masterRow

    // }

    // private _createRow = (property: Property) => {
    //     const row = document.createElement('tr');
    //     // Cột key
    //     const keyCell = document.createElement('td');
    //     keyCell.className = 'content-td content-td-key';
    //     keyCell.innerHTML = `<span class="key">${property.name}</span>`;
    //     row.appendChild(keyCell);

    //     // Cột value
    //     const valueCell = document.createElement('td');
    //     valueCell.className = 'content-td content-td-value';
    //     valueCell.innerHTML = `<span class="value">${property.value ? this._processValue(property.value, property.valueType) : ""}</span>`;

    //     // valueCell.textContent = this._processValue(item.value, item.valueType);
    //     row.appendChild(valueCell);

    //     return row;
    // }
    renderTableData = () => {

        if (this.containerDiv) {
            this.clearContainer()
        } else {
            this.containerDiv = document.createElement('div')
            this.containerDiv.className = 'dpu-table-container';
        }

        const propertiesSet = this._store.viewer?.scene.selectedObjectIds
        if (propertiesSet === undefined || propertiesSet.length === 0) {
            // Trường hợp không có dữ liệu
            const noDataDiv = document.createElement('div');
            noDataDiv.className = 'no-data';
            noDataDiv.textContent = getMessage("noData");
            this.containerDiv.appendChild(noDataDiv);
            return this.containerDiv;
        }
        if (this._store.viewer!.scene.selectedObjectIds.length > 1) {
            // Trường hợp không có dữ liệu
            const chooseElement = document.createElement('div');
            chooseElement.className = 'no-data';
            chooseElement.textContent = 'Chỉ chọn 1 phần tử để xem thông tin';
            this.containerDiv.appendChild(chooseElement);
            return this.containerDiv;
        }


        propertiesSet.forEach((id) => {
            const metaObject = this._store.viewer?.metaScene.metaObjects[id];
            const propertySets = metaObject?.propertySets;
            // const masterNode = this._createMasterNode(id);
            // this.containerDiv!.appendChild(masterNode);

            propertySets?.forEach((propertySet) => {
                if (!propertySet) return;
                const masterRowProperty = this._createMasterRow(propertySet)
                this.containerDiv!.appendChild(masterRowProperty);

            })


        })
        return this.containerDiv;
    }
    //#region _createMasterNode
    private _createMasterNode = (id: string) => {
        const pluginTree = this._store.treeLayerModel?.treeViewPlugin;
        const nodeName = (pluginTree as any)._objectNodes[id];

        const masterNodeRow = document.createElement('div');
        masterNodeRow.className = 'dpu-container-node-master-row hidden';
        const row = document.createElement('div');

        row.className = 'dpu-row-table node';
        row.innerHTML = `
                <div class="content-key">${nodeName.title}</div><div class ="dpu-arrow">${IconArrowTriangle()}</div>
                `;
        row.setAttribute('row-target-collapse', nodeName.nodeId);

        masterNodeRow.appendChild(row);
        // const containerTable = document.createElement('div');
        // containerTable.className = 'container-table';
        // // Tạo bảng
        // const table = document.createElement('table');
        // table.className = 'info-table';

        // // Tạo tbody
        // const tbody = document.createElement('tbody');
        const metaObject = this._store.viewer?.metaScene.metaObjects[id];
        const propertySets = metaObject?.propertySets;
        propertySets?.forEach((propertySet) => {
            if (!propertySet) return;
            const masterRowProperty = this._createMasterRow(propertySet)
            masterNodeRow.appendChild(masterRowProperty);

        })

        row.addEventListener('click', () => {
            // Kiểm tra xem dòng có lớp "open" hay không và thay đổi
            masterNodeRow.classList.toggle('hidden');


        });
        // containerTable.appendChild(table);
        // table.appendChild(tbody);
        // masterRow.appendChild(containerTable);


        return masterNodeRow


    }

    //#region _createMasterRow
    private _createMasterRow = (propertySet: PropertySet) => {
        const masterRow = document.createElement('div');
        masterRow.className = 'dpu-container-master-row';
        const row = document.createElement('div');

        row.className = 'dpu-row-table';
        row.innerHTML = `
                <div class="content-key">${propertySet.name}</div><div class ="dpu-arrow">${IconArrowTriangle()}</div>
                `;
        row.setAttribute('row-target-collapse', propertySet.id);
        // Thêm sự kiện click để thay đổi lớp "open"
        row.addEventListener('click', () => {
            // Kiểm tra xem dòng có lớp "open" hay không và thay đổi
            masterRow.classList.toggle('open');
            if (masterRow.classList.contains('open')) {
                table.style.display = "none"
            } else {
                table.style.removeProperty('display')
            }

        });
        masterRow.appendChild(row);
        const containerTable = document.createElement('div');
        containerTable.className = 'container-table';
        // Tạo bảng
        const table = document.createElement('table');
        table.className = 'info-table';

        // Tạo tbody
        const tbody = document.createElement('tbody');
        propertySet.properties.forEach((property) => {
            tbody.appendChild(this._createRow(property));
        })

        containerTable.appendChild(table);
        table.appendChild(tbody);
        masterRow.appendChild(containerTable);


        return masterRow

    }

    private _createRow = (property: Property) => {
        const row = document.createElement('tr');
        // Cột key
        const keyCell = document.createElement('td');
        keyCell.className = 'content-td content-td-key';
        keyCell.innerHTML = `<span class="key">${property.name}</span>`;
        row.appendChild(keyCell);

        // Cột value
        const valueCell = document.createElement('td');
        valueCell.className = 'content-td content-td-value';
        valueCell.innerHTML = `<span class="value">${property.value ? this._processValue(property.value, property.valueType) : ""}</span>`;

        // valueCell.textContent = this._processValue(item.value, item.valueType);
        row.appendChild(valueCell);

        return row;
    }




    //#region _processValue
    private _processValue = (value: string, valueType: string | number): string => {

        if (valueType === "Double") {
            const values = value.split(", ");
            const roundedValues = values.map(v => {
                const floatValue = parseFloat(v);
                // Kiểm tra phần thập phân
                const decimalPlaces = (v.split('.')[1] || '').length;

                if (decimalPlaces === 0) {
                    return v; // Nếu không có phần thập phân, giữ nguyên
                }

                return floatValue.toFixed(3); // Nếu có phần thập phân, làm tròn lên 3 chữ số
            });
            return roundedValues.join(", ");
        }
        return value;
    }


}