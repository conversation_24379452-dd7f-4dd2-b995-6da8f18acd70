import { fileURLToPath } from 'url';
import path from 'path';
import ProgressBarPlugin from 'progress-bar-webpack-plugin';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Custom plugin để hiển thị thông tin thời gian build chi tiết
class BuildTimePlugin {
  constructor() {
    this.startTime = null;
    this.buildSteps = [];
  }

  formatTime(ms) {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  }

  formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  }

  apply(compiler) {
    compiler.hooks.compile.tap('BuildTimePlugin', () => {
      this.startTime = Date.now();
      const startTimeString = this.formatDateTime(this.startTime);

      console.log(chalk.blue('═'.repeat(60)));
      console.log(chalk.blue.bold('🚀 BUILD STARTED'));
      console.log(chalk.blue('═'.repeat(60)));
      console.log(chalk.cyan('📅 Start time: ') + chalk.yellow(startTimeString));
      console.log(chalk.cyan('📦 Project: ') + chalk.white('TypeScript Xeokit Viewer'));
      console.log(chalk.cyan('🔧 Mode: ') + chalk.white('Production'));
      console.log(chalk.blue('─'.repeat(60)));
    });

    // Track compilation progress
    compiler.hooks.compilation.tap('BuildTimePlugin', (compilation) => {
      compilation.hooks.buildModule.tap('BuildTimePlugin', (module) => {
        if (module.resource && module.resource.includes('.ts')) {
          this.buildSteps.push({
            step: 'TypeScript compilation',
            file: module.resource.split('\\').pop() || module.resource.split('/').pop(),
            time: Date.now()
          });
        }
      });
    });

    compiler.hooks.done.tap('BuildTimePlugin', (stats) => {
      const endTime = Date.now();
      const buildTime = endTime - this.startTime;
      const endTimeString = this.formatDateTime(endTime);

      console.log(chalk.green('═'.repeat(60)));
      if (stats.hasErrors()) {
        console.log(chalk.red.bold('❌ BUILD FAILED'));
      } else if (stats.hasWarnings()) {
        console.log(chalk.yellow.bold('⚠️  BUILD COMPLETED WITH WARNINGS'));
      } else {
        console.log(chalk.green.bold('✅ BUILD COMPLETED SUCCESSFULLY'));
      }
      console.log(chalk.green('═'.repeat(60)));

      console.log(chalk.cyan('⏰ Started: ') + chalk.yellow(this.formatDateTime(this.startTime)));
      console.log(chalk.cyan('🏁 Finished: ') + chalk.yellow(endTimeString));
      console.log(chalk.cyan('⚡ Duration: ') + chalk.magenta(this.formatTime(buildTime)));

      // Hiển thị thống kê
      const compilation = stats.compilation;
      const assets = compilation.getAssets();
      const mainAsset = assets.find(asset => asset.name === 'index.js');

      if (mainAsset) {
        const sizeInKB = (mainAsset.info.size / 1024).toFixed(2);
        console.log(chalk.cyan('📦 Bundle size: ') + chalk.white(`${sizeInKB} KB`));
      }

      console.log(chalk.cyan('📊 Files processed: ') + chalk.white(Object.keys(compilation.modules).length));

      if (stats.hasErrors()) {
        console.log(chalk.red('🚨 Errors: ') + chalk.white(stats.compilation.errors.length));
      }

      if (stats.hasWarnings()) {
        console.log(chalk.yellow('⚠️  Warnings: ') + chalk.white(stats.compilation.warnings.length));
      }

      console.log(chalk.green('═'.repeat(60)));
      console.log('\n');
    });
  }
}


export default {
  entry: './src/index.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: {
      type: 'module'
    }
  },
  experiments: {
    outputModule: true, // Enable ESM
    asyncWebAssembly: true, // Enable WASM support
  },
  resolve: {
    mainFiles: ["index"],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'ifcdb.wasm': path.resolve(__dirname, 'node_modules/@xeokit/xeokit-sdk/dist/web-ifc.wasm')
    },
    extensions: ['.ts', '.js', '.css', '.glb', '.wasm'],
    fallback: {
      "path": "path-browserify",
      "fs": false, // fs is not available in browser, disable it
      "crypto": "crypto-browserify",
      "stream": "stream-browserify",
      "buffer": "buffer",
      "util": "util",
      "assert": "assert",
      "url": "url",
      "querystring": "querystring-es3",
      "os": "os-browserify/browser",
      "https": "https-browserify",
      "http": "stream-http",
      "zlib": "browserify-zlib",
      "vm": "vm-browserify"
    }
  },
  module: {
    rules: [
      {
        test: /\.css$/i, // Add rule for processing CSS
        use: ['style-loader', 'css-loader'], // style-loader inserts CSS into DOM, css-loader processes CSS files
      },
      {
        test: /\.(png|gif|jpg|jpeg|svg|xml)$/,
        type: "asset/inline",
      },
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.worker\.js$/,
        use: { loader: 'worker-loader' }
      },
      {
        test: /\.json$/i,
        type: 'json', // Properly handle JSON files without converting to base64
      },
      {
        test: /\.glb$/i, // Define rule for `.glb` files
        use: [
          {
            loader: 'file-loader', // Use file-loader for .glb files
            options: {
              name: 'assets/glb/[name].[ext]',
            },
          },
        ],
      },
      {
        test: /\.wasm$/,
        type: 'asset/resource',
        generator: {
          filename: 'wasm/[name][ext]'
        }
      },
    ]
  },

  mode: 'production',
  devtool: 'source-map',

  plugins: [
    new ProgressBarPlugin({
      format: '  Build [:bar] ' + chalk.green.bold(':percent') + ' (:elapsed seconds)',
      clear: false,
      summary: false
    }),
    new BuildTimePlugin()
  ]
};