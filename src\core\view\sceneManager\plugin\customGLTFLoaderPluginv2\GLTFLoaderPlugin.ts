import { SceneModel, utils,Plugin } from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js"
import { GLTFSceneModelLoader } from "./GLTFSceneModelLoader";
import { GLTFDefaultDataSource } from "./GLTFDefaultDataSource";
import {IFCObjectDefaults} from "@xeokit/xeokit-sdk/src/viewer/metadata/IFCObjectDefaults.js"



interface GLTFLoaderPluginConfig {
    id?: string;
    objectDefaults?: { [key: string]: any };
    dataSource?: any;
    [key: string]: any;
}

interface LoadParams {
    id?: string;
    src?: string;
    gltf?: any;
    metaModelSrc?: string;
    metaModelJSON?: any;
    objectDefaults?: { [key: string]: any };
    edges?: boolean;
    origin?: number[];
    position?: number[];
    scale?: number[];
    rotation?: number[];
    matrix?: number[];
    saoEnabled?: boolean;
    pbrEnabled?: boolean;
    colorTextureEnabled?: boolean;
    backfaces?: boolean;
    edgeThreshold?: number;
    dtxEnabled?: boolean;
    autoMetaModel?: boolean;
    globalizeObjectIds?: boolean;
    entityId?: string;
    [key: string]: any;
}

/**
 * {@link Viewer} plugin that loads models from [glTF](https://www.khronos.org/gltf/).
 *
 * * Loads all glTF formats, including embedded and binary formats.
 * * Loads physically-based materials and textures.
 * * Creates an {@link Entity} representing each model it loads, which will have {@link Entity#isModel} set ````true```` and will be registered by {@link Entity#id} in {@link Scene#models}.
 * * Creates an {@link Entity} for each object within the model, which is indicated by each glTF ````node```` that````node```` that````node```` that````node```` that````node```` that````node```` that````node```` that has a ````name```` attribute. Those Entities will have {@link Entity#isObject} set ````true```` and will be registered by {@link Entity#id} in {@link Scene#objects}.
 * * When loading, can set the World-space position, scale and rotation of each model within World space, along with initial properties for all the model's {@link Entity}s.
 * * Not recommended for large models. For best performance with large glTF datasets, we recommend first converting them
 * to ````.xkt```` format (eg. using [convert2xkt](https://github.com/xeokit/xeokit-convert)), then loading
 * the ````.xkt```` using {@link XKTLoaderPlugin}.
 *
 * ## Metadata
 *
 * GLTFLoaderPlugin can also load an accompanying JSON metadata file with each model, which creates a {@link MetaModel} corresponding
 * to the model {@link Entity} and a {@link MetaObject} corresponding to each object {@link Entity}.
 *
 * Each {@link MetaObject} has a {@link MetaObject#type}, which indicates the classification of its corresponding {@link Entity}. When loading
 * metadata, we can also provide GLTFLoaderPlugin with a custom lookup table of initial values to set on the properties of each type of {@link Entity}. By default, GLTFLoaderPlugin
 * uses its own map of default colors and visibilities for IFC element types.
 *
 * @class GLTFLoaderPlugin
 */
class GLTFLoaderPlugin extends Plugin {
    private _sceneModelLoader: GLTFSceneModelLoader;
    private _dataSource: any;
    private _objectDefaults: { [key: string]: any; } | undefined;

    /**
     * @constructor
     *
     * @param {Viewer} viewer The Viewer.
     * @param {Object} cfg  Plugin configuration.
     * @param {String} [cfg.id="GLTFLoader"] Optional ID for this plugin, so that we can find it within {@link Viewer#plugins}.
     * @param {Object} [cfg.objectDefaults] Map of initial default states for each loaded {@link Entity} that represents an object.  Default value is {@link IFCObjectDefaults}.
     * @param {Object} [cfg.dataSource] A custom data source through which the GLTFLoaderPlugin can load metadata, glTF and binary attachments. Defaults to an instance of {@link GLTFDefaultDataSource}, which loads over HTTP.
     */
    constructor(viewer: any, cfg: GLTFLoaderPluginConfig = {}) {

        super("GLTFLoader", viewer, cfg);
        this._sceneModelLoader = new GLTFSceneModelLoader(this);

        this.dataSource = cfg.dataSource;
        this.objectDefaults = cfg.objectDefaults || IFCObjectDefaults;
    }

    /**
     * Sets a custom data source through which the GLTFLoaderPlugin can load metadata, glTF and binary attachments.
     *
     * Default value is {@link GLTFDefaultDataSource}, which loads via an XMLHttpRequest.
     *
     * @type {Object}
     */
    set dataSource(value: any) {
        this._dataSource = value || new GLTFDefaultDataSource();
    }

    /**
     * Gets the custom data source through which the GLTFLoaderPlugin can load metadata, glTF and binary attachments.
     *
     * Default value is {@link GLTFDefaultDataSource}, which loads via an XMLHttpRequest.
     *
     * @type {Object}
     */
    get dataSource(): any {
        return this._dataSource;
    }

    /**
     * Sets map of initial default states for each loaded {@link Entity} that represents an object.
     *
     * Default value is {@link IFCObjectDefaults}.
     *
     * @type {{String: Object}}
     */
    set objectDefaults(value: { [key: string]: any }) {
        this._objectDefaults = value || IFCObjectDefaults;
    }

    /**
     * Gets map of initial default states for each loaded {@link Entity} that represents an object.
     *
     * Default value is {@link IFCObjectDefaults}.
     *
     * @type {{String: Object}}
     */
    get objectDefaults(): { [key: string]: any } {
        return this._objectDefaults || IFCObjectDefaults;
    }

    /**
     * Loads a glTF model from a file into this GLTFLoaderPlugin's {@link Viewer}.
     *
     * @param {*} params Loading parameters.
     * @param {String} [params.id] ID to assign to the root {@link Entity#id}, unique among all components in the Viewer's {@link Scene}, generated automatically by default.
     * @param {String} [params.src] Path to a glTF file, as an alternative to the ````gltf```` parameter.
     * @param {*} [params.gltf] glTF JSON, as an alternative to the ````src```` parameter.
     * @param {String} [params.metaModelSrc] Path to an optional metadata file, as an alternative to the ````metaModelJSON```` parameter.
     * @param {*} [params.metaModelJSON] JSON model metadata, as an alternative to the ````metaModelSrc```` parameter.
     * @param {{String:Object}} [params.objectDefaults] Map of initial default states for each loaded {@link Entity} that represents an object. Default value is {@link IFCObjectDefaults}.
     * @param {Boolean} [params.edges=false] Whether or not xeokit renders the model with edges emphasized.
     * @param {Number[]} [params.origin=[0,0,0]] The double-precision World-space origin of the model's coordinates.
     * @param {Number[]} [params.position=[0,0,0]] The single-precision position, relative to ````origin````.
     * @param {Number[]} [params.scale=[1,1,1]] The model's scale.
     * @param {Number[]} [params.rotation=[0,0,0]] The model's orientation, as Euler angles given in degrees, for each of the X, Y and Z axis.
     * @param {Number[]} [params.matrix=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]] The model's world transform matrix. Overrides the position, scale and rotation parameters. Relative to ````origin````.
     * @param {Boolean} [params.saoEnabled=true] Indicates if Scalable Ambient Obscurance (SAO) is enabled for the model. SAO is configured by the Scene's {@link SAO} component. Only works when {@link SAO#enabled} is also ````true````
     * @param {Boolean} [params.pbrEnabled=true] Indicates if physically-based rendering (PBR) is enabled for the model. Overrides ````colorTextureEnabled````. Only works when {@link Scene#pbrEnabled} is also ````true````.
     * @param {Boolean} [params.colorTextureEnabled=true] Indicates if base color texture rendering is enabled for the model. Overridden by ````pbrEnabled````.  Only works when {@link Scene#colorTextureEnabled} is also ````true````.
     * @param {Boolean} [params.backfaces=true] When true, always show backfaces, even on objects for which the glTF material is single-sided. When false, only show backfaces on geometries whenever the glTF material is double-sided.
     * @param {Number} [params.edgeThreshold=10] When xraying, highlighting, selecting or edging, this is the threshold angle between normals of adjacent triangles, below which their shared wireframe edge is not drawn.
     * @param {Boolean} [params.dtxEnabled=true] When ````true```` (default) use data textures (DTX), where appropriate, to
     * represent the returned model. Set false to always use vertex buffer objects (VBOs). Note that DTX is only applicable
     * to non-textured triangle meshes, and that VBOs are always used for meshes that have textures, line segments, or point
     * primitives. Only works while {@link DTX#enabled} is also ````true````.
     * @param {Boolean} [params.autoMetaModel] When supplied, creates a default MetaModel with a single MetaObject.
     * @param {Boolean} [params.globalizeObjectIds=false] Indicates whether to globalize each {@link Entity#id} and {@link MetaObject#id}, in case you need to prevent ID clashes with other models.
     * @returns {Entity} Entity representing the model, which will have {@link Entity#isModel} set ````true```` and will be registered by {@link Entity#id} in {@link Scene#models}
     */
    load(params: LoadParams = {}): any {
        console.log("params", params);
        if (params.id && (this.viewer as any).scene.components[params.id]) {
            this.error("Component with this ID already exists in viewer: " + params.id + " - will autogenerate this ID");
            delete params.id;
        }
        const sceneModel = new SceneModel(this.viewer.scene, utils.apply(params, {
            isModel: true,
            dtxEnabled: params.dtxEnabled
        }));

        const modelId: string = sceneModel.id;  // In case ID was auto-generated

        if (!params.src && !params.gltf) {
            this.error("load() param expected: src or gltf");
            return sceneModel; // Return new empty model
        }

        if (params.metaModelSrc || params.metaModelJSON) {
            const processMetaModelJSON = (metaModelJSON: any): void => {
                this.viewer.metaScene.createMetaModel(modelId, metaModelJSON, {});
                (this.viewer as any).scene.canvas.spinner.processes--;
                if (params.src) {
                    this._sceneModelLoader.load(this, params.src, metaModelJSON, params, sceneModel);
                } else {
                    this._sceneModelLoader.parse(this, params.gltf, metaModelJSON, params, sceneModel);
                }
            };
            if (params.metaModelSrc) {
                const metaModelSrc: string = params.metaModelSrc;
                (this.viewer as any).scene.canvas.spinner.processes++;

                this._dataSource.getMetaModel(metaModelSrc, (metaModelJSON: any) => {
                    (this.viewer as any).scene.canvas.spinner.processes--;
                    processMetaModelJSON(metaModelJSON);
                }, (errMsg: string) => {
                    this.error(`load(): Failed to load model metadata for model '${modelId} from  '${metaModelSrc}' - ${errMsg}`);
                    (this.viewer as any).scene.canvas.spinner.processes--;
                });

            } else if (params.metaModelJSON) {
                processMetaModelJSON(params.metaModelJSON);
            }
        } else {
            if (params.src) {
                this._sceneModelLoader.load(this, params.src, null, params, sceneModel);
            } else {
                this._sceneModelLoader.parse(this, params.gltf, null, params, sceneModel);
            }
        }

        sceneModel.once("destroyed", () => {
            this.viewer.metaScene.destroyMetaModel(modelId);
        });

        return sceneModel;
    }

    /**
     * Destroys this GLTFLoaderPlugin.
     */
    destroy(): void {
        super.destroy();
    }
}

export { GLTFLoaderPlugin }