import { Viewer } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";
import logoDPU from '../../../assets/images/dpu_logo2.png'
export class FooterView {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer: Viewer = this._store.viewer!;

    constructor() {
        this._viewer = this._store.viewer!;
        if (!this._viewer) {
            console.error("Viewer not initialized");
            return;
        }
        this.createFooter();
    }

    private createFooter = () => {
        const viewContainer = this._store.containerCanvas;
        const footerInfoContainer = document.createElement('div');
        footerInfoContainer.classList.add('footer-info-container');
        viewContainer?.appendChild(footerInfoContainer);

        // Tạo các phần tử con bên trong footer
        const footerImg = document.createElement('div');
        footerImg.classList.add('footer-info-content');

        footerImg.innerHTML = `
                <img src="${logoDPU}" alt="DP-Unity"/>
            `
        footerInfoContainer.appendChild(footerImg);
    }
}