import {
    utils
} from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";
import {
    core
} from "@xeokit/xeokit-sdk/src/viewer/scene/core.js";
interface GLTFDefaultDataSourceConfig {
    cacheBuster?: boolean;
}

type CallbackFunction<T> = (data: T) => void;
type ErrorCallback = (error: string) => void;

/**
 * Default data access strategy for {@link GLTFLoaderPlugin}.
 *
 * This just loads assets using XMLHttpRequest.
 */
class GLTFDefaultDataSource {
    private cacheBuster: boolean;

    constructor(cfg: GLTFDefaultDataSourceConfig = {}) {
        this.cacheBuster = (cfg.cacheBuster !== false);
    }

    private _cacheBusterURL(url: string): string {
        if (!this.cacheBuster) {
            return url;
        }
        const timestamp = new Date().getTime();
        if (url.indexOf('?') > -1) {
            return url + '&_=' + timestamp;
        } else {
            return url + '?_=' + timestamp;
        }
    }

    /**
     * Gets metamodel JSON.
     *
     * @param {String|Number} metaModelSrc Identifies the metamodel JSON asset.
     * @param {Function} ok Fired on successful loading of the metamodel JSON asset.
     * @param {Function} error Fired on error while loading the metamodel JSON asset.
     */
    getMetaModel(metaModelSrc: string | number, ok: CallbackFunction<any>, error: ErrorCallback): void {
        if (typeof metaModelSrc === "object") {
            // Nếu đã là object (JSON), trả về luôn
            ok(metaModelSrc);
            return;
        }
        utils.loadJSON(this._cacheBusterURL(metaModelSrc.toString()),
            (json: any) => {
                ok(json);
            },
            function (errMsg: string) {
                error(errMsg);
            });
    }

    /**
     * Gets glTF JSON.
     *
     * @param {String|Number} glTFSrc Identifies the glTF JSON asset.
     * @param {Function} ok Fired on successful loading of the glTF JSON asset.
     * @param {Function} error Fired on error while loading the glTF JSON asset.
     */
    getGLTF(glTFSrc: string | number, ok: CallbackFunction<ArrayBuffer>, error: ErrorCallback): void {
        utils.loadArraybuffer(this._cacheBusterURL(glTFSrc.toString()),
            (gltf: ArrayBuffer) => {
                ok(gltf);
            },
            function (errMsg: string) {
                error(errMsg);
            });
    }

    /**
     * Gets binary glTF file.
     *
     * @param {String|Number} glbSrc Identifies the .glb asset.
     * @param {Function} ok Fired on successful loading of the .glb asset.
     * @param {Function} error Fired on error while loading the .glb asset.
     */
    getGLB(glbSrc: string | number, ok: CallbackFunction<ArrayBuffer>, error: ErrorCallback): void {
        utils.loadArraybuffer(this._cacheBusterURL(glbSrc.toString()),
            (arraybuffer: ArrayBuffer) => {
                ok(arraybuffer);
            },
            function (errMsg: string) {
                error(errMsg);
            });
    }

    /**
     * Gets glTF binary attachment.
     *
     * Note that this method requires the source of the glTF JSON asset. This is because the binary attachment
     * source could be relative to the glTF source, IE. it may not be a global ID.
     *
     * @param {String|Number} glTFSrc Identifies the glTF JSON asset.
     * @param {String|Number} binarySrc Identifies the glTF binary asset.
     * @param {Function} ok Fired on successful loading of the glTF binary asset.
     * @param {Function} error Fired on error while loading the glTF binary asset.
     */
    getArrayBuffer(glTFSrc: string | number, binarySrc: string | number, ok: CallbackFunction<ArrayBuffer>, error: ErrorCallback): void {
        loadArraybuffer(this._cacheBusterURL(glTFSrc.toString()), binarySrc.toString(),
            (arrayBuffer: ArrayBuffer) => {
                ok(arrayBuffer);
            },
            function (errMsg: string) {
                error(errMsg);
            });
    }
}

function loadArraybuffer(glTFSrc: string, binarySrc: string, ok: CallbackFunction<ArrayBuffer>, err: ErrorCallback): void {
    // Check for data: URI
    const defaultCallback = (): void => {
    };
    ok = ok || defaultCallback;
    err = err || defaultCallback;
    const dataUriRegex = /^data:(.*?)(;base64)?,(.*)$/;
    const dataUriRegexResult = binarySrc.match(dataUriRegex);
    if (dataUriRegexResult) { // Safari can't handle data URIs through XMLHttpRequest
        const isBase64 = !!dataUriRegexResult[2];
        let data = dataUriRegexResult[3];
        data = window.decodeURIComponent(data);
        if (isBase64) {
            data = window.atob(data);
        }
        try {
            const buffer = new ArrayBuffer(data.length);
            const view = new Uint8Array(buffer);
            for (let i = 0; i < data.length; i++) {
                view[i] = data.charCodeAt(i);
            }
            core.scheduleTask(function () {
                ok(buffer);
            });
        } catch (error: any) {
            core.scheduleTask(function () {
                err(error.message || error.toString());
            });
        }
    } else {
        const basePath = getBasePath(glTFSrc);
        const url = basePath + binarySrc;
        const request = new XMLHttpRequest();
        request.open('GET', url, true);
        request.responseType = 'arraybuffer';
        request.onreadystatechange = function () {
            if (request.readyState === 4) {
                if (request.status === 200) {
                    ok(request.response);
                } else {
                    err('loadArrayBuffer error : ' + request.response);
                }
            }
        };
        request.send(null);
    }
}

function getBasePath(src: string): string {
    const i = src.lastIndexOf("/");
    return (i !== 0) ? src.substring(0, i + 1) : "";
}

export { GLTFDefaultDataSource };