import { fileURLToPath } from 'url';
import path from 'path';
import ProgressBarPlugin from 'progress-bar-webpack-plugin';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Enhanced Build Time Plugin for Development
class EnhancedBuildTimePlugin {
  constructor() {
    this.startTime = null;
    this.buildSteps = [];
    this.moduleCount = 0;
  }

  formatTime(ms) {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(0);
    return `${minutes}m ${seconds}s`;
  }

  formatDateTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  apply(compiler) {
    compiler.hooks.compile.tap('EnhancedBuildTimePlugin', () => {
      this.startTime = Date.now();
      this.moduleCount = 0;
      const startTimeString = this.formatDateTime(this.startTime);
      
      console.log(chalk.blue('╔' + '═'.repeat(70) + '╗'));
      console.log(chalk.blue('║') + chalk.blue.bold('  🚀 DEVELOPMENT BUILD STARTED'.padEnd(68)) + chalk.blue('║'));
      console.log(chalk.blue('╠' + '═'.repeat(70) + '╣'));
      console.log(chalk.blue('║') + chalk.cyan('  📅 Start time: ').padEnd(20) + chalk.yellow(startTimeString).padEnd(50) + chalk.blue('║'));
      console.log(chalk.blue('║') + chalk.cyan('  📦 Project: ').padEnd(20) + chalk.white('TypeScript Xeokit Viewer').padEnd(50) + chalk.blue('║'));
      console.log(chalk.blue('║') + chalk.cyan('  🔧 Mode: ').padEnd(20) + chalk.white('Development').padEnd(50) + chalk.blue('║'));
      console.log(chalk.blue('║') + chalk.cyan('  🎯 Target: ').padEnd(20) + chalk.white('ES Module').padEnd(50) + chalk.blue('║'));
      console.log(chalk.blue('╚' + '═'.repeat(70) + '╝'));
      console.log('');
    });

    // Track module compilation
    compiler.hooks.compilation.tap('EnhancedBuildTimePlugin', (compilation) => {
      compilation.hooks.buildModule.tap('EnhancedBuildTimePlugin', (module) => {
        this.moduleCount++;
        if (module.resource) {
          const fileName = module.resource.split('\\').pop() || module.resource.split('/').pop();
          if (fileName.endsWith('.ts') || fileName.endsWith('.js')) {
            process.stdout.write(chalk.gray(`  📄 Compiling: ${fileName}\r`));
          }
        }
      });
    });

    compiler.hooks.done.tap('EnhancedBuildTimePlugin', (stats) => {
      const endTime = Date.now();
      const buildTime = endTime - this.startTime;
      const endTimeString = this.formatDateTime(endTime);
      
      // Clear the last line
      process.stdout.write('\r' + ' '.repeat(80) + '\r');
      
      console.log(chalk.green('╔' + '═'.repeat(70) + '╗'));
      
      if (stats.hasErrors()) {
        console.log(chalk.green('║') + chalk.red.bold('  ❌ BUILD FAILED'.padEnd(68)) + chalk.green('║'));
      } else if (stats.hasWarnings()) {
        console.log(chalk.green('║') + chalk.yellow.bold('  ⚠️  BUILD COMPLETED WITH WARNINGS'.padEnd(68)) + chalk.green('║'));
      } else {
        console.log(chalk.green('║') + chalk.green.bold('  ✅ BUILD COMPLETED SUCCESSFULLY'.padEnd(68)) + chalk.green('║'));
      }
      
      console.log(chalk.green('╠' + '═'.repeat(70) + '╣'));
      
      // Build timing information
      console.log(chalk.green('║') + chalk.cyan('  ⏰ Started: ').padEnd(20) + chalk.yellow(this.formatDateTime(this.startTime)).padEnd(50) + chalk.green('║'));
      console.log(chalk.green('║') + chalk.cyan('  🏁 Finished: ').padEnd(20) + chalk.yellow(endTimeString).padEnd(50) + chalk.green('║'));
      console.log(chalk.green('║') + chalk.cyan('  ⚡ Duration: ').padEnd(20) + chalk.magenta(this.formatTime(buildTime)).padEnd(50) + chalk.green('║'));
      
      console.log(chalk.green('╠' + '═'.repeat(70) + '╣'));
      
      // Build statistics
      const compilation = stats.compilation;
      const assets = compilation.getAssets();
      const mainAsset = assets.find(asset => asset.name === 'index.js');
      
      if (mainAsset) {
        console.log(chalk.green('║') + chalk.cyan('  📦 Bundle size: ').padEnd(20) + chalk.white(this.formatFileSize(mainAsset.info.size)).padEnd(50) + chalk.green('║'));
      }
      
      console.log(chalk.green('║') + chalk.cyan('  📊 Modules processed: ').padEnd(20) + chalk.white(this.moduleCount.toString()).padEnd(50) + chalk.green('║'));
      console.log(chalk.green('║') + chalk.cyan('  📁 Assets generated: ').padEnd(20) + chalk.white(assets.length.toString()).padEnd(50) + chalk.green('║'));
      
      if (stats.hasErrors()) {
        console.log(chalk.green('║') + chalk.red('  🚨 Errors: ').padEnd(20) + chalk.white(stats.compilation.errors.length.toString()).padEnd(50) + chalk.green('║'));
      }
      
      if (stats.hasWarnings()) {
        console.log(chalk.green('║') + chalk.yellow('  ⚠️  Warnings: ').padEnd(20) + chalk.white(stats.compilation.warnings.length.toString()).padEnd(50) + chalk.green('║'));
      }
      
      // Performance metrics
      const avgModuleTime = buildTime / this.moduleCount;
      console.log(chalk.green('║') + chalk.cyan('  📈 Avg module time: ').padEnd(20) + chalk.white(this.formatTime(avgModuleTime)).padEnd(50) + chalk.green('║'));
      
      console.log(chalk.green('╚' + '═'.repeat(70) + '╝'));
      console.log('');
    });
  }
}

export default {
  entry: './src/index.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: {
      type: 'module'
    }
  },
  experiments: {
    outputModule: true,
    asyncWebAssembly: true,
  },
  resolve: {
    mainFiles: ["index"],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'ifcdb.wasm': path.resolve(__dirname, 'node_modules/@xeokit/xeokit-sdk/dist/web-ifc.wasm')
    },
    extensions: ['.ts', '.js', '.css', '.glb', '.wasm'],
    fallback: {
      "path": "path-browserify",
      "fs": false,
      "crypto": "crypto-browserify",
      "stream": "stream-browserify",
      "buffer": "buffer",
      "util": "util",
      "assert": "assert",
      "url": "url",
      "querystring": "querystring-es3",
      "os": "os-browserify/browser",
      "https": "https-browserify",
      "http": "stream-http",
      "zlib": "browserify-zlib",
      "vm": "vm-browserify"
    }
  },
  module: {
    rules: [
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|gif|jpg|jpeg|svg|xml)$/,
        type: "asset/inline",
      },
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.worker\.js$/,
        use: { loader: "worker-loader" },
      },
      {
        test: /\.json$/i,
        type: 'json',
      },
      {
        test: /\.glb$/i,
        use: [
          {
            loader: 'file-loader',
            options: {
              name: 'assets/glb/[name].[ext]',
            },
          },
        ],
      },
      {
        test: /\.wasm$/,
        type: 'asset/resource',
        generator: {
          filename: 'wasm/[name][ext]'
        }
      },
    ]
  },

  mode: 'development',
  devtool: 'eval-source-map',
  
  plugins: [
    new ProgressBarPlugin({
      format: '  Progress [:bar] ' + chalk.green.bold(':percent') + ' (:elapsed seconds) :msg',
      clear: false,
      summary: false,
      width: 50
    }),
    new EnhancedBuildTimePlugin()
  ]
};
