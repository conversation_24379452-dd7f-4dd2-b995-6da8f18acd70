

import { getMessage } from "../../../language";
import GlobalStore, { StoreConfig } from "../../../core/globalStore/globalStore";
import { IconCube, IconDataElement, IconEraser, IconEyeHide, IconEyeShow, IconMapModel, IconMeasureRuler, IconMouseClick, IconMousePan, IconPointMarket, IconPointTool, IconRulerTool, IconSetting, IconTick, IconTrash } from "../../../utils/icon";
import { LayerModel } from "./layerModel";
import { Setting } from "./setting";
import { DynamicToolTip, ToolTipArrowType } from "../tooltip/dynamic-tooltip";
import { ToolbarDataProperties } from "./toolbar_dataProperties";

export const enum ELayerModel {
    //Layer
    LAYER_GROUP = "group-layer",
    LAYER_MODEL = "layer-model",
}


export type ToolbarOption = {
    //Layer
    layer?: boolean;
    //Cordinates
    cordinates?: boolean;
    //Section
    section?: boolean;
    //Data properties
    dataProperties?: boolean;
    //Setting
    setting?: boolean;

    //Measure
    measure?: boolean;
    //Merge entity pick
    mergeEntityPick?: boolean;

}

export class Toolbar {
    private _globalStore: GlobalStore | undefined
    private _store: StoreConfig | undefined
    private _finalOption: ToolbarOption | undefined = undefined

    private _layerModel: LayerModel | undefined
    private _setting: Setting | undefined
    private _dataProperties: ToolbarDataProperties | undefined
    private _toolTip: DynamicToolTip | undefined
    //#region _buttonConfig
    private _buttonConfig: any[] = []

    //#region constructor

    constructor(globalStore: GlobalStore, toolbarOption: ToolbarOption) {
        this._globalStore = globalStore
        this._store = globalStore.getAll()
        this._finalOption = toolbarOption; // Lưu tùy chọn toolbar vào biến
        this._store.toolbar = this; // Lưu instance vào store

        this._layerModel = new LayerModel(this);
        this._setting = new Setting(this);
        this._dataProperties = new ToolbarDataProperties(this);

        this._buttonConfig = [
            //Layer Model
            {
                id: `${ELayerModel.LAYER_GROUP}-${this._store?.containerId}`,
                className: 'container-tool-cesium',
                buttons: [
                    {
                        id: `${ELayerModel.LAYER_MODEL}-${this._store?.containerId}`,
                        className: 'btn-tool-cesium',
                        icon: IconMapModel(),
                        tooltip: getMessage("tool-tip-model"),
                        toolTipArrowPosition: ToolTipArrowType.Right,
                        show: this._finalOption?.layer,
                        onClick: () => { this.toolBarActionLayerModel(`${ELayerModel.LAYER_MODEL}-${this._store?.containerId}`) }
                    },
                ],
                show: true
            },

            //#region distance
            //distance
            {
                id: `group-distance-${this._store?.containerId}`,
                className: 'dpu-container-toolbar',
                show: true,
                buttons: [
                    //Dim group
                    {
                        id: `dim-group-${this._store?.containerId}`,
                        className: 'dpu-container-btn-toolbar',
                        show: this._finalOption?.measure,

                        buttons: [
                            {
                                id: `ruler-tool-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure"),
                                toolTipArrowPosition: ToolTipArrowType.Right,
                                icon: IconRulerTool(),
                                show: true,
                                onClick: () => { this.ToolBarActionMeasure(`ruler-tool-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `measure-ruler-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure-action"),
                                toolTipArrowPosition: ToolTipArrowType.Right,
                                icon: IconMeasureRuler(),

                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`measure-ruler-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            // {
                            //     id: `measure-multi-ruler-${this._store?.containerId}`,
                            //     className: 'dpu-btn-toolbar',
                            //     tooltip: getMessage("tool-tip-measure-multi-action"),
                            //     icon: IconMeasureMultiRuler(),
                            //     show: false,
                            //     onClick: () => { this.ToolBarActionMeasure(`measure-multi-ruler-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            // },
                            {
                                id: `eraser-measure-ruler-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure-delete-pick"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEraser(),
                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`eraser-measure-ruler-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `eye-show-dim-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure-visible"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEyeShow(),
                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`eye-show-dim-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `eye-hide-dim-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure-hide"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEyeHide(),
                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`eye-hide-dim-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `remove-all-dim-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure-delete-all"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconTrash(),
                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`remove-all-dim-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `done-dim-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar done',
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                tooltip: '',
                                icon: IconTick(),
                                show: false,
                                onClick: () => { this.ToolBarActionMeasure(`done-dim-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            }
                        ]
                    },
                    //Point group
                    {
                        id: `point-group-${this._store?.containerId}`,
                        className: 'dpu-container-btn-toolbar',
                        show: this._finalOption?.cordinates,


                        buttons: [
                            {
                                id: `point-tool-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconPointTool(),
                                show: true,
                                onClick: () => { this.ToolBarActionPoint(`point-tool-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `point-pick-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates-action"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconPointMarket(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`point-pick-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `eraser-point-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates-delete-pick"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEraser(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`eraser-point-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `eye-hide-point-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates-hide"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEyeHide(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`eye-hide-point-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `eye-show-point-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates-visible"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconEyeShow(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`eye-show-point-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `remove-all-point-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-coordinates-delete-all"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconTrash(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`remove-all-point-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `done-point-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar done',
                                tooltip: '',
                                icon: IconTick(),
                                show: false,
                                onClick: () => { this.ToolBarActionPoint(`done-point-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            }
                        ]
                    },


                ]
            },
            //#region section group
            {
                id: `group-section-${this._store?.containerId}`,
                className: 'dpu-container-toolbar',
                show: true,
                buttons: [
                    //Section plan
                    {
                        id: `section-plan-group-${this._store?.containerId}`,
                        className: 'dpu-container-btn-toolbar',
                        show: this._finalOption?.section,


                        buttons: [
                            {
                                id: `mode-section-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-section-action"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconCube(),
                                show: true,
                                onClick: () => { this.ToolBarActionSection(`mode-section-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `mode-mouse-pan-section-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-section-interact"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconMousePan(),
                                show: false,
                                onClick: () => { this.ToolBarActionSection(`mode-mouse-pan-section-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `mode-mouse-click-section-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-section-pick"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconMouseClick(),
                                show: false,
                                onClick: () => { this.ToolBarActionSection(`mode-mouse-click-section-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `mode-remove-section-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-section-remove"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconTrash(),
                                show: false,
                                onClick: () => { this.ToolBarActionSection(`mode-remove-section-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            },
                            {
                                id: `done-section-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar done',
                                tooltip: '',
                                icon: IconTick(),
                                show: false,
                                onClick: () => { this.ToolBarActionSection(`done-section-${this._store?.containerId}`, `${this._store?.containerId}`) }
                            }

                        ]
                    }
                ]

            },
            //#region Data
            {
                id: `group-data-${this._store?.containerId}`,
                className: 'container-tool-cesium',
                show: true,
                buttons: [
                    {
                        id: `data-element-${this._store?.containerId}`,
                        className: 'btn-tool-cesium',
                        icon: IconDataElement(),
                        tooltip: getMessage("tool-tip-data-properties"),
                        show: this._finalOption?.dataProperties,
                        onClick: () => { this.ToolBarActionDataElement(`data-element-${this._store?.containerId}`) }
                    },

                ],

            },
            //#region setting
            {
                id: `group-setting-${this._store?.containerId}`,
                className: 'dpu-container-toolbar',
                show: true,
                buttons: [
                    //Setting
                    {
                        id: `setting-group-${this._store?.containerId}`,
                        className: 'dpu-container-btn-toolbar',
                        show: this._finalOption?.setting,


                        buttons: [
                            {
                                id: `mode-setting-${this._store?.containerId}`,
                                className: 'dpu-btn-toolbar',
                                tooltip: getMessage("tool-tip-measure"),
                                toolTipArrowPosition: ToolTipArrowType.Right,

                                icon: IconSetting(),
                                show: true,
                                onClick: () => { this.toolBarActionSetting(`mode-setting-${this._store?.containerId}`) }
                            },


                        ]
                    }
                ]

            },





        ]


        this.checkOptionToolbar(this._finalOption, this._buttonConfig)
        // Tạo toolbar từ buttonConfig

        const container = this._store?.containerCanvas;
        if (!container) {
            console.error(`Container not found`);
            return;
        }

        // Tạo toolbar chính
        const toolbar = document.createElement('div');
        toolbar.className = 'dpu-custom-toolbar';

        // Đệ quy để render từng group
        this._buttonConfig.forEach(group => {
            if (group.show) {
                const groupElement = this.createButtonElement(group); // Sử dụng hàm đệ quy để tạo group
                if (groupElement) {
                    toolbar.appendChild(groupElement);
                }
            }
        })

        container.appendChild(toolbar); // Thêm toolbar vào container

        this._buttonConfig.forEach(group => {
            if (group.buttons && Array.isArray(group.buttons)) {
                // Kiểm tra nếu tất cả các button bên trong có show: false
                const allButtonsHidden = group.buttons.every((button: any) => button.show === false);

                if (group.buttons.length == 0 || allButtonsHidden) {
                    // Query DOM theo id của nhóm và đặt display: none
                    // showHideElementByID(group.id, false);
                    // Query DOM theo id của nhóm
                    const element = document.getElementById(group.id);
                    if (element) {
                        element.remove(); // Xóa phần tử khỏi DOM
                    }
                }
            }
        });



    }

    //#region checkOptionToolbar
    checkOptionToolbar = (finalOptions: ToolbarOption, buttonConfig: any[]) => {
        Object.entries(finalOptions).forEach(([key, value]) => {
            if (key === 'measure' && !value) {
                this.removeButtonsConfigById(buttonConfig, [`dim-group-${this._store?.containerId}`])
            }
        });
    }

    //#region Measure
    ToolBarActionMeasure = (idElement: string, containerId: string) => {
        //list để bật tắt các button
        const listBtnToggle = [
            `ruler-tool-${containerId}`,
            `measure-ruler-${containerId}`,
            // `measure-multi-ruler-${containerId}`,
            `eraser-measure-ruler-${containerId}`,
            `eye-show-dim-${containerId}`,
            `eye-hide-dim-${containerId}`,
            `remove-all-dim-${containerId}`,
            `done-dim-${containerId}`
        ]

        const listIdGroup = [
            `dim-group-${containerId}`,
            `point-group-${containerId}`,
            `label-properties-${containerId}`,
            `group-data-${containerId}`,
            `data-element-${containerId}`,
            `group-camera-${containerId}`,
            `fly-camera-${containerId}`
        ];
        const btnActiveGr = listBtnToggle.slice(1, listBtnToggle.length - 2);

        this._unActiveBtnInGroup(listBtnToggle)
        this._activeBtnInGroup(btnActiveGr, idElement)

        //Check current in section plane active?
        if (this._store?.sectionSurfacePlanes?.isSectionPlanesActive) {
            if (!this._store?.sectionSurfacePlanes?.isMousePan) {
                this._store?.sectionSurfacePlanes.destroyEventClick();
            }
            this.activeBtn(`mode-mouse-click-section-${containerId}`, false) // Unactive section plane
            this.activeBtn(`mode-mouse-pan-section-${containerId}`, false) // Unactive section plane

        }
        //Hủy select đối tượng
        this._store?.eventHandler?.destroy();
        switch (idElement) {
            case `ruler-tool-${containerId}`:
                //Ẩn group k cần thiết
                this._showHideElementsByID(listIdGroup, false)
                this._showHideElementByID(`dim-group-${containerId}`, true)

                //Hiện ra các nút cần thiết
                this._showHideElementsByID(listBtnToggle, true)
                this._showHideElementByID(`ruler-tool-${containerId}`, false)

                this._showHideElementByID(`group-camera-${containerId}`, false);
                this._showHideElementByID(`group-data-${containerId}`, false);


                //Active nút dim trước
                this.activeBtn(`measure-ruler-${containerId}`, true)
                //Hiện hết DIM
                this._store?.measure?.actionShowHideAllMeasurements(true)
                //Thực hiện Dim
                this._store?.measure?.actionStartMeasure()

                //Hủy context Menu đi
                this._store?.contextMenu?.removeEventListeners()
                //Ẩn sectionBox nếu đang hiển thị
                if (this._store?.contextMenu && this._store?.contextMenu.sectionBox && this._store?.contextMenu.sectionBox.active) {
                    this._store.contextMenu.sectionBox.visible = false;
                }


                break;

            case `measure-ruler-${containerId}`:
                this._store?.measure?.actionShowHideAllMeasurements(true)

                this._store?.measure?.actionStartMeasure()
                // this._measure!.showHideAllDimensionCesium(true);
                // this._measure!.isMultiDim = false
                // this._measure!.dimensionCesium()
                break;
            case `measure-multi-ruler-${containerId}`:
                // this._measure!.isMultiDim = true

                // this._measure!.showHideAllDimensionCesium(true);
                // this._measure!.dimensionCesium()
                break;
            case `eraser-measure-ruler-${containerId}`:

                this._store?.measure?.actionShowHideAllMeasurements(true)

                this._store?.measure?.actionRemoveMeasurement()
                break;
            case `eye-show-dim-${containerId}`:
                this._store?.measure?.actionShowHideAllMeasurements(true)

                break;
            case `eye-hide-dim-${containerId}`:
                this._store?.measure?.actionShowHideAllMeasurements(false)


                break
            case `remove-all-dim-${containerId}`:
                this._store?.measure?.actionRemoveAllMeasurements()

                break
            case `done-dim-${containerId}`:
                this._store?.measure?.refreshAction()


                //Ẩn hết DIM
                this._store?.measure?.actionShowHideAllMeasurements(false)

                //Unactive Toolbar Measure
                if (this._store?.measure) {
                    this._store.measure.isActiveMeasure = false;
                }

                //unactive hết tất ca
                this._unActiveBtnInGroup(listBtnToggle)
                //show lại group
                this._showHideElementsByID(listIdGroup, true)
                //Ẩn các nút 
                this._showHideElementsByID(listBtnToggle, false)
                //Show lại nút
                this._showHideElementByID(`ruler-tool-${containerId}`, true)
                this._showHideElementByID(`group-camera-${containerId}`, true);
                this._showHideElementByID(`group-data-${containerId}`, true);
                this._showHideElementByID(`group-note-${containerId}`, true);
                //tạo context Menu
                this._store?.contextMenu?.initialize()
                //Hiện sectionBox nếu có và  đang tắt
                if (this._store?.contextMenu && this._store?.contextMenu.sectionBox && this._store?.contextMenu.sectionBox.active) {
                    if (this._store?.contextMenu && this._store.contextMenu.sectionBox) {
                        this._store.contextMenu.sectionBox.visible = true;
                    }
                }
                //Bật lại sự kiện select đối tượng
                this._store?.eventHandler?.initialize();

                //Toggle on section plane if current in section plane handle & off handle measure
                if (this._store?.sectionSurfacePlanes?.isSectionPlanesActive) {
                    // this._store?.contextMenu?.removeEventListeners()
                    if (this._store?.sectionSurfacePlanes?.currentSectionPlane && !this._store?.sectionSurfacePlanes?.isMousePan) {
                        this._store?.eventHandler?.destroy(); //Remove event handle
                        this._store?.sectionSurfacePlanes.initialize();
                    }
                }
                break;
            default:
                break


        }

    }

    //#region Point
    ToolBarActionPoint = (idElement: string, containerId: string) => {
        const listBtnToggle = [
            `point-tool-${containerId}`,
            `point-pick-${containerId}`,
            `eraser-point-${containerId}`,
            `eye-show-point-${containerId}`,
            `eye-hide-point-${containerId}`,
            `remove-all-point-${containerId}`,
            `done-point-${containerId}`
        ];

        const listIdGroup = [
            `dim-group-${containerId}`,
            `point-group-${containerId}`,
            `label-properties-${containerId}`,
            `group-data-${containerId}`,
            `data-element-${containerId}`,
            `group-camera-${containerId}`,
            `fly-camera-${containerId}`
        ];

        const btnActiveGr = listBtnToggle.slice(1, listBtnToggle.length - 2);

        this._unActiveBtnInGroup(listBtnToggle);
        this._activeBtnInGroup(btnActiveGr, idElement);

        //Check current in section plane active?
        if (this._store?.sectionSurfacePlanes?.isSectionPlanesActive) {
            if (!this._store?.sectionSurfacePlanes?.isMousePan) {
                this._store?.sectionSurfacePlanes.destroyEventClick();
            }
            this.activeBtn(`mode-mouse-click-section-${containerId}`, false) // Unactive section plane
            this.activeBtn(`mode-mouse-pan-section-${containerId}`, false) // Unactive section plane

        }
        //Toggle off event handler mouse
        this._store?.eventHandler?.destroy()
        switch (idElement) {
            case `point-tool-${containerId}`:
                this._showHideElementsByID(listIdGroup, false);
                this._showHideElementByID(`point-group-${containerId}`, true);
                this._showHideElementsByID(listBtnToggle, true);
                this._showHideElementByID(`point-tool-${containerId}`, false);
                this._showHideElementByID(`group-camera-${containerId}`, false);
                this._showHideElementByID(`group-data-${containerId}`, false);
                this._showHideElementByID(`group-note-${containerId}`, false);
                this.activeBtn(`point-pick-${containerId}`, true);

                //Hủy context Menu
                this._store?.contextMenu?.removeEventListeners()
                //Ẩn sectionBox nếu đang hiển thị
                if (this._store?.contextMenu && this._store.contextMenu.sectionBox && this._store.contextMenu.sectionBox.active) {
                    this._store.contextMenu.sectionBox.visible = false;
                }
                //Hủy select đối tượng
                this._store?.eventHandler?.destroy();

                //Bật point
                this._store?.coordinates?.actionShowHideAllAnnotations(true);
                this._store?.coordinates?.actionCreateAnnotation()
                //Active coordinates
                if (this._store && this._store.coordinates) {
                    this._store.coordinates.isActiveCoordinates = true;
                }

                break;

            case `point-pick-${containerId}`:

                this._store?.coordinates?.actionShowHideAllAnnotations(true);
                this._store?.coordinates?.actionCreateAnnotation()

                break;

            case `eraser-point-${containerId}`:
                this._store?.coordinates?.actionShowHideAllAnnotations(true);
                this._store?.coordinates?.actionRemovebyClickAnnotation()

                break;

            case `eye-show-point-${containerId}`:
                this._store?.coordinates?.actionShowHideAllAnnotations(true);

                break;

            case `eye-hide-point-${containerId}`:
                this._store?.coordinates?.actionShowHideAllAnnotations(false);

                break;
            case `remove-all-point-${containerId}`:
                this._store?.coordinates?.actionRemoveAllAnnotations();

                break;


            case `done-point-${containerId}`:


                this._showHideElementsByID(listIdGroup, true);
                this._showHideElementsByID(listBtnToggle, false);
                this._showHideElementByID(`point-tool-${containerId}`, true);

                this._showHideElementByID(`group-camera-${containerId}`, true);
                this._showHideElementByID(`group-data-${containerId}`, true);
                this._showHideElementByID(`group-note-${containerId}`, true);
                //Check current in section plane handle
                // if (this._store?.sectionSurfacePlanes?.isSectionPlane) {
                //     this._store?.sectionSurfacePlanes.initialize();
                // }
                //Unactive coordinates
                if (this._store && this._store.coordinates) {
                    this._store.coordinates.isActiveCoordinates = false;
                }

                //Toggle context Menu
                this._store?.contextMenu?.initialize()
                //Hiện sectionBox nếu có và  đang tắt
                if (this._store?.contextMenu && this._store?.contextMenu.sectionBox && this._store?.contextMenu.sectionBox.active) {
                    this._store?.contextMenu.sectionBox.visible = true;
                }
                //Toggle select element
                this._store?.eventHandler?.initialize();
                //Turn off coordinates
                this._store?.coordinates?.refreshActions();
                break;

            default:
                break;
        }
    };

    //#region Section
    ToolBarActionSection = (idElement: string, containerId: string) => {
        //list để bật tắt các button
        const listBtnToggle = [
            `mode-section-${containerId}`,
            `mode-mouse-pan-section-${containerId}`,
            `mode-mouse-click-section-${containerId}`,
            `mode-remove-section-${containerId}`,
            `done-section-${containerId}`
        ]

        const listIdGroup = [
            // `dim-group-${containerId}`,
            // `point-group-${containerId}`,
            // `label-properties-${containerId}`,
            // `group-data-${containerId}`,
            // `data-element-${containerId}`,
            `group-camera-${containerId}`,
            `fly-camera-${containerId}`
        ];
        const btnActiveGr = listBtnToggle.slice(1, listBtnToggle.length - 2);

        this._unActiveBtnInGroup(listBtnToggle)
        this._activeBtnInGroup(btnActiveGr, idElement)




        const checkActiveToolbar = () => {
            //Check is handling Measure?
            if (this._store?.measure?.isActiveMeasure) {
                //Toggle off Measuring 
                this._store?.measure?.distanceMeasurementsMouseControl?.reset()
                //Toggle off Measure
                this._store?.measure?.distanceMeasurementsMouseControl?.deactivate()
                //Toggle off active button
                this.activeBtn(`measure-ruler-${containerId}`, false)
                //Toogle off event handler
                this._store?.eventHandler?.destroy()

            }
            if (this._store?.coordinates?.isActiveCoordinates) {
                this._store?.coordinates?.removeAllEventListeners()
                //Toggle off active button
                this.activeBtn(`point-pick-${containerId}`, false)
                //Toogle off event handler
                this._store?.eventHandler?.destroy()
            }
        }

        switch (idElement) {
            case `mode-section-${containerId}`:
                //Ẩn group k cần thiết
                this._showHideElementsByID(listIdGroup, false)
                this._showHideElementByID(`mode-mouse-pan-section-${containerId}`, true)

                //Hiện ra các nút cần thiết
                this._showHideElementsByID(listBtnToggle, true)
                this._showHideElementByID(`mode-section-${containerId}`, false)

                //Active mouse pan
                this.activeBtn(`mode-mouse-pan-section-${containerId}`, true)

                //Active section plane
                if (this._store?.sectionSurfacePlanes) {
                    this._store?.sectionSurfacePlanes.isSectionPlanesActive = true;

                    this._store?.sectionSurfacePlanes.showControl = true
                }

                checkActiveToolbar()
                //Toggle one event handler mouse
                this._store?.eventHandler?.initialize()
                break;

            case `mode-mouse-pan-section-${containerId}`:
                checkActiveToolbar()
                //Toggle one event handler mouse
                this._store?.eventHandler?.initialize()
                //Toggle off section plane
                this._store?.sectionSurfacePlanes?.destroyEventClick()
                //Active mouse pan 
                this._store?.sectionSurfacePlanes!.isMousePan = true;
                break;
            case `mode-mouse-click-section-${containerId}`:
                checkActiveToolbar()

                //Toggle off event handler mouse
                this._store?.eventHandler?.destroy()
                //Toggle mouse click create section
                this._store?.sectionSurfacePlanes?.initialize()
                //Active mouse pan 
                this._store?.sectionSurfacePlanes!.isMousePan = false;
                break;

            case `mode-remove-section-${containerId}`:

                //Remove Event click
                this._store?.sectionSurfacePlanes?.destroyEventClick()

                //Remove section plane
                this._store?.sectionSurfacePlanes?.actionRemoveSectionPlane()

                break
            case `done-section-${containerId}`:

                //Toggle hide section plane
                this._store?.sectionSurfacePlanes?.actionHideSectionPlane()
                //Check toolbar measure is active?
                if (!this._store?.measure?.isActiveMeasure) {
                    //Toggle on event handler mouse
                    this._store?.eventHandler?.initialize()
                }


                //unActive section plane
                if (this._store?.sectionSurfacePlanes) {
                    this._store?.sectionSurfacePlanes.isSectionPlanesActive = false;
                }
                this._store?.eventHandler?.initialize()

                //unactive hết tất cả
                this._unActiveBtnInGroup(listBtnToggle)
                //show lại group
                this._showHideElementsByID(listIdGroup, true)
                //Ẩn các nút 
                this._showHideElementsByID(listBtnToggle, false)
                //Show lại nút
                this._showHideElementByID(`mode-section-${containerId}`, true)


                break;
            default:
                break


        }

    }
    //#region ToolBarActionDataElement

    ToolBarActionDataElement = (idElement: string) => {
        this._dataProperties?.toolBarActionDataProperties(idElement);

    }


    //#region LayerModel
    toolBarActionLayerModel = (idElement: string) => {
        this._layerModel?.layerModelAction(idElement);
    }

    //#region LayerModel
    toolBarActionSetting = (idElement: string) => {
        this._setting?.settingAction(idElement);
    }





    //#region removeButtonsConfigById
    removeButtonsConfigById = (buttonConfig: any[], idsToRemove: string[]): any[] => {
        return buttonConfig
            .map(group => {
                // Loại bỏ các buttons có id trong danh sách idsToRemove
                if (group.buttons && Array.isArray(group.buttons)) {
                    group.buttons = group.buttons.filter((button: any) => {
                        // Nếu button có id khớp, loại bỏ nó
                        if (idsToRemove.includes(button.id)) {
                            return false;
                        }
                        // Nếu button có nested buttons, gọi đệ quy để xử lý
                        if (button.buttons && Array.isArray(button.buttons)) {
                            button.buttons = button.buttons.filter((subButton: any) => !idsToRemove.includes(subButton.id));
                        }
                        return true;
                    });
                }

                // Kiểm tra nếu chính group.id nằm trong idsToRemove
                return !idsToRemove.includes(group.id);
            })
            .filter(group => group); // Loại bỏ các nhóm bị xóa hoàn toàn
    };


    //#region createButtonElement
    // Hàm tạo button với các thuộc tính đúng
    createButtonElement = (button: any) => {
        const buttonElement = document.createElement('div');
        buttonElement.id = button.id;
        buttonElement.className = button.className || '';

        // Nếu không hiển thị, ẩn thẻ button
        if (!button.show) {
            buttonElement.style.display = 'none';
        }

        // Thêm biểu tượng vào button (nếu có)
        if (button.icon) {
            buttonElement.innerHTML = button.icon;
        }

        // Thêm tooltip vào button (nếu có)
        if (button.tooltip) {
            buttonElement.setAttribute('data-tooltip', button.tooltip);

            if (button.toolTipArrowPosition) {
                buttonElement.setAttribute('tooltip-arrow-position', button.toolTipArrowPosition);

                new DynamicToolTip({
                    containerViewId: this._store?.containerId as string,
                    htmlElement: buttonElement,
                    delayShow: 500,
                    toolTip: button.tooltip
                })
            }
        }

        // Đặt sự kiện click (nếu có)
        if (typeof button.onClick === 'function') {
            buttonElement.addEventListener('click', button.onClick);
        }

        // Nếu button có các button con
        if (button.buttons && Array.isArray(button.buttons) && button.buttons.length > 0) {
            // Duyệt qua các button con và đệ quy để tạo ra các button con
            button.buttons.forEach((subButton: any) => {
                const subButtonElement = this.createButtonElement(subButton);
                if (subButtonElement) {
                    buttonElement.appendChild(subButtonElement); // Gắn trực tiếp vào thẻ cha
                }
            });
        }



        return buttonElement;



    };

    //#region _unActiveBtnInGroup
    _unActiveBtnInGroup = (listBtn: string[]) => {
        listBtn.forEach(id => {
            this.activeBtn(id, false);
        })
    }

    //#region activeBtn
    activeBtn = (idTarget: string, active: boolean) => {
        const btnSelect = document.getElementById(idTarget)
        if (active) {
            btnSelect?.classList.add('active')
        } else {
            btnSelect?.classList.remove('active')
        }
    }

    //#region _activeBtnInGroup
    _activeBtnInGroup = (listBtn: string[], idActive: string) => {
        listBtn.forEach(id => {
            this.activeBtn(id, false);
        })
        if (listBtn.includes(idActive)) {
            this.activeBtn(idActive, true)
        }
    }


    //#region _showHideElementsByID
    _showHideElementsByID = (idElements: string[], showElements: boolean) => {
        idElements.forEach(id => {
            this._showHideElementByID(id, showElements);
        });
    }
    //#region _showHideElementByID
    _showHideElementByID = (idElement: string, showElement: boolean) => {
        const element = document.getElementById(idElement);
        if (element) {
            if (showElement) {
                element.style.removeProperty("display");
            } else {
                element.style.display = "none";
            }
        }
    }

}