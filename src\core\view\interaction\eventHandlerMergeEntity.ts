import {
    Entity,
    MarqueePicker,
    MarqueePickerMouseControl,

    ObjectsKdTree3,
} from "@xeokit/xeokit-sdk";
import GlobalStore from "../../globalStore/globalStore";

import { OverlappingPicker } from "../../../libs/overlappingPick";
import mathTs from "../../../libs/mathTs";

enum SectionPlaneType {
    FRONT = "Front-section-box",
    BACK = "Back-section-box",
    LEFT = "Left-section-box",
    RIGHT = "Right-section-box",
    TOP = "Top-section-box",
    BOTTOM = "Bottom-section-box",
}
enum ArrowType {
    FRONT = "Front__Arrow-section-box",
    BACK = "Back__Arrow-section-box",
    LEFT = "Left__Arrow-section-box",
    RIGHT = "Right__Arrow-section-box",
    TOP = "Top__Arrow-section-box",
    BOTTOM = "Bottom__Arrow-section-box",
}
enum AxisType {
    FRONT = "Front__Axis-section-box",
    BACK = "Back__Axis-section-box",
    LEFT = "Left__Axis-section-box",
    RIGHT = "Right__Axis-section-box",
    TOP = "Top__Axis-section-box",
    BOTTOM = "Bottom__Axis-section-box",

}
export class EventHandlerMergeEntity {
    private _store = GlobalStore.getInstance().getAll();
    private _canvas: HTMLCanvasElement | null = null;

    selectedEntities: Map<string, Entity> = new Map();
    private marqueePicker: MarqueePicker | undefined;
    private marqueeControl: MarqueePickerMouseControl | undefined;

    private _isDragging = false;
    private _mouseDownPos: { x: number; y: number } | null = null;
    private picker = new OverlappingPicker();
    constructor() {
        this._canvas = this._store.canvas ?? null;
        if (!this._canvas) {
            console.error("Canvas not found in store.");
            return;
        }
        this.initialize();
        this.setupMarqueePicker();

    }

    //#region setupMarqueePicker
    setupMarqueePicker() {
        const view = this._store.viewer!;
        const objectsKdTree3 = new ObjectsKdTree3({ viewer: view });
        if (!this.marqueePicker) {
            this.marqueePicker = new MarqueePicker({
                viewer: view,
                objectsKdTree3,
            });


        }
        if (!this.marqueeControl) {
            this.marqueeControl = new MarqueePickerMouseControl({
                marqueePicker: this.marqueePicker,
            });
        }
        this.marqueeControl.setActive(true);

        this.marqueePicker.on("clear", () => {
            this.clearSelections();
        });

        this.marqueePicker.on("picked", (objectIds: (string | number)[]) => {
            const ids = objectIds.map((id) => String(id));
            ids.forEach((id) => {
                const entity = view.scene.objects[id];
                if (entity) {
                    if (entity.xrayed) {
                        return; // Bỏ qua entity đang XRay
                    }


                    const key = String(id);
                    if (!this.selectedEntities.has(key)) {
                        this.selectedEntities.set(key, entity);
                    }
                }
            });

            // Chỉ set selected với những entity không xray
            const selectedIds = ids.filter((id) => {
                const entity = view.scene.objects[id];
                return entity && !entity.xrayed;
            });

            view.scene.setObjectsSelected(selectedIds, true);
            this._store.treeLayerModel?.scrollToObjectId(selectedIds);
            if (this._store.dataProperties?.showModal) {

                this._store.dataProperties?.renderTableData()
            }
        });

    }

    //#region initialize
    initialize() {
        // //Clear selected entities in global Viewer


        this._canvas?.addEventListener("mousedown", this._handleMouseDown);
        this._canvas?.addEventListener("mousemove", this._handleMouseMove);
        this._canvas?.addEventListener("mouseup", this._handleMouseUp);
        //For trigger select again
        if (this.marqueeControl) {
            this.marqueeControl?.setActive(true);
        }



    }

    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
    };

    private _handleMouseMove = (event: MouseEvent) => {
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) this._isDragging = true;
    };

    private _handleMouseUp = (event: MouseEvent) => {
        if (event.button !== 0 || !this._mouseDownPos) return;
        if (!this._isDragging) {
            this._handleClick(event)
        };
        this._mouseDownPos = null;
        this._isDragging = false;
    };

    //#region _handleClick   

    private _handleClick(event: MouseEvent) {
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        let selectedEntity: Entity | null = null;

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        while (true) {
            const hit = this.picker.pickEntityAABB(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            if (!hit || !hit.entity) break;
            if (this._store.contextMenu?.sectionBox) {
                // Kiểm tra nếu có sectionBox thì bỏ qua các entity của section box
                if (this._store.contextMenu?.sectionBox) {
                    const entityId = hit.entity.id;
                    const isSectionBoxEntity =
                        Object.values(SectionPlaneType).includes(entityId as SectionPlaneType) ||
                        Object.values(ArrowType).includes(entityId as ArrowType) ||
                        Object.values(AxisType).includes(entityId as AxisType);

                    if (isSectionBoxEntity) {
                        continue; // Bỏ qua entity này và tiếp tục pick entity phía sau
                    }
                }
            }


            if (!hit.entity.xrayed) {
                console.log('hit.entity', hit.entity);
                selectedEntity = hit.entity;
                break; // gặp thằng không xray đầu tiên thì break
            }
        }


        if (selectedEntity) {
            const key = String(selectedEntity.id);
            if (event.ctrlKey) {
                if (this.selectedEntities.has(key)) {
                    this._deselectEntity(selectedEntity);
                } else {
                    this._addEntity(selectedEntity);
                }
            } else {
                this.clearSelections();
                this._addEntity(selectedEntity);
            }
        } else {
            if (!event.ctrlKey) {
                this.clearSelections();
            }
        }
        if (this._store.dataProperties?.showModal) {

            this._store.dataProperties?.renderTableData()
        }


    }


    //#region _deselectEntity
    private _deselectEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);
        viewer.scene.setObjectsSelected([key], false);
        this.selectedEntities.delete(String(entity.id));
        this._store.treeLayerModel?.scrollToObjectId(this._store.viewer!.scene.selectedObjectIds);
    }

    //#region _addEntity
    private _addEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);
        viewer.scene.setObjectsSelected([key], true);
        const entityKey = String(entity.id);
        if (!this.selectedEntities.has(entityKey)) {
            this.selectedEntities.set(entityKey, entity);
        }
        this._store.treeLayerModel?.scrollToObjectId(this._store.viewer!.scene.selectedObjectIds);

    }

    //#region _clearSelections
    clearSelections() {
        const viewer = this._store.viewer!;
        const deselectIds = Array.from(this.selectedEntities.keys());
        viewer.scene.setObjectsSelected(deselectIds, false);
        this.selectedEntities.clear();
        this._store.treeLayerModel?.scrollToObjectId([]);
    }



    //#region destroy
    public destroy() {
        const scene = this._store.viewer?.scene;
        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);


        this.clearSelections();
        //Clear selected entities in global Viewer
        scene?.setObjectsHighlighted(scene.selectedObjectIds, false);
        scene?.setObjectsSelected(scene.objectIds, false);
        scene?.setObjectsXRayed(scene.selectedObjectIds, false);
        scene?.setObjectsPickable(scene.selectedObjectIds, false);
        this._isDragging = false;
        this._mouseDownPos = null;
        this.marqueeControl?.setActive(false);
    }


    //#region setSelectedToModal
    setSelectedToModal() {
        const selectedIds = Array.from(this.selectedEntities.keys());
        if (selectedIds.length === 0) return
        this._store.treeLayerModel?.scrollToObjectId(selectedIds);
    }
}