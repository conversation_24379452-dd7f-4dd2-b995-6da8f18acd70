import { ContextMenu } from "@xeokit/xeokit-sdk";
import { TreeLayerModel } from "./treeLayerModel";
import GlobalStore from "../../../../core/globalStore/globalStore";
import { INodeLayerModel } from "../../../../types/layerModel/layerModel";
import { TreeViewNode } from "@xeokit/xeokit-sdk/types/plugins/TreeViewPlugin/TreeViewNode";

export default class ContextMenuTreeNode {
    treeLayerModel: TreeLayerModel;
    private _store = GlobalStore.getInstance().getAll();

    private _contextMenu: ContextMenu = new ContextMenu({
        enabled: true,
        context: {
            viewer: this._store.viewer,
        },
        items: [
            [
                {
                    title: "Expand All",
                    doAction: () => {
                        this.expandCollapseAllInWorker(true);
                    }
                },
                {
                    title: "Collapse All",
                    doAction: () => {

                        this.expandCollapseAllInWorker(false);
                    }
                },

            ],
            [
                {
                    title: "Hide All",
                    getEnabled: function (context: any) {
                        return (context.viewer.scene.visibleObjectIds.length > 0);
                    },
                    doAction: (context: any) => {
                        context.viewer.scene.setObjectsVisible(context.viewer.scene.visibleObjectIds, false);
                        this.treeLayerModel.handleRefreshList();
                    }
                },
                {
                    title: "Hide Others",
                    getEnabled: (context: any) => {
                        return ((this.treeLayerModel.selectedRows?.length ?? 0) > 0);
                    },
                    doAction: (context: any) => {
                        const nodeVisible = this.treeLayerModel.selectedRows
                        context.viewer.scene.setObjectsVisible(context.viewer.scene.visibleObjectIds, false);
                        nodeVisible?.forEach((dataView: INodeLayerModel) => {
                            dataView.isShow = true;
                            if (dataView.listNodesXeokit && dataView.listNodesXeokit.length > 0) {
                                dataView.listNodesXeokit.forEach((node: TreeViewNode) => {
                                    this.treeLayerModel.treeViewPlugin?.withNodeTree(node, (treeViewNode: any) => {
                                        if (treeViewNode.objectId) {
                                            const entity = this._store.viewer!.scene.objects[treeViewNode.objectId];
                                            if (entity) {
                                                entity.xrayed = false
                                                entity.visible = true;
                                            }
                                        }
                                    });
                                })
                            } else {

                                const treeViewNode = dataView.nodeXeokit ? (this.treeLayerModel.treeViewNodes as any)[dataView.nodeXeokit.objectId] : undefined;

                                if (treeViewNode) {
                                    // this._viewer.scene.setObjectsXRayed(this._viewer.scene.objectIds, true);
                                    this.treeLayerModel.treeViewPlugin?.withNodeTree(treeViewNode, (treeViewNode: any) => {
                                        if (treeViewNode.objectId) {
                                            const entity = this._store.viewer!.scene.objects[treeViewNode.objectId];
                                            if (entity) {
                                                entity.xrayed = false
                                                entity.visible = true;
                                            }
                                        }
                                    });
                                }
                            }
                        })



                        this.treeLayerModel.handleRefreshList();

                    }
                },
                {
                    title: "Show All",

                    doAction: (context: any) => {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        scene.setObjectsPickable(scene.objectIds, true);

                        this.treeLayerModel.handleRefreshList();

                    }
                },


            ]
        ]

    });
    constructor(treeLayerModel: TreeLayerModel) {
        this.treeLayerModel = treeLayerModel;

        this._inititalizeContextMenu()
    }


    //#region _inititalizeContextMenu
    private _inititalizeContextMenu() {
        this.treeLayerModel.containerBodyTree?.addEventListener("contextmenu", (event: MouseEvent) => {
            this._contextMenu.show(event.pageX, event.pageY);

            event.preventDefault();

        });
    }

    //#region expandCollapseAllInWorker
    // // Hàm gọi worker blob để expand/collapse tất cả node
    expandCollapseAllInWorker = (expand: boolean) => {
        const workerCode = `(${expandCollapseWorkerFunction.toString()})()`;
        const blob = new Blob([workerCode], { type: "application/javascript" });
        const worker = new Worker(URL.createObjectURL(blob));

        worker.postMessage({
            listParentNode: Array.from(this.treeLayerModel.listParentNode.entries()),
            expand: expand,
        });

        worker.onmessage = (event) => {
            const { expandedKeys } = event.data;
            // Chỉ cập nhật trạng thái expand cho node đã có
            expandedKeys.forEach((key: string) => {
                const parentNode = this.treeLayerModel.listParentNode.get(key);
                if (parentNode) parentNode.expand = expand;
            });
            this.treeLayerModel.handleDisplayItem();
            this.treeLayerModel.handleRefreshList();
            worker.terminate();
        };
    }
}

// / Tách logic xử lý expand/collapse sang function riêng cho worker
//#region expandCollapseWorkerFunction
function expandCollapseWorkerFunction() {
    self.onmessage = function (event) {
        const { listParentNode, expand } = event.data;
        // Chỉ lấy các key thực sự thay đổi trạng thái expand
        const expandedKeys: string[] = [];
        listParentNode.forEach(([key, node]: [string, any]) => {
            if (node.expand !== expand) {
                node.expand = expand;
                expandedKeys.push(key);
            }
        });
        self.postMessage({ expandedKeys });
    };
}