import { createUUID } from "../../../utils/utils";
import GlobalStore, { StoreConfig } from "../../../core/globalStore/globalStore";
import { ContextMenu as ContextMenuXeokit, Viewer } from "@xeokit/xeokit-sdk";
import { SectionBox } from "../sections/sectionBox";
import { SectionBoxV2 } from "../sections/sectionBox_v2";
import { SectionSplit } from "../sections/sectionSplit/sectionSplit";




export class ContextMenu {
    private _generalId = createUUID();
    private _store: StoreConfig | undefined = undefined;
    private _globalStore: GlobalStore | undefined = undefined;
    private _canvas: HTMLCanvasElement | null = null;
    private _containerView: HTMLElement | null = null;
    contextMenu: HTMLElement | null = null;
    private _isRightClick = false;
    private _startX = 0;
    private _startY = 0;
    private _onMouseDown: ((event: MouseEvent) => void) | null = null;
    private _onMouseUp: ((event: MouseEvent) => void) | null = null;
    sectionBox: SectionBox | undefined = undefined;
    sectionBoxv2: SectionBoxV2 = new SectionBoxV2();
    _sectionSplit: SectionSplit | undefined = undefined;
    private canvasContextMenu = new ContextMenuXeokit({
        enabled: true,
        context: {
            viewer: this._store?.viewer,
        },
        items: [
            [
                {
                    title: "Isolate",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },

                    doAction: (context: any) => {
                        const scene = (context.viewer as Viewer).scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);
                        scene.setObjectsPickable(scene.objectIds, false);
                        scene.setObjectsPickable(scene.selectedObjectIds, true);

                        scene.setObjectsSelected(scene.selectedObjectIds, false);


                        //Refresh selected objects in treeLayerModel
                        this._store?.treeLayerModel?.scrollToObjectId([]);
                    }
                },
            ],
            [

                {
                    title: "Hide",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const viewer = context.viewer;
                        const scene = viewer.scene;

                        scene.setObjectsVisible(scene.selectedObjectIds, false);

                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);

                        //Refresh selected objects in treeLayerModel
                        this._store?.treeLayerModel?.scrollToObjectId([]);
                    }
                },
                {
                    title: "Hide Others",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const viewer = context.viewer;
                        const scene = viewer.scene;

                        scene.setObjectsVisible(scene.visibleObjectIds, false);
                        scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        scene.setObjectsHighlighted(scene.highlightedObjectIds, false);
                        scene.setObjectsVisible(scene.selectedObjectIds, true);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);

                        //Refresh selected objects in treeLayerModel
                        this._store?.treeLayerModel?.scrollToObjectId([]);
                    }
                },

                {
                    title: "Show All",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;

                        const checkVisible = scene.numVisibleObjects < scene.numObjects
                        const checkXRayed = scene.numXRayedObjects > 0;
                        if (checkVisible || checkXRayed) {
                            return true;
                        }
                        return false;
                    },
                    doAction: (context: any) => {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        scene.setObjectsPickable(scene.objectIds, true);



                        //Refresh selected objects in treeLayerModel
                        this._store?.treeLayerModel?.scrollToObjectId([]);
                    }
                },
                {
                    title: "scene",

                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        console.log("scene", scene);

                    }
                },
                {
                    title: "viewer",

                    doAction: (context: any) => {
                        const viewer = context.viewer;
                        console.log("viewer", viewer);

                        console.log("view split", this._store?.sectionSplit?.viewerSplit);

                    }
                },
                {
                    title: "Section Split",
                    getEnabled: (context: any) => {
                        if (!this._sectionSplit) { return true; }
                        return false;
                    },
                    doAction: (context: any) => {
                        this._sectionSplit = new SectionSplit(this._globalStore!);

                    }
                },
                {
                    title: "Test Filter",

                    doAction: (context: any) => {
                        this._testFilter();

                    }
                },

                {
                    title: "Hit Properties",

                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const viewer = context.viewer as Viewer;
                        const scene = viewer.scene;
                        scene.selectedObjectIds.forEach((objId: string) => {
                            const metaObject = viewer.metaScene.metaObjects[objId];
                            const propertySets = metaObject.propertySets;
                            console.log("propertySets", propertySets);
                        })

                        // scene.setObjectsVisible(scene.selectedObjectIds, false);

                        // scene.setObjectsSelected(scene.selectedObjectIds, false);
                        // scene.setObjectsHighlighted(scene.selectedObjectIds, false);

                        // //Refresh selected objects in treeLayerModel
                        // this._store.treeLayerModel?.scrollToObjectId([]);
                    }
                },

                {
                    title: "Create Entity",


                    doAction: (context: any) => {
                        this._store?.sectionSplit?.createEntity();
                    }
                },

            ],
            [
                {
                    title: "Box Cut",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const getObjectIds = context.viewer.scene.selectedObjectIds;
                        if (this.sectionBox) {
                            this.sectionBox.destroy();
                        }
                        this.sectionBox = new SectionBox();

                        const aabb = context.viewer.scene.getAABB(getObjectIds);
                        // const aabb = context.viewer.scene.getAABB();


                        this.sectionBox.active = true
                        this.sectionBox.visible = true;
                        this.sectionBox.aabb = aabb;
                    }
                },
                // {
                //     // title: "Hidden Cut",
                //     getTitle: (context: any) => {
                //         return (this.sectionBox?.visible) ? "Hide Cut" : "Unhide Cut";
                //     },

                //     getEnabled: (context: any) => {
                //         return this.sectionBox?.active
                //     },
                //     doAction: (context: any) => {
                //         if (this.sectionBox) {
                //             this.sectionBox.visible = !this.sectionBox.visible;
                //         }
                //     }
                // },
                {
                    title: "Remove Cut",
                    getEnabled: (context: any) => {
                        return this.sectionBox?.active
                    },
                    doAction: (context: any) => {
                        // this.sectionBox.active = false;
                        // this.sectionBox.visible = false;
                        this.sectionBox?.destroy();
                        this.sectionBox = undefined;
                        // this.sectionBox = new SectionBox();
                        // this.sectionBox.reset();

                    }
                }
            ],
            [
                {
                    title: "View fit all",
                    doAction: (context: any) => {
                        context.viewer.cameraFlight.flyTo({
                            aabb: context.viewer.scene.getAABB()
                        });
                        this._store?.sectionSplit?.viewerSplit?.cameraFlight.flyTo({
                            aabb: this._store.sectionSplit.viewerSplit.scene.aabb
                        });


                    }
                },
                {
                    title: "View fit selected",
                    getEnabled: function (context: any) {
                        return context.viewer.scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const getObjectIds = context.viewer.scene.selectedObjectIds;
                        const aabb = context.viewer.scene.getAABB(getObjectIds);
                        context.viewer.cameraFlight.flyTo({
                            aabb: aabb
                        });


                    }
                }
            ],
        ]
    });

    constructor(store?: GlobalStore) {
        this._store = store?.getAll();
        this._globalStore = store;
        this._canvas = this._store?.canvas || null;
        this._containerView = this._store?.containerViewer as HTMLElement || null;
        if (!this._canvas || !this._containerView) {
            throw new Error("Canvas not found in GlobalStore.");
        }
        this.initialize();
        this.saveOriginalMaterials();
        this.changeMaterial();

        this._globalStore?.set("contextMenu", this);
        const liElement = (this.canvasContextMenu as any)._rootMenu.menuElement
        this._store?.containerCanvas?.appendChild(liElement);
    }

    //#region initialize
    public initialize() {
        this._onMouseDown = (event: MouseEvent) => {
            if (event.button === 2) {
                event.preventDefault();
                this._isRightClick = true;
                this._startX = event.clientX;
                this._startY = event.clientY;
            }
        };

        this._onMouseUp = (event: MouseEvent) => {
            if (this._isRightClick && event.button === 2) {
                const deltaX = Math.abs(event.clientX - this._startX);
                const deltaY = Math.abs(event.clientY - this._startY);

                if (deltaX < 5 && deltaY < 5) {
                    this.handleContextMenu(event);
                }
                this._isRightClick = false;
            }
        };

        this._canvas?.addEventListener('mousedown', this._onMouseDown);
        this._canvas?.addEventListener('mouseup', this._onMouseUp);


    }
    //#region handleContextMenu
    public handleContextMenu = (event: MouseEvent) => {
        // const canvasPos = this.getCanvasPosFromEvent(event);

        this.canvasContextMenu.context = { // Must set context before showing menu
            viewer: this._store?.viewer,

        };
        this.canvasContextMenu.show(event.pageX, event.pageY);

        event.preventDefault();
    }



    private _originalMaterials: any = null;
    //#region saveOriginalMaterials
    public saveOriginalMaterials = () => {
        const viewer = this._store?.viewer!;
        this._originalMaterials = {
            xrayMaterial: {
                fill: viewer.scene.xrayMaterial.fill,
                fillAlpha: viewer.scene.xrayMaterial.fillAlpha,
                fillColor: [...viewer.scene.xrayMaterial.fillColor],
                edgeAlpha: viewer.scene.xrayMaterial.edgeAlpha,
                edgeColor: [...viewer.scene.xrayMaterial.edgeColor],
            },

        };
    }
    //#region resetMaterials
    public resetMaterials = () => {
        if (!this._originalMaterials) return;
        const viewer = this._store?.viewer!;
        const orig = this._originalMaterials;

        Object.assign(viewer.scene.xrayMaterial, orig.xrayMaterial);

    }
    //#region changeMaterial
    public changeMaterial = () => {
        const viewer = this._store?.viewer!;

        viewer.scene.xrayMaterial.fill = true;
        viewer.scene.xrayMaterial.edges = true;
        // viewer.scene.xrayMaterial.fillAlpha = 0.01;
        viewer.scene.xrayMaterial.fillColor = [0, 0, 0];
        viewer.scene.xrayMaterial.fillAlpha = 0.1;
        // viewer.scene.xrayMaterial.edgeAlpha = 0.02;
        viewer.scene.xrayMaterial.edgeColor = [0, 0, 0];
        viewer.scene.xrayMaterial.edgeAlpha = 0.2;

    }
    //#region removeEventListeners
    public removeEventListeners() {
        if (this._onMouseDown) {
            this._canvas?.removeEventListener('mousedown', this._onMouseDown);
            this._onMouseDown = null;
        }
        if (this._onMouseUp) {
            this._canvas?.removeEventListener('mouseup', this._onMouseUp);
            this._onMouseUp = null;
        }
        this.canvasContextMenu.hide();
        // this.resetMaterials();
    }

    private _mapFilterKey = new Map<string, string[]>();

    private _testFilter = () => {
        const metaScene = this._store?.viewer?.metaScene;
        Object.values(metaScene?.metaObjects || {}).forEach((metaObj) => {
            metaObj.propertySets.forEach((propertySet) => {
                propertySet?.properties.forEach((property) => {
                    if (property.name) {
                        if (this._mapFilterKey.has(property.name)) {
                            const arr = this._mapFilterKey.get(property.name)!;
                            if (!arr.includes(metaObj.id)) {
                                arr.push(metaObj.id);
                            }
                        } else {
                            this._mapFilterKey.set(property.name, [metaObj.id]);
                        }
                    }
                })
            })
        })
        console.log("this._mapFilterKey", this._mapFilterKey);
    }

}
