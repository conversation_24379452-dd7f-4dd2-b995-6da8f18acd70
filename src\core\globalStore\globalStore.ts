import { GLTFLoaderPlugin, ViewCullPlugin, Viewer } from "@xeokit/xeokit-sdk";
import { EventHandler } from "../view/interaction/eventHandler";
import { ContextMenu } from "../view/interaction/contextMenu";
import { Toolbar } from "../view/toolbar/toolbar";
import { Measure } from "../view/measure/measure";
import { Coordinates } from "../view/coordinates/coordinates";
import { SectionSurfacePlanes } from "../view/sections/sectionPlane";
import { TreeLayerModel } from "../view/dialog/layerModel/treeLayerModel";
import { NavCube } from "../view/navCube/navCube";
import { EventHandlerMergeEntity } from "../view/interaction/eventHandlerMergeEntity";
import { DevMeasure } from "../view/measure/dev_measure";
import { DevCoordinates } from "../view/coordinates/dev_coordinates";
import { DataProperties } from "../view/dataProperties/data_properties";
import { FilterLayerModel } from "../view/dialog/filterLayerModel/filterLayerModel";
import { SectionSplit } from "../view/sections/sectionSplit/sectionSplit";


export type StoreConfig = {
    idViewer: string;
    idGeneral?: string;
    token?: string;
    containerViewer?: Element;
    containerId?: string;
    containerCanvas?: Element;
    canvasId?: string;
    canvas?: HTMLCanvasElement;
    viewer?: Viewer;
    eventHandler?: EventHandler | EventHandlerMergeEntity;
    contextMenu?: ContextMenu;
    gltfLoaderPlugin?: GLTFLoaderPlugin
    toolbar?: Toolbar
    measure?: Measure
    coordinates?: Coordinates;
    sectionSurfacePlanes?: SectionSurfacePlanes;
    dataProperties?: DataProperties
    treeLayerModel?: TreeLayerModel
    viewCullPlugin?: ViewCullPlugin; // ViewCullPlugin
    filterLayerModel?: FilterLayerModel; // FilterLayerModel

    navCube?: NavCube

    devGeneral?: {
        measure?: DevMeasure;
        coordinates?: DevCoordinates;
    }; // Placeholder for dev general toolbar

    sectionSplit?: SectionSplit
    [key: string]: any;
};

class GlobalStore {
    private store: StoreConfig;

    constructor(config: StoreConfig) {
        this.store = { ...config };
    }

    public set<K extends keyof StoreConfig>(key: K, value: StoreConfig[K]) {
        this.store[key] = value;
    }

    public get<K extends keyof StoreConfig>(key: K): StoreConfig[K] {
        return this.store[key];
    }

    public getAll() {
        return this.store;
    }
}

export default GlobalStore;
