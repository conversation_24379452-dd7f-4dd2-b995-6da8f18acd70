

/* Tooltip Styles */
.dynamic-tooltip {
  position: absolute;
  background-color: var(--background-color-1);
  color: var(--color-icon-2);
  -webkit-box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  -moz-box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  padding: 5px;
  border-radius: 4px;
  white-space: nowrap;
  font-size: 12px;
  z-index: 999;
  opacity: 0.85;
  transition: opacity 0.3s ease;
  text-align: right;
  direction: rtl;
}

/* <PERSON><PERSON><PERSON> hướng mũi tên */
.dynamic-tooltip-arrow-right,
.dynamic-tooltip-arrow-left,
.dynamic-tooltip-arrow-top,
.dynamic-tooltip-arrow-bottom,
.dynamic-tooltip-arrow-topLeft,
.dynamic-tooltip-arrow-topRight,
.dynamic-tooltip-arrow-bottomLeft,
.dynamic-tooltip-arrow-bottomRight {
  position: absolute;
}

/* <PERSON><PERSON><PERSON> tên bên phải */
.dynamic-tooltip-arrow-right::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -5px;
  transform: translateY(-50%);
  border-top: 7px solid transparent;
  border-left: 10px solid var(--background-color-1);
  border-bottom: 7px solid transparent;
}

/* Mũi tên bên trái */
.dynamic-tooltip-arrow-left::after {
  content: "";
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  border-top: 7px solid transparent;
  border-right: 10px solid var(--background-color-1);
  border-bottom: 7px solid transparent;
}

/* Mũi tên phía trên */
.dynamic-tooltip-arrow-top::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới */
.dynamic-tooltip-arrow-bottom::after {
  content: "";
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}

/* Mũi tên phía trên bên trái */
.dynamic-tooltip-arrow-topLeft::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía trên bên phải */
.dynamic-tooltip-arrow-topRight::after {
  content: "";
  position: absolute;
  bottom: -5px;
  right: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới bên trái */
.dynamic-tooltip-arrow-bottomLeft::after {
  content: "";
  position: absolute;
  top: -5px;
  left: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới bên phải */
.dynamic-tooltip-arrow-bottomRight::after {
  content: "";
  position: absolute;
  top: -5px;
  right: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}
