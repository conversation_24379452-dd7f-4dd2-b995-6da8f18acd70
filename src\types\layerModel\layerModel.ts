import { TreeViewPlugin } from "@xeokit/xeokit-sdk";
import { TreeViewNode } from "@xeokit/xeokit-sdk/types/plugins/TreeViewPlugin/TreeViewNode";

export interface IInputTreeLayerModel {
    url?: string;
    arrObj?: IObjTreeLayerModel[];
}

export interface IObjTreeLayerModel {
    id: string;
    name: string;
    parentId?: string;
    parentID?: string;
    url?: string;
    isShow?: boolean;
    isFolder?: boolean;
    metaSrc?: string; //Source of metadata for the node
    primitive?: string; //Primitive type of the node
    linkFile?: string; //List of linked files for the node
}


export interface INodeLayerModel {
    id: string;
    title: string;
    children?: INodeLayerModel[];
    parent?: INodeLayerModel | null;
    url?: string;
    nodeXeokit?: TreeViewNode;
    listNodesXeokit?: TreeViewNode[];
    isShow?: boolean; //Show/Hide node
    isFolder?: boolean; //True if node is folder
    expand?: boolean; //True if node is expanded
    level?: number; //Level of node in tree
    metaSrc?: string; //Source of metadata for the node
    primitive?: string; //Primitive type of the node, e.g., "IfcWall", "IfcDoor"

    treeViewPlugin?: TreeViewPlugin; // Reference to the corresponding TreeViewNode in xeokit

    childrenFilter?: string[]
};
