/**
 * Default data access strategy for {@link WebIFCLoaderPlugin}.
 */

interface WebIFCDefaultDataSourceConfig {
    cacheBuster?: boolean;
}

class WebIFCDefaultDataSource {
    cacheBuster: boolean;

    constructor(cfg: WebIFCDefaultDataSourceConfig = {}) {
        this.cacheBuster = (cfg.cacheBuster !== false);
    }

    _cacheBusterURL(url: string): string {
        if (!this.cacheBuster) {
            return url;
        }
        const timestamp = new Date().getTime();
        if (url.indexOf('?') > -1) {
            return url + '&_=' + timestamp;
        } else {
            return url + '?_=' + timestamp;
        }
    }

    /**
     * Gets the contents of the given IFC file in an arraybuffer.
     *
     * @param src Path or ID of an IFC file.
     * @param ok Callback fired on success, argument is the IFC file in an arraybuffer.
     * @param error Callback fired on error.
     */
    getIFC(
        src: string | number,
        ok?: (buffer: ArrayBuffer) => void,
        error?: (errMsg: any) => void
    ): void {
        src = this._cacheBusterURL(String(src));

        const defaultCallback = () => {};
        ok = ok || defaultCallback;
        error = error || defaultCallback;
        const dataUriRegex = /^data:(.*?)(;base64)?,(.*)$/;
        const dataUriRegexResult = src.match(dataUriRegex);
        if (dataUriRegexResult) { // Safari can't handle data URIs through XMLHttpRequest
            const isBase64 = !!dataUriRegexResult[2];
            let data = dataUriRegexResult[3];
            data = window.decodeURIComponent(data);
            if (isBase64) {
                data = window.atob(data);
            }
            try {
                const buffer = new ArrayBuffer(data.length);
                const view = new Uint8Array(buffer);
                for (let i = 0; i < data.length; i++) {
                    view[i] = data.charCodeAt(i);
                }
                ok(buffer);
            } catch (errMsg) {
                error(errMsg);
            }
        } else {
            const request = new XMLHttpRequest();
            request.open('GET', src, true);
            request.responseType = 'arraybuffer';
            request.onreadystatechange = function () {
                if (request.readyState === 4) {
                    if (request.status === 200) {
                        ok(request.response);
                    } else {
                        error('getXKT error : ' + request.response);
                    }
                }
            };
            request.send(null);
        }
    }
}

export {WebIFCDefaultDataSource};