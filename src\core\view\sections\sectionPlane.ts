import { <PERSON><PERSON>lick<PERSON><PERSON><PERSON> } from "../../../utils/mouseClickHandler";
import GlobalStore from "../../globalStore/globalStore";
import mathTs from "../../../libs/mathTs";
import { OverlappingPicker } from "../../../libs/overlappingPick";
import { buildPlaneGeometry, EmphasisMaterial, Mesh, PhongMaterial, PickResult, ReadableGeometry, SectionPlane, SectionPlanesPlugin } from "@xeokit/xeokit-sdk";
import { createUUID } from "../../../utils/utils";
import QUARE_SHAPE from '../../../assets/images/quare_shape.png'
import { Texture, transformToNode, math } from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";

export class SectionSurfacePlanes {
    private _generalId = createUUID();
    private _store = GlobalStore.getInstance().getAll();
    private _handleMouseClick: MouseClickHandler | undefined = undefined
    private picker = new OverlappingPicker();
    sectionPlanes = new SectionPlanesPlugin(this._store.viewer!, {
        overviewCanvasId: `section-plan-overview-${this._store.containerId}`,
        overviewVisible: false
    });
    private _currentSectionPlane: undefined | SectionPlane = undefined;
    private _isSectionPlanesActive = false
    private _isMousePan: boolean = true;
    private _planeMarkup: Mesh = new Mesh(this._store.viewer!.scene, {
        visible: false,
        pickable: false,
        highlighted: true,
        collidable: false,
        clippable: false,
        id: `section-plane-markup-${this._generalId}`,
        geometry: new ReadableGeometry(this._store.viewer!.scene, buildPlaneGeometry({})),
        material: new PhongMaterial(this._store.viewer!.scene, {
            alpha: 0.6,
            diffuseMap: new Texture(this._store.viewer!.scene, { src: QUARE_SHAPE })
        }),
        highlightMaterial: new EmphasisMaterial(this._store.viewer!.scene, {
            fillAlpha: 0.6,
            fill:true
        }),

    })


    constructor() {
        

        GlobalStore.getInstance().set("sectionSurfacePlanes", this);
    }

    //#region initialize
    initialize = () => {
        if (!this._handleMouseClick) {
            this._handleMouseClick = new MouseClickHandler(this._store.canvas!, this.handleMouseClick)
        } else {
            this._handleMouseClick.initializeEventListener()
        }
        if (this._currentSectionPlane) {
            this.sectionPlanes.showControl(this._currentSectionPlane.id);
        }

        this.showPlaneMarkup = true;

    };

    //Section planes active state
    get isSectionPlanesActive(): boolean {
        return this._isSectionPlanesActive;
    }

    set isSectionPlanesActive(value: boolean) {
        this._isSectionPlanesActive = value;


    }

    get currentSectionPlane(): SectionPlane | undefined {
        return this._currentSectionPlane;
    }
    set currentSectionPlane(value: SectionPlane | undefined) {
        this._currentSectionPlane = value;
    }

    get isMousePan(): boolean {
        return this._isMousePan;
    }
    set isMousePan(value: boolean) {
        this._isMousePan = value;
    }

    set showPlaneMarkup(value: boolean) {
        this._planeMarkup.visible = value;

        const canvas = this._store.canvas;
        if (canvas) {
            canvas.removeEventListener("mousemove", this._updatePlaneMesh);
            if (value) {
                canvas.addEventListener("mousemove", this._updatePlaneMesh);
            }
        }
    }


    set showControl(value: boolean) {
        if (this._currentSectionPlane) {
            value
                ? this.sectionPlanes.showControl(this._currentSectionPlane.id)
                : this.sectionPlanes.hideControl();
        }


    }


    private _lastUpdateTime = 0;
    _updatePlaneMesh = (event: MouseEvent) => {
        const now = performance.now();
        const THROTTLE_MS = 16; // ~60fps
        if (now - this._lastUpdateTime < THROTTLE_MS) return;
        this._lastUpdateTime = now;

        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._store.canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        let hit: PickResult | null = null;

        while (true) {
            hit = this.picker.pickSurface(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            if (!hit || !hit.entity) break;

            if (!hit.entity.xrayed) {
                break; // gặp thằng không xray đầu tiên thì break
            }
        }

        if (hit) {
            const dir = hit.worldNormal;
            const res = math.identityMat4();

            const acc = (mat: any) => math.mulMat4(mat, res, res);
            // face front instead of up
            acc(math.rotationMat4v(Math.PI / 2, [1, 0, 0], math.mat4()));

            // orient along normal vector of the intersection point
            acc(math.quaternionToMat4(math.vec3PairToQuaternion([0, 0, 1], dir, math.vec4()), math.mat4()));

            // translate to intersection point
            acc(math.translationMat4v(math.addVec3(hit.worldPos, math.mulVec3Scalar(dir, 0.01, math.vec3()), math.vec3())));

            this._planeMarkup.matrix = res;
        }
    }

    //#region handleMouseClick
    handleMouseClick = (event: MouseEvent) => {
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._store.canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        let hit: PickResult | null = null;

        while (true) {
            hit = this.picker.pickSurface(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            if (!hit || !hit.entity) break;

            if (!hit.entity.xrayed) {
                break; // gặp thằng không xray đầu tiên thì break
            }
        }

        if (hit) {

            //Create section plane if does not exist
            if (!this._currentSectionPlane) {
                this._currentSectionPlane = this.sectionPlanes.createSectionPlane({
                    id: `current-section-plane-${this._generalId}`,

                });
            }
            //Change section plane position and direction
            this._currentSectionPlane.pos = hit.worldPos;
            //Get direction from hit world normal
            this._currentSectionPlane.dir = Array.from(mathTs.mulVec3Scalar(new Float32Array(hit.worldNormal), -1))
            // this._currentSectionPlane.dir = Array.from(mathTs.mulVec3Scalar(new Float32Array([0,hit.worldNormal[1],hit.worldNormal[2]]), -1))
            this.sectionPlanes.showControl(this._currentSectionPlane.id)

          
        }
    }

    //#region actionCreateSectionPlane
    actionRemoveSectionPlane = () => {
        if (this._currentSectionPlane) {
            this.sectionPlanes.destroySectionPlane(this._currentSectionPlane.id);
            this._currentSectionPlane = undefined;
        }
        this.showPlaneMarkup = false;

    }

    //#region actionHideSectionPlane
    actionHideSectionPlane = () => {
        if (this._currentSectionPlane) {

            this.sectionPlanes.hideControl();
            this.destroyEventClick();
        }
        this.showPlaneMarkup = false;
    }

    //#region createSectionPlane
    createSectionPlane = ({
        pos,
        dir,
        active = true
    }: {
        pos: [number, number, number];
        dir: [number, number, number];
        active: boolean;
    }) => {
        // Create a new section plane
        if (!this._currentSectionPlane) {
            this._currentSectionPlane = this.sectionPlanes.createSectionPlane({
                id: `current-section-plane-${this._generalId}`,

            });
        }
        this._currentSectionPlane.pos = pos;
        this._currentSectionPlane.dir = dir


        this._currentSectionPlane.active = active;

        this.sectionPlanes.showControl(this._currentSectionPlane.id)

    }

    //#region destroyEventClick
    destroyEventClick = () => {
        this._handleMouseClick?.destroy();
        // this._handleMouseClick = undefined;
        this.showPlaneMarkup = false;

    }




}

//#region create2dMarkup
function createImage(): HTMLImageElement {
    const img = document.createElement('img');
    img.crossOrigin = "anonymous";
    img.src = QUARE_SHAPE;
    img.style.cssText = 'width:50px;height:50px;object-fit:cover;';
    return img;
}