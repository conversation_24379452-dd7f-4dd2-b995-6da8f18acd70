import { createUUID } from "../../../../utils/utils";
import GlobalStore from "../../../../core/globalStore/globalStore";
import { DialogGeneral } from "../../../../widgets/dialog/dialog";
import { TreeViewPlugin } from "@xeokit/xeokit-sdk";
import { IconArrowDown, IconEyeHide, IconEyeShow, IconFilter, IconSearch } from "../../../../utils/icon";
import { HyperList } from "../../../../utils/hyperlist/hyperlist";
import { TreeViewNode } from "@xeokit/xeokit-sdk/types/plugins/TreeViewPlugin/TreeViewNode";
import { INodeLayerModel } from "../../../../types/layerModel/layerModel";
import { EventHandlerMergeEntity } from "../../interaction/eventHandlerMergeEntity";
import ContextMenuTreeNode from "./contextMenuTreeNode";
import { FilterLayerModel } from "../filterLayerModel/filterLayerModel";
import loading from "../../../../assets/images/loading-roll-2.png"

export class TreeLayerModel {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer = this._store.viewer!;
    private _targetContainerId = this._store.containerId as string;
    private _idGeneral: string = createUUID();
    private _dialog: DialogGeneral | undefined;
    treeViewPlugin: TreeViewPlugin | undefined;
    newTreeViewNode: INodeLayerModel[] = [];
    private _list: HyperList | undefined
    contextMenuTreeNode: ContextMenuTreeNode;
    isFiltering = false;
    private _config = {
        height: '100%',
        itemHeight: 33,
        total: 50,
        reverse: false,
        scrollerTagName: 'div',
        // Customize the virtual row class, defaults to vrow.
        rowClassName: 'dpu-tree-item',
        generate: (row: number): HTMLDivElement => {
            const item = this.listDisplayItem[row];
            const div = this._createItemTag(item, this.listDisplayItem) as HTMLDivElement;
            return div ?? document.createElement('div'); // Ensure a valid HTMLElement is always returned
        }
    }
    // Thuộc tính để lưu callback
    private onClosedCallback: ((status: boolean) => void) | undefined;
    constructor() {
        GlobalStore.getInstance().set("treeLayerModel", this);

        this._createModal();
        this._dialog?.showModal(false)

        this.contextMenuTreeNode = new ContextMenuTreeNode(this);
    }

    private _createModal() {
        this._dialog = new DialogGeneral(
            this._targetContainerId,
            this._handleCloseModal
        );
        this._dialog.innerHeader = "Model";
        this._dialog.modal = false
        this._dialog.minimizeDialog = true;
        this._dialog.widthModal = '300px'
        this._dialog.heightModal = '500px'
        this._dialog.zIndex = 4000
        this._dialog.topModal = '5px'
        this._dialog.leftModal = '5px'
        // Lấy chuỗi HTML của tree view và gán cho innerBody
        this._dialog.innerBody = this.renderTreeLayerModel();
        this._dialog.innerFooter = this.renderFooter();

        this._dialog.createDialog();

        this.treeViewPlugin = new TreeViewPlugin(
            this._viewer!, {
            containerElementId: this._store.containerId,
            // containerElement: this._containerBodyTree!,
            // hierarchy: "storeys",
            // autoAddModels: false

        }
        )

        console.log('this.treeViewPlugin', this.treeViewPlugin);
    }

    private _handleCloseModal = (status: boolean) => {
        if (!status) {
            this.notifyCallBack(status)
        }
    }

    // Gọi callback để thông báo thay đổi trạng thái
    notifyCallBack(status: boolean): void {
        if (this.onClosedCallback) {
            this.onClosedCallback(status); // Thông báo cho lớp cha
        } else {
            console.error("State change callback is not set.");
        }
    }
    // Gán callback từ lớp cha
    setClosedCallback(callback: (status: boolean) => void): void {
        this.onClosedCallback = callback;
    }

    //#region renderFooter
    private containerFooter: HTMLElement | undefined
    renderFooter = (): HTMLElement => {

        this.containerFooter = document.createElement('div');
        this.containerFooter.id = `footer-tree-folder-${this._idGeneral}`;

        this.containerFooter.style.marginTop = '5px';
        const settingBtn = document.createElement('button');
        // settingBtn.id = `btn-move-model-${this._idGeneral}`;
        settingBtn.className = 'dpu-btn-defaut';
        settingBtn.style.width = '25px';
        settingBtn.style.marginRight = '5px';
        settingBtn.innerHTML = IconFilter()
        this.containerFooter.appendChild(settingBtn)

        if (settingBtn) {
            settingBtn.addEventListener('click', () => {
                this._handleFilterTree()
            });
        }


        return this.containerFooter;
    }


    //#region  renderTreeLayerModel
    containerBodyTree: HTMLElement | null = null;
    private _containerBodyDialog = document.createElement("div")
    // Hàm renderTreeView
    renderTreeLayerModel = (): HTMLElement => {

        // Nếu containerUl đã tồn tại, remove nó khỏi DOM
        if (this.containerBodyTree) {
            this._clearContainer()
        } else {
            this._containerBodyDialog = document.createElement("div");
            this._containerBodyDialog.style.cssText = "width:100%;height:100%;overflow:hidden";

            this._createSearch()


            // Tạo một phần tử div mới để chứa tree view
            this.containerBodyTree = document.createElement("div");
            this.containerBodyTree.id = `tree-layer-model-${this._idGeneral}`;

            const containerHeight = document.createElement("div");
            containerHeight.className = "dpu__container-tree";

            containerHeight.appendChild(this.containerBodyTree);

            this._containerBodyDialog.appendChild(containerHeight);
        }

        //Render ra tree layer
        this.handleDisplayItem()

        this._createTreeNode(this.listDisplayItem)



        return this._containerBodyDialog;
        // return this.containerDialog!;
    };
    private _clearContainer = () => {
        if (this.containerBodyTree) {
            this.containerBodyTree.innerHTML = ""; // Xóa nội dung bên trong containerUl
        }
    };

    //#region _createSearch
    private _inputSearch: HTMLInputElement | null = null;
    private _searchBtn: HTMLButtonElement | null = null;
    private _createSearch = () => {
        const containerSearch = document.createElement("div");
        containerSearch.style.cssText = "padding:5px;width:100%;display:flex";

        this._inputSearch = document.createElement("input");
        this._inputSearch.className = "dpu-input-text";
        this._inputSearch.setAttribute("type", "text");
        this._inputSearch.placeholder = "Search...";


        containerSearch.appendChild(this._inputSearch);

        this._searchBtn = document.createElement("button");
        this._searchBtn.className = "dpu-btn-defaut";
        this._searchBtn.innerHTML = `${IconSearch()}`;
        this._searchBtn.style.width = "32px";
        this._searchBtn.style.height = "30px";
        this._searchBtn.style.padding = "5px";
        this._searchBtn.style.marginLeft = "5px";

        // Chỉ thêm event listener cho button search
        this._searchBtn.addEventListener('click', this._handeleSearch);

        containerSearch.appendChild(this._searchBtn);

        this._containerBodyDialog.appendChild(containerSearch);
    }
    //#region  _handeleSearch
    // ...existing code...
    private _originalListDisplayItem: INodeLayerModel[] = []; // Lưu list tạm
    private _handeleSearch = () => {
        const searchTerm = this._inputSearch?.value?.trim().toLowerCase() || '';

        // Nếu input rỗng, trả lại list display tạm
        if (searchTerm === '') {
            if (this._originalListDisplayItem.length > 0) {
                this.listDisplayItem = [...this._originalListDisplayItem];
                this._originalListDisplayItem = []; // Clear list tạm
                this.handleRefreshList();

            }
            return;
        }

        // Lưu lại list display hiện tại nếu chưa lưu
        if (this._originalListDisplayItem.length === 0) {
            this._originalListDisplayItem = [...this.listDisplayItem];
        }

        // Tìm kiếm trong _newTreeViewNode
        const searchResults = this.newTreeViewNode.filter(node => {
            const titleMatch = node.title?.toLowerCase().includes(searchTerm);
            // const typeMatch = node.type?.toLowerCase().includes(searchTerm);
            const objectIdMatch = node.id?.toLowerCase().includes(searchTerm);

            // return titleMatch || typeMatch || objectIdMatch;
            return titleMatch || objectIdMatch;
        });

        // Gán kết quả search vào listDisplay hiện tại
        this.listDisplayItem = searchResults

        // Refresh hiển thị
        this.handleRefreshList();
    }

    private _isOpenningModal: boolean = false;

    get isOpenningModal(): boolean {
        return this._isOpenningModal;
    }

    set isOpenningModal(value: boolean) {
        this._isOpenningModal = value;
        if (value) {
            (this._store.eventHandler as EventHandlerMergeEntity)?.setSelectedToModal();
        }
    }

    //#region showHideModalTreeLayerModel
    showHideModalTreeLayerModel = (show: boolean) => {
        if (show) {
            this.isOpenningModal = true;

        } else {
            this.isOpenningModal = false;

        }
        this._dialog?.showModal(show)

    }
    private _createTreeNode = (flatData: INodeLayerModel[]) => {
        flatData.forEach((currentItem: INodeLayerModel) => {
            const div = this._createItemTag(currentItem, flatData)
            if (div) {
                this.containerBodyTree!.appendChild(div);
            }
        })
    }

    private _listItemMap: Map<string, INodeLayerModel> = new Map<string, INodeLayerModel>()
    //#region _createItemTag
    private _createItemTag = (currentItem: INodeLayerModel, flatData: INodeLayerModel[]): HTMLElement => {
        if (!currentItem) return document.createElement('div');
        this._listItemMap.set(currentItem.id, currentItem)
        const div = document.createElement('div');
        div.setAttribute('data-layer-id', currentItem.id);
        if (currentItem.level || currentItem.level !== 0) {
            div.setAttribute('data-level', String(currentItem?.level ?? ""));
            div.style.paddingLeft = `${(currentItem.level ?? 0) * 15}px`;
        }
        const fullrow = document.createElement('div');
        fullrow.className = "fullrow";
        div.appendChild(fullrow);

        div.addEventListener('click', (event) => this.handleItemSelection(event, div, currentItem));
        if (Array.isArray(this.selectedRows) && this.selectedRows.some(item => item && item.id === currentItem.id)) {
            div.classList.add('active');
        }
        //Tạo tất cả row
        const span = this.createCaretSpan(currentItem);
        div.appendChild(span);

        //Tạo nút visible/search
        const buttonContainer = this.createButtonContainer(currentItem, div);

        div.appendChild(buttonContainer);

        return div
    }
    //#region createButtonContainer
    private createButtonContainer(currentItem: INodeLayerModel, li: HTMLElement): HTMLDivElement {
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'button-container';
        // Tạo nút tìm kiếm
        const showButton = document.createElement('div');
        showButton.className = 'folder-button';
        showButton.setAttribute('data-folder-id', currentItem.id.toString());
        if (currentItem.nodeXeokit) {
            currentItem.isShow = currentItem.nodeXeokit?.checked

        } else {
            // if (currentItem.isShow === undefined) {
            //     currentItem.isShow = true
            // }
            const checkDisplay = this._checkDisplayParentNode(currentItem);
            if (checkDisplay) {
                currentItem.isShow = true
            } else {
                currentItem.isShow = false
            }
        }

        showButton.innerHTML = currentItem.isShow ? IconEyeShow() : IconEyeHide();
        showButton.addEventListener('click', (event) => {
            event.stopPropagation();

            this.handleItemSelection(event, li, currentItem)
            this._handleToggleVisible(currentItem, showButton)

        });

        // Thêm nút vào container
        buttonContainer.appendChild(showButton);
        // Tạo nút tìm kiếm
        const searchButton = document.createElement('div');
        searchButton.className = 'search-button';
        searchButton.innerHTML = IconSearch(); // Hàm tạo biểu tượng tìm kiếm
        searchButton.addEventListener('click', (event) => {
            event.stopPropagation();
            this.handleItemSelection(event, li, currentItem)//Lưu lại trước khi refesh list
            this._handleZoomTo(currentItem)


        });
        buttonContainer.appendChild(searchButton);




        return buttonContainer;
    }


    //#region createCaretSpan
    private createCaretSpan(currentItem: INodeLayerModel): HTMLSpanElement {
        const span = document.createElement('span');
        span.className = "caret";
        //Tạo arrow
        this.createNestedContainer(currentItem, span)

        const textNode = document.createElement('span');
        textNode.className = "text";
        textNode.setAttribute('data-layer-text-id', currentItem.id);
        textNode.textContent = (currentItem.title === "") ? currentItem.id || "" : currentItem.title;
        if (currentItem?.nodeXeokit && !currentItem?.nodeXeokit?.checked) {
            textNode.style.opacity = "0.5"
        } else if (!currentItem?.nodeXeokit && currentItem.listNodesXeokit && currentItem.listNodesXeokit.length > 0) {
            const checkDisplay = this._checkDisplayParentNode(currentItem);
            if (checkDisplay) {
                textNode.style.removeProperty("opacity")

            } else {
                textNode.style.opacity = "0.5"
            }
        }

        else {
            textNode.style.removeProperty("opacity")
        }
        span.appendChild(textNode);

        return span;
    }

    //#region createNestedContainer
    private createNestedContainer(currentItem: INodeLayerModel, span: HTMLElement) {
        const spanHolder = document.createElement('span');
        if (currentItem.level == 0 || (currentItem.children?.length ?? 0) > 0) {
            spanHolder.className = "arrow-icon";
            spanHolder.innerHTML = IconArrowDown();
            if (currentItem.expand === undefined || currentItem.expand === null) {
                currentItem.expand = false

            }
            if (currentItem.expand) {
                spanHolder.classList.add("arrow-down");
            } else {
                spanHolder.classList.remove("arrow-down");

            }

            spanHolder.addEventListener("click", (event) => {
                event.stopPropagation();
                this._actionArrow(currentItem, spanHolder)
            });

        } else {
            spanHolder.className = "arrow-placeholder";
        }



        span.appendChild(spanHolder);


        // children.forEach(child => {
        //     const childItem = this.createTreeItem(flatData, child);
        //     nestedContainer.appendChild(childItem);
        // });

    }

    //#region actionArrow
    private _actionArrow = async (currentItem: any, arrow: HTMLSpanElement) => {


        if (!currentItem.expand) {
            currentItem.expand = true

            this.handleDisplayItem(currentItem)


        } else {
            currentItem.expand = false
            this.handleRemoveDisplayItem(currentItem)

        }
        this.handleRefreshList()
        // arrow.classList.toggle("arrow-down");




    }
    //#region _handleToggleVisible
    // Hàm xử lý khi nút bên trái được nhấn
    private _handleToggleVisible = async (dataView: INodeLayerModel, button: HTMLElement) => {
        // console.log('dataView show', dataView);

        dataView.isShow = !dataView.isShow;

        if (dataView.listNodesXeokit && dataView.listNodesXeokit.length > 0) {
            // console.log('vô list');
            dataView.listNodesXeokit.forEach((node: TreeViewNode) => {
                this.treeViewPlugin?.withNodeTree(node, (treeViewNode: any) => {
                    if (treeViewNode.objectId) {
                        const entity = this._viewer.scene.objects[treeViewNode.objectId];
                        if (entity) {
                            entity.xrayed = false
                            entity.pickable = dataView.isShow ?? true;
                            entity.visible = dataView.isShow ?? true; // Default to true if isShow is undefined
                        }
                    }
                });
            })
        } else {
            // const pluginTreeNode = dataView.treeViewPlugin
            // const treeViewNode = dataView.nodeXeokit ? (pluginTreeNode as any)._objectNodes[dataView.nodeXeokit.objectId] : undefined;
            // if (treeViewNode) {
            //     pluginTreeNode?.withNodeTree(treeViewNode, (treeViewNode: any) => {
            //         if (treeViewNode.objectId) {
            //             const entity = this._viewer.scene.objects[treeViewNode.objectId];
            //             if (entity) {
            //                 entity.xrayed = false
            //                 entity.visible = dataView.isShow ?? true;
            //             }
            //         }
            //     })
            // }
            const treeViewNode = dataView.nodeXeokit ? (this.treeViewNodes as any)[dataView.nodeXeokit.objectId] : undefined;

            if (treeViewNode) {
                // this._viewer.scene.setObjectsXRayed(this._viewer.scene.objectIds, true);
                // console.log('vô element');
                this.treeViewPlugin?.withNodeTree(treeViewNode, (treeViewNode: any) => {
                    if (treeViewNode.objectId) {
                        const entity = this._viewer.scene.objects[treeViewNode.objectId];
                        if (entity) {
                            entity.xrayed = false
                            entity.pickable = dataView.isShow ?? true;

                            entity.visible = dataView.isShow ?? true;
                        }
                    }
                });
            }
        }




        this.handleRefreshList()
    }
    //#region _handleZoomTo
    private _handleZoomTo = async (dataView: INodeLayerModel) => {
        const objectIds: string[] = [];

        // currentItem.isShow = true;
        if (!dataView.nodeXeokit && dataView.listNodesXeokit && dataView.listNodesXeokit.length > 0) {
            dataView.listNodesXeokit.forEach((node: TreeViewNode) => {
                this.treeViewPlugin?.withNodeTree(node, (treeViewNode: any) => {
                    if (treeViewNode.objectId) {
                        objectIds.push(treeViewNode.objectId);
                    }
                });
            });
        } else {
            // const pluginTreeNode = dataView.treeViewPlugin

            // const treeViewNode = dataView.nodeXeokit ? (pluginTreeNode as any)._objectNodes[dataView.nodeXeokit.objectId] : undefined;

            // if (treeViewNode) {
            //     pluginTreeNode?.withNodeTree(treeViewNode, (treeViewNode: any) => {
            //         if (treeViewNode.objectId) {
            //             objectIds.push(treeViewNode.objectId);
            //         }
            //     });
            // }

            const treeViewNode = dataView.nodeXeokit ? (this.treeViewNodes as any)[dataView.nodeXeokit.objectId] : undefined;

            if (treeViewNode) {
                this.treeViewPlugin?.withNodeTree(treeViewNode, (treeViewNode: any) => {
                    if (treeViewNode.objectId) {
                        objectIds.push(treeViewNode.objectId);
                    }
                });
            }



        }


        //Show all
        this._viewer.scene.setObjectsVisible(this._viewer.scene.objectIds, true);
        //xrayed all
        this._viewer.scene.setObjectsXRayed(this._viewer.scene.objectIds, true);

        //xrayed Ids
        this._viewer.scene.setObjectsXRayed(objectIds, false);
        this._viewer.cameraFlight.flyTo({
            aabb: this._viewer.scene.getAABB(objectIds),
            duration: 0.5
        },);
        this.handleRefreshList()
    }


    //#reggion handleItemSelection
    private handleItemSelection(event: MouseEvent, li: HTMLElement, currentItem: INodeLayerModel): void {
        const isCtrl = event.ctrlKey;
        // Multi-select bằng Ctrl
        if (isCtrl) {
            if (!Array.isArray(this.selectedRows)) this.selectedRows = [];
            const idx = this.selectedRows.findIndex(item => item.id === currentItem.id);
            if (idx === -1) {
                this.selectedRows.push(currentItem);
                li.classList.add('active');
            } else {
                this.selectedRows.splice(idx, 1);
                li.classList.remove('active');
            }
            // Update xeokit selection
            const selectedIds = this.selectedRows.map(item => item.id);
            this._store.viewer?.scene.setObjectsSelected(selectedIds, true);
            return;
        }
        // Mặc định: chỉ chọn một dòng
        this.selectSingleItem(li, currentItem);
    }

    //#region toggleSelectItem
    // private toggleSelectItem(li: HTMLElement, currentItem: INodeLayerModel): void {
    //     const isSelected = li.classList.toggle('active');

    //     if (isSelected) {
    //         this.selectedItems.add(currentItem.id);
    //     } else {
    //         this.selectedItems.delete(currentItem.id);
    //     }
    //     if (this.selectedItems.size === 1) {

    //         this.selectedRows = [currentItem];
    //     }
    //     // this.lastSelectedItem = li;
    // }

    //#region selectSingleItem
    selectedRows: INodeLayerModel[] | null = null;
    private selectSingleItem(li: HTMLElement, currentItem: INodeLayerModel): void {
        this._store.viewer?.scene.setObjectsSelected(this._viewer.scene.objectIds, false);
        //Lưu lại dòng được chọn
        this.selectedRows = [currentItem]

        const allTreeItems = this.containerBodyTree!.querySelectorAll('.dpu-tree-item');
        allTreeItems.forEach(item => item.classList.remove('active'));

        li.classList.add('active');

    }

    listDisplayItem: INodeLayerModel[] = [];
    private _buttonsMapHide = new Map<string, INodeLayerModel>()
    //#region handleDisplayItem
    handleDisplayItem = (startItem?: INodeLayerModel) => {

        if (this.newTreeViewNode.length == 0) return

        if (!startItem) {
            this.listDisplayItem = []

            const rootItems = this.newTreeViewNode.filter(item => item.parent === null || item.parent === undefined);
            // Set level cho root
            rootItems.forEach(root => {
                root.level = 0;
                this.listDisplayItem.push(root);
            });
            // Duyệt từng phần tử để xử lý expand
            let index = 0;
            while (index < this.listDisplayItem.length) {
                const currentItem = this.listDisplayItem[index];
                if (currentItem.expand) {
                    const children = this.newTreeViewNode.filter(item => item.parent?.id === currentItem.id);
                    children.forEach(child => {
                        child.level = (currentItem.level || 0) + 1;
                    });
                    // currentItem.childrenObject = children;
                    this.listDisplayItem.splice(index + 1, 0, ...children);
                }
                index++;
            }
        } else {
            // Tìm vị trí startItem
            const startIndex = this.listDisplayItem.findIndex(item => item.id === startItem.id);
            if (startIndex !== -1) {
                const children = this.newTreeViewNode.filter(item => item.parent?.id === startItem.id);
                children.forEach(child => {
                    child.level = (startItem.level || 0) + 1;
                    if (!startItem.isShow) {
                        this._buttonsMapHide.set(child.id, child); // Thêm vào map nếu không hiển thị
                    }
                });
                // Thêm children vào ngay sau startItem
                this.listDisplayItem.splice(startIndex + 1, 0, ...children);
                // Tạo stack để duyệt cây con
                let stack = [...children];
                while (stack.length > 0) {
                    const currentItem = stack.shift();
                    if (currentItem?.expand) {
                        const grandChildren = this.newTreeViewNode.filter(item => item.parent?.id === currentItem.id);
                        grandChildren.forEach(gc => {
                            gc.level = (currentItem.level || 0) + 1;
                        });

                        // Thêm grandChildren vào sau currentItem
                        const currentIndex = this.listDisplayItem.findIndex(item => item.id === currentItem.id);
                        this.listDisplayItem.splice(currentIndex + 1, 0, ...grandChildren);

                        // Thêm vào stack để duyệt tiếp nếu expand
                        stack.unshift(...grandChildren);
                    }
                }
            }
        }


    }

    //#region _handleRemoveDisplayItem
    handleRemoveDisplayItem = (parentItem: INodeLayerModel) => {
        // Tạo map tra cứu con nhanh
        const childMap = new Map<string, INodeLayerModel[]>();
        for (const node of this.newTreeViewNode) {
            if (node.parent?.id) {
                if (!childMap.has(node.parent.id)) childMap.set(node.parent.id, []);
                childMap.get(node.parent.id)!.push(node);
            }
        }

        // Gom tất cả id cần xóa bằng BFS
        const idsToRemove = new Set<string>();
        const queue: INodeLayerModel[] = [parentItem];
        while (queue.length > 0) {
            const current = queue.shift();
            if (!current) continue;
            const children = childMap.get(current.id) || [];
            for (const child of children) {
                idsToRemove.add(child.id);
                queue.push(child);
            }
        }
        // Xóa khỏi _listDisplayItem chỉ một lần
        this.listDisplayItem = this.listDisplayItem.filter(item => !idsToRemove.has(item.id));
    }



    //#region mapNewList
    treeViewNodes: Map<string, TreeViewNode> | undefined;
    private _treeViewTrees: Map<string, TreeViewNode> | undefined;
    listParentNode: Map<string, INodeLayerModel> = new Map();
    mapNewList = (newList: INodeLayerModel[]) => {

        this.treeViewNodes = (this.treeViewPlugin as any)._objectNodes;
        this._treeViewTrees = (this.treeViewPlugin as any)._nodeNodes;


        // Đảm bảo ref gốc, không clone object
        const uniqueMap = new Map<string, INodeLayerModel>();
        newList.forEach((item: INodeLayerModel) => {
            if (item.children && item.children.length > 0) {
                this.listParentNode.set(item.id, item);
            }
            if (!uniqueMap.has(item.id)) {
                uniqueMap.set(item.id, item); // giữ ref gốc
            }
        });
        this.newTreeViewNode = Array.from(uniqueMap.values());

        this.newTreeViewNode.forEach((item: INodeLayerModel) => {
            if (item.nodeXeokit) {
                const itemNode = item.nodeXeokit
                const itemNewTree = (this._treeViewTrees as any)[item.nodeXeokit.nodeId]
                if (itemNewTree && itemNode) {
                    item.nodeXeokit = itemNewTree;
                }

                // console.log(itemNewTree === itemNode, itemNewTree?.nodeId, itemNode?.nodeId);

            }
            if (item.listNodesXeokit && item.listNodesXeokit.length > 0) {
                item.listNodesXeokit = item.listNodesXeokit.map((oldNode: TreeViewNode) => {
                    const newNode = (this._treeViewTrees as any)[oldNode.nodeId];
                    if (newNode) {
                        return newNode;
                    }
                    return oldNode;
                });
            }
        })


        this.handleDisplayItem();
        this.handleRefreshList();



        // this.treeViewNodes = (this.treeViewPlugin as any)._objectNodes;
        // this._treeViewTrees = (this.treeViewPlugin as any)._nodeNodes;
        // // console.log('this.treeViewPlugin', this.treeViewPlugin);

        // // Đảm bảo ref gốc, không clone object
        // const uniqueMap = new Map<string, INodeLayerModel>();
        // newList.forEach((item: INodeLayerModel) => {
        //     if (item.children && item.children.length > 0) {
        //         this.listParentNode.set(item.id, item);
        //     }
        //     if (!uniqueMap.has(item.id)) {
        //         uniqueMap.set(item.id, item); // giữ ref gốc
        //     }
        // });
        // this.newTreeViewNode = Array.from(uniqueMap.values());

        // this.newTreeViewNode.forEach((item: INodeLayerModel) => {
        //     if (item.nodeXeokit) {
        //         const itemNode = item.nodeXeokit
        //         const itemNewTree = (this._treeViewTrees as any)[item.nodeXeokit.nodeId]
        //         if (itemNewTree && itemNode) {
        //             item.nodeXeokit = itemNewTree;
        //         }

        //         // console.log(itemNewTree === itemNode, itemNewTree?.nodeId, itemNode?.nodeId);

        //     }
        //     if (item.listNodesXeokit && item.listNodesXeokit.length > 0) {
        //         item.listNodesXeokit = item.listNodesXeokit.map((oldNode: TreeViewNode) => {
        //             const newNode = (this._treeViewTrees as any)[oldNode.nodeId];
        //             if (newNode) {
        //                 return newNode;
        //             }
        //             return oldNode;
        //         });
        //     }
        // })


        // this.handleDisplayItem();
        // this.handleRefreshList();


        // console.log("New TreeViewNode list:", this.newTreeViewNode);
    }

    //#region _handleRefreshList 
    handleRefreshList = () => {
        if (this.containerBodyTree && !this._list) {
            // const height = this.containerUl?.getBoundingClientRect().height || window.innerHeight;
            this._config.total = this.newTreeViewNode.length
            this._list = new HyperList(this.containerBodyTree!, this._config)
        }


        this._config.total = this.listDisplayItem.length
        this._list?.refresh(this.containerBodyTree!, this._config)

    }

    //#region _checkDisplayParentNode
    private _checkDisplayParentNode = (nodeParent: INodeLayerModel): boolean => {

        for (let node of nodeParent.listNodesXeokit || []) {
            if (node.numEntities
                === node.numVisibleEntities) {
                return true
            }

        }
        return false
    }
    //#region scrollToObjectId
    // scrollToObjectId = (objectId: string[]) => {

    //     if (!this._isOpenningModal) return;

    //     if (objectId.length > 0) {
    //         Array.from(this.listParentNode.keys()).forEach((key: string) => {
    //             const parentNode = this.listParentNode.get(key);

    //             if (parentNode && !parentNode.expand) {
    //                 parentNode.expand = true;
    //             }
    //         });
    //         this.handleDisplayItem(); // Cập nhật danh sách hiển thị sau khi mở rộng các node cha
    //     }
    //     const items = objectId.map(id => this.newTreeViewNode.find(item => item.id === id)).filter((item): item is INodeLayerModel => !!item);
    //     this.selectedRow = items; // Lưu lại dòng được chọn

    //     const indices = objectId.map(id => this.listDisplayItem.findIndex(item => item.id === id));
    //     const validIndices = indices.filter(index => index !== -1);
    //     if (validIndices.length > 0 && this._list) {
    //         this._list?.scrollToIndex(validIndices[0]);
    //     }

    //     this.handleRefreshList();


    // }

    // Chuẩn bị dữ liệu gửi sang worker
    scrollToObjectId = (objectIds: string[]) => {
        if (!this._isOpenningModal) return;
        const treeViewPlugin = this.treeViewPlugin as any;
        // const objectIdSets = objectIds.map(id => `${treeViewPlugin._id}-${id}`);//Tạm ngưng để dùng cho glb
        const objectIdSets = objectIds;//Điều chỉnh cho glb

        // Chuẩn bị dữ liệu đơn giản cho worker
        const listParentNodeData = Array.from(this.listParentNode.entries()).map(([key, node]) => ({
            key,
            expand: node.expand
        }));

        const workerCode = `(${scrollWorkerFunction.toString()})()`;
        const blob = new Blob([workerCode], { type: "application/javascript" });
        const worker = new Worker(URL.createObjectURL(blob));

        worker.postMessage({
            objectId: objectIdSets,
            listParentNode: listParentNodeData,
            newTreeViewNode: this.newTreeViewNode.map(node => ({ id: node.id })),
            listDisplayItem: this.listDisplayItem.map(node => ({ id: node.id }))
        });

        worker.onmessage = (event) => {
            const { expandedKeys, selectedItems, scrollIndex } = event.data as {
                expandedKeys: string[];
                selectedItems: { id: string }[];
                scrollIndex: number;
            };

            // 1) Apply expansions reported by worker
            expandedKeys.forEach((key: string) => {
                const parentNode = this.listParentNode.get(key);
                if (parentNode) parentNode.expand = true;
            });

            // 2) Recompute visible list with expanded nodes
            this.handleDisplayItem();

            // 3) Restore selections by id
            this.selectedRows = selectedItems
                .map((item: any) => this.newTreeViewNode.find(n => n.id === item.id))
                .filter(Boolean) as any;

            // 4) Refresh virtual list BEFORE trying to scroll
            this.handleRefreshList();

            // 5) Recompute the target index on the UPDATED list
            const targetIds: string[] = (selectedItems && selectedItems.length > 0)
                ? selectedItems.map(i => i.id)
                : objectIdSets; // fallback to requested ids

            const recomputedIndex = targetIds
                .map(id => this.listDisplayItem.findIndex(item => item.id === id))
                .find(idx => idx !== -1);

            const finalIndex = (recomputedIndex !== undefined && recomputedIndex !== -1)
                ? recomputedIndex
                : scrollIndex;

            // 6) Scroll after DOM/virtual list is ready (next frame)
            if (finalIndex !== -1 && this._list) {
                requestAnimationFrame(() => this._list!.scrollToIndex(finalIndex));
            }

            worker.terminate();
        };
    }
    //#region _handleFilterTree
    private _filterLayerModel: FilterLayerModel | null = null;
    private _handleFilterTree = () => {
        if (!this._filterLayerModel) {
            this._filterLayerModel = new FilterLayerModel();
        } else {
            this._filterLayerModel.isShow = true;
        }
    }

    private _isLoading: boolean = false;
    get isLoading(): boolean {
        return this._isLoading;
    }
    set isLoading(value: boolean) {
        this._isLoading = value;
        this.createLoading(value)
    }

    //#region createLoading
    private _loadingElement: HTMLElement | null = null;
    createLoading = (isShowLoading: boolean) => {
        const containerTree = this._containerBodyDialog.querySelector('.dpu__container-tree')

        if (!this._loadingElement) {
            this._loadingElement = document.createElement('div');
            this._loadingElement.style.cssText = "position: absolute;top:0;width: 100%;height: 100%;background-color: rgba(255, 255, 255, 0.2);z-index: 9999;";

            this._loadingElement.innerHTML = `<div style=" display:flex;justify-content:center;align-items:center ;height: 100%;"><img class = "dpu-loading-animation" src="${loading}" alt="Loading..." width="35" height ="35"  style="pointer-events: none;"/></div>`
        }
        if (isShowLoading) {
            // this._containerBodyDialog.style.position = 'relative';
            containerTree?.appendChild(this._loadingElement);
        } else {
            if (this._loadingElement && containerTree?.contains(this._loadingElement)) {
                containerTree.removeChild(this._loadingElement);
            }
        }
    }
}

// Định nghĩa type cho dữ liệu truyền qua worker (chỉ dùng dữ liệu đơn giản)
interface ScrollWorkerInput {
    objectId: string[];
    listParentNode: { key: string; expand: boolean }[];
    newTreeViewNode: { id: string }[];
    listDisplayItem: { id: string }[];
}

interface ScrollWorkerOutput {
    expandedKeys: string[];
    selectedItems: { id: string }[];
    scrollIndex: number;
}

// Tách logic worker ra ngoài class, có type rõ ràng
//#region scrollWorkerFunction
function scrollWorkerFunction() {
    self.onmessage = function (event: MessageEvent<ScrollWorkerInput>) {
        const { objectId, listParentNode, newTreeViewNode, listDisplayItem } = event.data;

        // Mở rộng các node cha
        const expandedKeys: string[] = [];
        if (objectId.length > 0 && listParentNode) {
            for (const { key, expand } of listParentNode) {
                if (!expand) {
                    expandedKeys.push(key);
                }
            }
        }

        // Tìm các item được chọn
        const selectedItems = objectId
            .map(id => newTreeViewNode.find(item => item.id === id))
            .filter(item => !!item);

        // Tìm index để scroll
        const indices = objectId.map(id => listDisplayItem.findIndex(item => item.id === id));
        const validIndices = indices.filter(index => index !== -1);

        const output: ScrollWorkerOutput = {
            expandedKeys,
            selectedItems,
            scrollIndex: validIndices.length > 0 ? validIndices[0] : -1
        };

        self.postMessage(output);
    };




}