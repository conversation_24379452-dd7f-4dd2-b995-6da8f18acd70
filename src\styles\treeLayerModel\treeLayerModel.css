/* <PERSON>ăn chỉnh caret */
.caret {
  /* margin-right: auto; */
  /* flex-grow: 1;*/
  display: flex;
  width: 100%;
  align-items: center;
  padding-right: 45px;
  position: relative;
  height: 33px;
}
.caret .text {
  width: 100%;
  white-space: nowrap; /* <PERSON><PERSON><PERSON> văn bản xuống dòng */
  overflow: hidden; /* Ẩn phần văn bản thừa */
  text-overflow: ellipsis; /* Thêm dấu "..." khi văn bản bị cắt */
}
.button-container {
  white-space: nowrap;
  position: absolute;
  display: flex;
  align-items: center;
  right: 10px;
  height: 33px;
}
.folder-button {
  width: 20px;
  height: 20px;
  margin-left: 5px;
}
.search-button {
  width: 17px;
  height: 16px;
  margin-left: 5px;
}
.folder-button svg,
.search-button svg {
  fill: var(--color-icon-1);
  stroke: var(--color-icon-1);
}
/* <PERSON><PERSON><PERSON> tên */
.arrow-icon {
  margin-right: 10px;
  font-size: 16px;
  transition: 0.1s;
  user-select: none;
}
.arrow-icon svg {
  height: 12px;
  width: 12px;
}
.dpu-tree-item.active .arrow-icon svg {
  fill: #fff;
}
.dpu-tree-item.active .folder-button svg,
.dpu-tree-item.active .search-button svg {
  fill: #fff;
  stroke: #fff;
}
.arrow-icon svg {
  transition: 0.1s;
  fill: var(--color-text-1);
  transform: rotate(-90deg);
}
/*.arrow-icon.arrow-down svg {
     transform: rotate(0deg);  Quay mũi tên khi mở 
  }
  */
/* Placeholder thay thế mũi tên */
.arrow-placeholder {
  width: 12px; /* Kích thước tương tự mũi tên */
  display: inline-block;
  margin-right: 10px;
  flex-shrink: 0;
}

/* Nested items */
.nested {
  display: none;
  padding-left: 10px;
}

.nested.active {
  display: block;
}

/* Khi mở rộng, mũi tên sẽ quay */
.arrow-down {
  transform: rotate(90deg); /* Quay mũi tên 90 độ */
}
.container-treelist {
  font-size: 14px;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;

  overflow: auto;
  position: relative;
  background-color: var(--background-color-4);
}
.dpu-tree-item {
  cursor: pointer;
  margin: 5px 0;
  display: flex; /* Đảm bảo các phần tử con (text và buttons) nằm ngang */
  justify-content: space-between; /* Đẩy phần tử text và buttonContainer ra hai bên */
  align-items: center; /* Căn giữa các phần tử theo chiều dọc */
  padding: 0px 10px;
  width: 100%;
}

.dpu__container-tree {
  height: calc(100% - 44px);
  border: 1px solid #ced4da;
  margin: 0 4px 4px 4px;
  border-radius: 4px;
  position : relative;

}

.dpu-tree-item:hover > .fullrow {
  background: var(--primary-color-hover-2);
}

.dpu-tree-item.active > .fullrow {
  background: var(--primary-color-1);
}
.dpu-tree-item.active > .caret {
  color: #fff;
}
.fullrow {
  height: 32px;
  width: 100%;
  position: absolute;
  background: transparent;
  left: 0;
  overflow: hidden;
}

/* Test mới */
.caret--folder {
  width: 10px;
  height: 10px;
  line-height: 10px;
  margin-right: 5px;
}
