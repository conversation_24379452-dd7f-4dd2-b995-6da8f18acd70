
.footer-info-container {
  height: 20px;
  font-size: 10px;
  color: var(--color-text-1);
  border-top: 1px solid var(--border-color-1);
  background-color: var(--background-color-1);
  overflow: hidden;
  width: 100%;
  display: flex;
  user-select: none;
  align-items: center;
  position: absolute;
  bottom: 0;
  opacity: 0.85;
}
.footer-info-content {
  width: 130px;
  color: var(--color-text-4);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 5px;
}

.footer-info-content span {
  font-weight: 600;
  color: var(--color-text-1);
}
.footer-info-container .footer-info-content:nth-child(1) {
  flex-grow: 1;
}
.footer-info-container .footer-info-content:nth-child(2) {
  width: 110px;
}

.footer-info-container .footer-info-content:nth-child(3),
.footer-info-container .footer-info-content:nth-child(4) {
  width: 105px;
}

.footer-info-content img {
  height: 100%;
  width: 47px;
  margin-left: 10px;
}



@media only screen and (max-width: 650px) {
  .footer-info-container .footer-info-content:nth-child(5),
  .footer-info-container .footer-info-content:nth-child(6) {
    display: none;
  }
}

@media only screen and (max-width: 400px) {
  .footer-info-container .footer-info-content:nth-child(1) {
    display: block;
  }

  .footer-info-container .footer-info-content {
    display: none;
  }
}
