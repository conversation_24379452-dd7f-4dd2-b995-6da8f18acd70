.dpu-setting-container {
  padding: 5px;
}

.dpu-slider-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dpu__slider {
  margin-right: 5px;
}

.dpu__input-value {
  /* margin-left: 10px; */
  max-width: 65px;
}

.dpu__switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
  margin-left: 5px;
}

.dpu__switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.dpu__switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.dpu__switch-slider:before {
  position: absolute;
  content: "";
  height: 9px;
  width: 9px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.dpu-switch-container {
  width: fit-content;
  display: flex;
  align-items: center;
}

input:checked + .dpu__switch-slider {
  background-color: var(--primary-color-1);
}

input:focus + .dpu__switch-slider {
  box-shadow: 0 0 1px var(--primary-color-1);
}

input:checked + .dpu__switch-slider:before {
  -webkit-transform: translateX(15px);
  -ms-transform: translateX(15px);
  transform: translateX(15px);
}

/* Rounded sliders */
.dpu__switch-slider.round {
  border-radius: 34px;
}

.dpu__switch-slider.round:before {
  border-radius: 50%;
}
