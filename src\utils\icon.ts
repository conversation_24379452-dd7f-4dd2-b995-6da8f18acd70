
export const IconRulerTool = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" 
xmlns:xlink="http://www.w3.org/1999/xlink"
   
    version="1.1" 
    id="Layer_1" 
    viewBox="0 0 490 490" 
    xml:space="preserve" 
class="mdl-js">
<g>
<g>
<g>
<path d="M122.704,0.238H8.274C3.704,0.238,0,3.942,0,8.512v472.976c0,4.57,3.704,8.274,8.274,8.274h114.43     c4.57,0,8.274-3.705,8.274-8.274V8.512C130.979,3.942,127.274,0.238,122.704,0.238z M114.43,473.214H16.549v-36.852h60.384     c4.57,0,8.274-3.704,8.274-8.274c0-4.57-3.705-8.274-8.274-8.274H16.549v-24.137h22.24c4.57,0,8.274-3.705,8.274-8.274     c0-4.57-3.704-8.274-8.274-8.274h-22.24V354.99h22.24c4.57,0,8.274-3.704,8.274-8.274c0-4.57-3.704-8.274-8.274-8.274h-22.24     v-24.137h60.384c4.57,0,8.274-3.705,8.274-8.274c0-4.57-3.705-8.274-8.274-8.274H16.549V273.62h22.24     c4.57,0,8.274-3.704,8.274-8.274c0-4.57-3.704-8.274-8.274-8.274h-22.24v-24.137h22.24c4.57,0,8.274-3.704,8.274-8.274     s-3.704-8.274-8.274-8.274h-22.24V192.25h60.384c4.57,0,8.274-3.705,8.274-8.274c0-4.57-3.705-8.274-8.274-8.274H16.549v-24.137     h22.24c4.57,0,8.274-3.704,8.274-8.274s-3.704-8.274-8.274-8.274h-22.24V110.88h22.24c4.57,0,8.274-3.705,8.274-8.274     c0-4.57-3.704-8.274-8.274-8.274h-22.24V70.187h60.384c4.57,0,8.274-3.704,8.274-8.274c0-4.57-3.705-8.274-8.274-8.274H16.549     V16.786h97.881V473.214z" />
<path d="M488.573,476.843L167.695,3.866c-2.04-3.007-5.805-4.33-9.278-3.264c-3.474,1.067-5.844,4.276-5.844,7.91v472.976     c0,4.57,3.704,8.274,8.274,8.274h320.878c3.064,0,5.876-1.693,7.311-4.4C490.471,482.656,490.293,479.378,488.573,476.843z      M169.122,473.214V35.447l296.991,437.767H169.122z" />
<path d="M214.723,428.734h154.821c3.064,0,5.877-1.693,7.311-4.399c1.435-2.707,1.256-5.985-0.463-8.521L221.57,187.607     c-2.04-3.008-5.804-4.33-9.277-3.264c-3.474,1.067-5.844,4.276-5.844,7.91V420.46     C206.448,425.029,210.153,428.734,214.723,428.734z M222.997,219.187l130.934,192.998H222.997V219.187z" />
        </g>
    </g>
</g>
</svg>`
}


export const IconHomeLocation = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
   
version="1.1" id="Layer_1"  
viewBox="0 0 300 300" enable-background="new 0 0 300 300" xml:space="preserve" class="mdl-js">
<g>
<path d="M287.991,131.752L158.449,20.347c-4.861-4.175-12.036-4.175-16.897,0L12.009,131.752   c-5.424,4.667-6.042,12.846-1.379,18.271c4.676,5.421,12.852,6.036,18.27,1.376L150,47.257l121.1,104.141   c2.449,2.103,5.449,3.129,8.436,3.129c3.649,0,7.273-1.527,9.834-4.505C294.034,144.598,293.415,136.418,287.991,131.752z" />
<path d="M150,137.043c-16.075,0-29.149,13.075-29.149,29.149c0,16.074,13.075,29.152,29.149,29.152s29.149-13.078,29.149-29.152   C179.149,150.118,166.075,137.043,150,137.043z" />
<path d="M154.23,65.419c-2.437-2.093-6.024-2.093-8.461,0l-97.622,84.208c-1.423,1.228-2.245,3.018-2.245,4.901v121.781   c0,3.575,2.9,6.476,6.476,6.476h195.244c3.575,0,6.476-2.901,6.476-6.476V154.528c0-1.883-0.822-3.674-2.245-4.901L154.23,65.419z    M155.103,259.241c-1.225,1.574-3.105,2.496-5.103,2.496s-3.878-0.921-5.103-2.496c-5.115-6.547-49.955-64.799-49.955-93.048   c0-30.362,24.702-55.058,55.058-55.058s55.058,24.696,55.058,55.058C205.058,194.442,160.217,252.694,155.103,259.241z" />
</g>
        </svg>`
}

export const IconYourLocation = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
     version="1.1" id="_x32_" viewBox="0 0 512 512" xml:space="preserve" class="mdl-js">
    
    <g>
    <path  d="M405.974,62.12C364.561,20.707,310.275,0,255.997,0C201.72,0,147.443,20.707,106.029,62.12   c-82.814,82.81-82.814,217.11,0,299.929L255.997,512l149.976-149.951C488.784,279.23,488.784,144.93,405.974,62.12z    M382.328,338.394L255.997,464.708L129.68,338.403c-69.653-69.657-69.653-182.988,0-252.637   c33.738-33.738,78.601-52.322,126.318-52.322c47.717,0,92.584,18.584,126.322,52.322   C451.977,155.414,451.977,268.746,382.328,338.394z" />
    <path  d="M255.997,213.207c30.513,0,55.249-24.724,55.249-55.253c0-30.513-24.736-55.245-55.249-55.245   c-30.521,0-55.257,24.732-55.257,55.245C200.74,188.483,225.477,213.207,255.997,213.207z" />
    <path  d="M255.997,233.979c-43.785,0-79.274,35.502-79.274,79.274h158.546   C335.268,269.481,299.775,233.979,255.997,233.979z" />
    </g>
    </svg>`
}


export const IconPointTool = () => {
    return ` <svg viewBox="0 0 16.00 16.00" xmlns="http://www.w3.org/2000/svg" class="bi bi-pin-map"  stroke-width="0.00016">
       <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
       <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke-width="0.096"></g>
       <g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" d="M3.1 11.2a.5.5 0 0 1 .4-.2H6a.5.5 0 0 1 0 1H3.75L1.5 15h13l-2.25-3H10a.5.5 0 0 1 0-1h2.5a.5.5 0 0 1 .4.2l3 4a.5.5 0 0 1-.4.8H.5a.5.5 0 0 1-.4-.8l3-4z"></path> 
           <path fill-rule="evenodd" d="M8 1a3 3 0 1 0 0 6 3 3 0 0 0 0-6zM4 4a4 4 0 1 1 4.5 3.969V13.5a.5.5 0 0 1-1 0V7.97A4 4 0 0 1 4 3.999z"></path> 
           </g>
           </svg>`
}

export const IconMeasureRuler = () => {
    return `  <svg 
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink"
      
     version="1.1" id="Layer_1" 
     viewBox="0 0 512 512" xml:space="preserve" 
     class="mdl-js">
 <g>
 <g>
 <path d="M503.467,290.133H8.533c-4.719,0-8.533,3.814-8.533,8.533v102.4c0,4.719,3.814,8.533,8.533,8.533h494.933    c4.719,0,8.533-3.814,8.533-8.533v-102.4C512,293.948,508.186,290.133,503.467,290.133z M494.933,392.533H17.067V307.2h25.6    l-0.009,17.067c0,4.71,3.814,8.533,8.533,8.533c4.71,0,8.533-3.814,8.533-8.533l0.009-17.067h34.133v34.133    c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533V307.2h34.133v17.067c0,4.719,3.814,8.533,8.533,8.533    c4.719,0,8.533-3.814,8.533-8.533V307.2h34.133v34.133c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533V307.2h34.133    v17.067c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533V307.2h34.133v34.133c0,4.719,3.814,8.533,8.533,8.533    s8.533-3.814,8.533-8.533V307.2h34.133v17.067c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533V307.2h34.133v34.133    c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533V307.2h34.15l-0.009,17.067c0,4.71,3.814,8.533,8.533,8.533    c4.71,0,8.533-3.814,8.533-8.533l0.009-17.067h25.583V392.533z" />
     </g>
 </g>
 <g>
 <g>
 <path d="M8.533,102.4C3.814,102.4,0,106.214,0,110.933V256c0,4.719,3.814,8.533,8.533,8.533s8.533-3.814,8.533-8.533v-68.267    v-76.8C17.067,106.214,13.252,102.4,8.533,102.4z" />
     </g>
 </g>
 <g>
 <g>
 <path d="M503.467,102.4c-4.719,0-8.533,3.814-8.533,8.533v76.8V256c0,4.719,3.814,8.533,8.533,8.533    c4.719,0,8.533-3.814,8.533-8.533V110.933C512,106.214,508.186,102.4,503.467,102.4z" />
     </g>
 </g>
 <g>
 <g>
 <path d="M476.032,182.263l-39.62-51.2c-2.142-2.765-5.658-3.763-8.789-2.56c-3.132,1.229-5.222,4.437-5.222,8.03V179.2H89.6    v-42.675c0-3.593-2.091-6.801-5.222-8.021c-3.132-1.22-6.639-0.213-8.789,2.56l-39.62,51.208c-1.22,1.579-1.835,3.524-1.835,5.461    s0.614,3.883,1.835,5.47l39.62,51.2c1.536,1.98,3.78,3.063,6.084,3.063c0.905,0,1.818-0.171,2.697-0.512    c3.132-1.229,5.222-4.437,5.222-8.021v-42.667h332.8v42.667c0,3.593,2.091,6.801,5.222,8.021c0.879,0.341,1.792,0.512,2.697,0.512    c2.304,0,4.548-1.084,6.093-3.063l39.62-51.2c1.229-1.587,1.843-3.524,1.843-5.47S477.252,183.851,476.032,182.263z     M73.754,215.364l-21.385-27.631l21.385-27.631V215.364z M438.246,215.364v-55.262l21.385,27.631L438.246,215.364z" />
     </g>
 </g>
 </svg>`
}

export const IconMeasureMultiRuler = () => {
    return ` <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--gis" preserveAspectRatio="xMidYMid meet" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M34.266 0C28.07 0 22.982 5.09 22.982 11.285c0 3.494 1.621 6.634 4.145 8.71L18.021 38.81a11.081 11.081 0 0 0-1.736-.149C10.091 38.662 5 43.751 5 49.945C5 56.14 10.09 61.23 16.285 61.23c5.98 0 10.883-4.755 11.219-10.657a3.147 3.147 0 0 0 .062-.627a3.147 3.147 0 0 0-.062-.627c-.176-3.093-1.616-5.862-3.799-7.806l9.211-19.033c.444.053.892.087 1.35.087c4.148 0 7.775-2.287 9.722-5.658l16.01 4.23c.84 5.256 5.35 9.337 10.791 9.503l6.299 19.2c-2.81 2.06-4.654 5.375-4.654 9.096c0 6.195 5.088 11.282 11.283 11.282c5.98 0 10.885-4.753 11.22-10.655a3.147 3.147 0 0 0 .063-.627a3.147 3.147 0 0 0-.063-.626c-.335-5.902-5.24-10.659-11.22-10.659c-.24 0-.475.02-.711.035L76.889 29.04c3.102-1.88 5.243-5.212 5.46-9.035a3.147 3.147 0 0 0 .063-.627a3.147 3.147 0 0 0-.062-.627c-.336-5.902-5.241-10.658-11.221-10.658c-4.585 0-8.558 2.792-10.317 6.754L45.51 10.799a3.147 3.147 0 0 0-.024-.14C45.151 4.756 40.246 0 34.266 0zm0 6.293c2.793 0 4.99 2.199 4.99 4.992s-2.197 4.988-4.99 4.988c-2.794 0-4.99-2.195-4.99-4.988c0-2.793 2.196-4.992 4.99-4.992zm36.863 8.092c2.793 0 4.99 2.199 4.99 4.992s-2.197 4.988-4.99 4.988s-4.99-2.195-4.99-4.988c0-2.793 2.197-4.992 4.99-4.992zm-54.844 30.57c2.793 0 4.988 2.197 4.988 4.99s-2.195 4.99-4.988 4.99c-2.793 0-4.992-2.197-4.992-4.99s2.199-4.99 4.992-4.99zm67.432 8.99c2.793 0 4.99 2.2 4.99 4.992c0 2.794-2.197 4.989-4.99 4.989s-4.99-2.195-4.99-4.989c0-2.793 2.197-4.992 4.99-4.992zM5 76v24h90V76h-6.27v17.896h-7.687V81.174h-6.27v12.722h-7.681V81.174h-6.268v12.722h-7.69V76h-6.269v17.896h-7.69V81.174h-6.267v12.722h-7.681V81.174h-6.27v12.722H11.27V76H5z"  fill-rule="evenodd"></path></g></svg>`
}

export const IconEraser = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" 
xmlns:xlink="http://www.w3.org/1999/xlink" 
 version="1.1" id="Layer_1" viewBox="0 0 512 512" xml:space="preserve" class="mdl-js">
<g>
<g>
<path d="M495.276,133.96L377.032,15.715c-19.605-19.608-51.34-19.609-70.946,0L40.37,281.428    c-19.557,19.56-19.557,51.386,0.001,70.946l61.153,61.153c9.475,9.476,22.074,14.693,35.473,14.693h114.188    c13.4,0,25.998-5.219,35.473-14.693l25.678-25.678v-0.001l182.941-182.942C514.837,185.347,514.837,153.52,495.276,133.96z     M263.009,389.878c-3.158,3.158-7.358,4.897-11.824,4.897H136.997c-4.467,0-8.666-1.739-11.824-4.897l-61.152-61.152    c-6.521-6.521-6.521-17.129-0.001-23.65l70.948-70.948l141.895,141.895L263.009,389.878z M300.512,352.375L158.617,210.48    L273.973,95.124l141.894,141.894L300.512,352.375z M471.629,181.258l-32.113,32.113L297.622,71.475l32.113-32.113    c6.522-6.521,17.129-6.519,23.65,0l118.244,118.245C478.148,164.128,478.148,174.737,471.629,181.258z" />
    </g>
</g>
<g>
<g>
<path d="M495.278,477.546H16.722C7.487,477.546,0,485.034,0,494.269s7.487,16.722,16.722,16.722h478.555    c9.235,0,16.722-7.487,16.722-16.722S504.513,477.546,495.278,477.546z" />
    </g>
</g>
</svg>`
}

export const IconEyeShow = () => {
    return ` <svg xmlns="http://www.w3.org/2000/svg" 
 xmlns:cc="http://creativecommons.org/ns#" 
 xmlns:dc="http://purl.org/dc/elements/1.1/" 
 xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" 
 xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
 xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" 
 xmlns:svg="http://www.w3.org/2000/svg" 
 viewBox="0 0 24 24" id="SVGRoot" version="1.1" class="mdl-js">

     <defs id="defs2"></defs>

     <g id="layer1">

         <path d="M 12 4 C 8.6666667 4 5.7335794 5.6316279 3.6191406 7.3496094 C 2.5619212 8.2086001 1.7059096 9.0941755 1.09375 9.859375 C 0.78767021 10.241975 0.54228306 10.592509 0.35742188 10.916016 C 0.17256067 11.239523 3.7007434e-17 11.458333 0 12 C 0 12.541667 0.17256067 12.760477 0.35742188 13.083984 C 0.54228307 13.407491 0.78767021 13.758025 1.09375 14.140625 C 1.7059096 14.905824 2.5619212 15.7914 3.6191406 16.650391 C 5.7335794 18.368372 8.6666667 20 12 20 C 15.333333 20 18.266421 18.368372 20.380859 16.650391 C 21.438079 15.7914 22.29409 14.905824 22.90625 14.140625 C 23.21233 13.758025 23.457717 13.407491 23.642578 13.083984 C 23.827439 12.760477 24 12.541667 24 12 C 24 11.458333 23.827439 11.239523 23.642578 10.916016 C 23.457717 10.592509 23.21233 10.241975 22.90625 9.859375 C 22.29409 9.0941755 21.438079 8.2086001 20.380859 7.3496094 C 18.266421 5.6316279 15.333333 4 12 4 z M 12 6 C 14.666667 6 17.233579 7.3683721 19.119141 8.9003906 C 20.061921 9.6663999 20.83091 10.468324 21.34375 11.109375 C 21.60017 11.4299 21.792283 11.712179 21.904297 11.908203 C 21.926717 11.947439 21.926155 11.965225 21.939453 12 C 21.926155 12.034775 21.926717 12.052561 21.904297 12.091797 C 21.792283 12.287821 21.60017 12.5701 21.34375 12.890625 C 20.83091 13.531676 20.061921 14.3336 19.119141 15.099609 C 17.233579 16.631628 14.666667 18 12 18 C 9.3333333 18 6.7664206 16.631628 4.8808594 15.099609 C 3.9380788 14.3336 3.1690904 13.531676 2.65625 12.890625 C 2.3998298 12.5701 2.2077169 12.287821 2.0957031 12.091797 C 2.0732826 12.052561 2.073845 12.034775 2.0605469 12 C 2.073845 11.965225 2.0732826 11.947439 2.0957031 11.908203 C 2.2077169 11.712179 2.3998298 11.4299 2.65625 11.109375 C 3.1690904 10.468324 3.9380788 9.6663999 4.8808594 8.9003906 C 6.7664206 7.3683721 9.3333333 6 12 6 z M 11.939453 8 A 4 4 0 0 0 8 12 A 4 4 0 0 0 12 16 A 4 4 0 0 0 16 12 A 4 4 0 0 0 15.822266 10.824219 A 2 2 0 0 1 14 12 A 2 2 0 0 1 12 10 A 2 2 0 0 1 13.177734 8.1777344 A 4 4 0 0 0 12 8 A 4 4 0 0 0 11.939453 8 z " id="path6180" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-variant-east-asian:normal;font-feature-settings:normal;font-variation-settings:normal;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;writing-mode:lr-tb;direction:ltr;text-orientation:mixed;dominant-baseline:auto;baseline-shift:baseline;text-anchor:start;white-space:normal;shape-padding:0;shape-margin:0;inline-size:0;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;vector-effect:none;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate;stop-color:#000000;stop-opacity:1;opacity:1"></path>

     </g>

 </svg>`
}

export const IconEyeHide = () => {
    return ` <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="SVGRoot" version="1.1" class="mdl-js"> <defs id="defs2"></defs> <g id="layer1"> <path d="M 20.029297 3 A 1 1 0 0 0 19.292969 3.2929688 L 17.257812 5.328125 C 15.687708 4.5455983 13.912737 4 12 4 C 8.6666667 4 5.7335794 5.6316279 3.6191406 7.3496094 C 2.5619212 8.2086001 1.7059096 9.0941755 1.09375 9.859375 C 0.78767021 10.241975 0.54228306 10.592509 0.35742188 10.916016 C 0.17256067 11.239523 3.7007434e-17 11.458333 0 12 C 0 12.541667 0.17256067 12.760477 0.35742188 13.083984 C 0.54228307 13.407491 0.78767021 13.758025 1.09375 14.140625 C 1.7059096 14.905824 2.5619212 15.7914 3.6191406 16.650391 C 4.0317573 16.985642 4.4832215 17.313668 4.9550781 17.630859 L 3.2929688 19.292969 A 1 1 0 0 0 3.2929688 20.707031 A 1 1 0 0 0 4.7070312 20.707031 L 6.7421875 18.671875 C 8.3122921 19.454402 10.087263 20 12 20 C 15.333333 20 18.266421 18.368372 20.380859 16.650391 C 21.438079 15.7914 22.29409 14.905824 22.90625 14.140625 C 23.21233 13.758025 23.457717 13.407491 23.642578 13.083984 C 23.827439 12.760477 24 12.541667 24 12 C 24 11.458333 23.827439 11.239523 23.642578 10.916016 C 23.457717 10.592509 23.21233 10.241975 22.90625 9.859375 C 22.29409 9.0941755 21.438079 8.2086001 20.380859 7.3496094 C 19.968243 7.0143584 19.516778 6.6863317 19.044922 6.3691406 L 20.707031 4.7070312 A 1 1 0 0 0 20.707031 3.2929688 A 1 1 0 0 0 20.029297 3 z M 12 6 C 13.296769 6 14.567291 6.3273006 15.75 6.8359375 L 12.068359 10.517578 A 2 2 0 0 1 12 10 A 2 2 0 0 1 13.177734 8.1777344 A 4 4 0 0 0 12 8 A 4 4 0 0 0 11.939453 8 A 4 4 0 0 0 8 12 A 4 4 0 0 0 8.5546875 14.03125 L 6.3984375 16.1875 C 5.8572135 15.84669 5.3474521 15.478716 4.8808594 15.099609 C 3.9380788 14.3336 3.1690904 13.531676 2.65625 12.890625 C 2.3998298 12.5701 2.2077169 12.287821 2.0957031 12.091797 C 2.0732826 12.052561 2.073845 12.034775 2.0605469 12 C 2.073845 11.965225 2.0732826 11.947439 2.0957031 11.908203 C 2.2077169 11.712179 2.3998298 11.4299 2.65625 11.109375 C 3.1690904 10.468324 3.9380788 9.6663999 4.8808594 8.9003906 C 6.7664206 7.3683721 9.3333333 6 12 6 z M 17.601562 7.8125 C 18.142787 8.1533101 18.652548 8.5212841 19.119141 8.9003906 C 20.061921 9.6663999 20.83091 10.468324 21.34375 11.109375 C 21.60017 11.4299 21.792283 11.712179 21.904297 11.908203 C 21.926717 11.947439 21.926155 11.965225 21.939453 12 C 21.926155 12.034775 21.926717 12.052561 21.904297 12.091797 C 21.792283 12.287821 21.60017 12.5701 21.34375 12.890625 C 20.83091 13.531676 20.061921 14.3336 19.119141 15.099609 C 17.233579 16.631628 14.666667 18 12 18 C 10.703231 18 9.4327086 17.672699 8.25 17.164062 L 9.96875 15.445312 A 4 4 0 0 0 12 16 A 4 4 0 0 0 16 12 A 4 4 0 0 0 15.822266 10.824219 A 2 2 0 0 1 14 12 A 2 2 0 0 1 13.482422 11.931641 L 17.601562 7.8125 z " id="circle2141" style="stroke:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none"></path> </g> </svg>`
}

export const IconTrash = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg"
xmlns:xlink="http://www.w3.org/1999/xlink" 
   
viewBox="0 0 32 32" 
version="1.1" class="mdl-js">
    <g id="icomoon-ignore">
    </g>
    <path d="M26.129 5.871h-5.331v-1.066c0-1.178-0.955-2.132-2.133-2.132h-5.331c-1.178 0-2.133 0.955-2.133 2.132v1.066h-5.331v1.066h1.099l1.067 20.259c0 1.178 0.955 2.133 2.133 2.133h11.729c1.178 0 2.133-0.955 2.133-2.133l1.049-20.259h1.051v-1.066zM12.268 4.804c0-0.588 0.479-1.066 1.066-1.066h5.331c0.588 0 1.066 0.478 1.066 1.066v1.066h-7.464v-1.066zM22.966 27.14l-0.002 0.027v0.028c0 0.587-0.478 1.066-1.066 1.066h-11.729c-0.587 0-1.066-0.479-1.066-1.066v-0.028l-0.001-0.028-1.065-20.203h15.975l-1.046 20.204z" fill="#000000">

    </path>
    <path d="M15.467 9.069h1.066v17.060h-1.066v-17.060z" fill="#000000">

    </path>
    <path d="M13.358 26.095l-1.091-17.027-1.064 0.068 1.091 17.027z" fill="#000000">

    </path>
    <path d="M20.805 9.103l-1.064-0.067-1.076 17.060 1.064 0.067z" fill="#000000">

    </path>
</svg>`
}

export const IconTick = () => {
    return `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="mdl-js">

<title/>

<g id="Complete">

<g id="tick">

<polyline fill="none" points="3.7 14.3 9.6 19 20.3 5" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>

</g>

</g>

</svg>`
}

export const IconPointMarket = () => {
    return ` <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 48 48" class="mdl-js">
     <title>location-current-solid</title>
     
         
         <g id="icons_Q2" data-name="icons Q2">
             <g>
                 <path d="M44,22H39.9A16.1,16.1,0,0,0,26,8.1h0V4a2,2,0,0,0-4,0V8h0A16.1,16.1,0,0,0,8.1,22H4a2,2,0,0,0,0,4H8.1A16.1,16.1,0,0,0,22,39.9h0v4a2,2,0,0,0,4,0V40h0A16.1,16.1,0,0,0,39.9,26H44a2,2,0,0,0,0-4ZM24,36A12,12,0,1,1,36,24,12,12,0,0,1,24,36Z" />
                 <circle cx="24" cy="24" r="7" />
             </g>
         </g>
     
 </svg>`
}

export const IconMapModel = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -3.5 157 157" fill="none" class="mdl-js">
    <g clip-path="url(#clip0)">
        <path d="M3.85747 101.836C2.04502 103.067 1.14325 104.225 1.01509 105.481C0.894688 106.668 1.48249 107.912 2.81334 109.284C5.04277 111.675 7.57088 113.766 10.3349 115.505C20.9714 121.903 31.6915 128.157 42.1991 134.093C48.3628 137.575 54.707 140.969 60.8415 144.252C63.4889 145.668 66.1341 147.087 68.7777 148.511C70.1125 149.325 71.6271 149.795 73.1878 149.881C74.1885 149.879 75.1711 149.612 76.0353 149.106C80.6959 146.449 85.7526 143.508 91.4955 140.112L99.1809 135.566C116.529 125.307 134.467 114.703 152.077 104.212C154.642 102.684 156.112 100.427 156.111 98.0195C156.111 95.5662 154.593 93.261 151.952 91.6942C149.682 90.3468 147.351 89.0318 144.883 87.6429C144 87.1448 143.092 86.6325 142.15 86.0994C143.236 85.3026 144.295 84.5286 145.328 83.7766C148.102 81.75 150.722 79.8364 153.319 77.8974C155.615 76.1831 156.808 74.0091 156.675 71.776C156.54 69.4825 155.033 67.3961 152.436 65.9C150.151 64.5837 147.874 63.255 145.51 61.8738L143.05 60.4394C143.904 59.7901 144.749 59.151 145.584 58.522C148.063 56.6512 150.405 54.8856 152.724 53.0161C154.65 51.4642 155.617 49.5602 155.445 47.6544C155.268 45.6842 153.908 43.9135 151.614 42.6681C148.521 40.9882 145.74 39.4576 143.036 37.7381L141.861 36.9895C122.483 24.6583 102.446 11.9077 81.2972 1.79471C76.3939 -0.550739 72.1146 -0.291026 67.8333 2.61092C62.4118 6.2856 57.0178 9.83813 51.5093 13.455C45.712 17.2646 39.9127 21.0685 34.1111 24.8668C24.6961 31.0356 14.96 37.4181 5.40256 43.722C2.83017 45.42 1.56073 47.0408 1.40732 48.8252C1.25844 50.5629 2.22817 52.3895 4.37139 54.4096C6.85601 56.6621 9.46302 58.7749 12.1805 60.7383C13.2323 61.5285 14.2836 62.3186 15.3199 63.1245C16.008 63.6596 16.7316 64.1505 17.4987 64.6707L17.887 64.9357C16.6054 65.9046 15.3444 66.852 14.1042 67.7786C10.7544 70.2883 7.58979 72.6598 4.4847 75.1637C2.30588 76.9169 1.29603 78.4838 1.30444 80.087C1.31351 81.702 2.35696 83.2721 4.58821 85.0312C6.33593 86.4084 8.22084 87.6474 10.0436 88.8461C11.1441 89.5688 12.2794 90.3162 13.3675 91.0825C14.1016 91.6227 14.8058 92.2026 15.4772 92.8195C15.6312 92.9565 15.7872 93.0955 15.9478 93.2357C14.815 94.0461 13.7037 94.8461 12.6141 95.6344C9.6106 97.8071 6.77227 99.8559 3.85747 101.836ZM30.6557 101.044C36.2873 104.187 42.113 107.437 47.8636 110.553C50.8412 112.165 53.892 113.74 56.8456 115.264C60.6647 117.234 64.6134 119.271 68.4185 121.401C72.2641 123.552 76.146 123.69 81.0079 121.849C93.9067 116.965 105.541 110.059 115.214 103.949C119.848 101.021 124.34 98.0656 129.101 94.9377C131.114 93.6143 133.149 92.2799 135.205 90.9338L146.32 97.787C143.49 99.5351 140.683 101.273 137.898 103C130.915 107.327 124.321 111.414 117.547 115.525C111.631 119.117 105.6 122.742 99.7673 126.247C92.0644 130.877 84.0955 135.662 76.2955 140.436C74.8151 141.339 73.8319 141.112 72.4246 140.337C67.4831 137.61 62.5391 134.887 57.5932 132.169C47.762 126.76 37.5967 121.168 27.6231 115.624C23.6933 113.44 19.8316 111.168 15.7433 108.763C14.1121 107.804 12.451 106.827 10.7441 105.831L23.4532 97.0318C25.8617 98.3695 28.2617 99.7072 30.6532 101.044H30.6557ZM136.212 54.6355C129.398 59.4459 122.961 63.9902 116.226 68.2877C108.417 73.2721 100.323 78.1065 92.4962 82.7818C87.9864 85.4753 83.3233 88.2611 78.7624 91.0455C76.9596 92.1494 75.6508 92.1234 73.7684 90.9552C64.1887 85.0136 54.424 79.0201 44.9792 73.2279C37.0731 68.3766 28.8984 63.3577 20.8763 58.3993C18.2871 56.7986 15.8274 55.0642 13.2246 53.2265C12.2752 52.5577 11.3148 51.8841 10.3435 51.2057C12.5581 49.6906 14.7359 48.1947 16.8767 46.7181C22.2105 43.0466 27.2491 39.5752 32.412 36.1557C38.4474 32.1551 44.6149 28.1331 50.5798 24.2441C56.3861 20.4577 62.3892 16.5427 68.2663 12.6505L68.4541 12.5259C76.502 7.19469 76.5026 7.19664 85.1221 11.7817C102.287 20.9109 119.822 31.6414 141.979 46.5751C142.601 46.994 143.204 47.4765 143.968 48.0882C144.198 48.272 144.442 48.4674 144.706 48.6765C141.82 50.6759 138.972 52.6855 136.211 54.6355H136.212ZM25.1886 69.2513C26.863 70.3604 28.5403 71.4799 30.2202 72.6091C34.2115 75.2818 38.338 78.0468 42.4931 80.652C48.9745 84.7182 55.6073 88.7779 62.0234 92.7039C65.6914 94.9494 69.3596 97.1994 73.0273 99.4539C76.3156 101.484 79.2847 100.222 81.2325 99.0974C83.7718 97.6305 86.3138 96.1675 88.859 94.7091C96.8364 90.1286 105.086 85.3916 113.078 80.5247C120.001 76.3091 126.576 71.7636 133.84 66.6864C135.726 65.3682 136.941 65.3468 138.655 66.5929C140.568 67.9844 142.576 69.2598 144.701 70.6097C145.393 71.0487 146.103 71.502 146.83 71.9675C145.351 73.0214 143.914 74.0656 142.5 75.0929C138.927 77.6903 135.552 80.1422 132.013 82.3786C116.294 92.3247 102.452 100.895 89.6953 108.58C87.167 110.01 84.5337 111.245 81.8183 112.275C80.3554 112.873 78.8413 113.492 77.3856 114.165C76.3887 114.658 75.2838 114.889 74.1743 114.836C73.0642 114.782 71.9864 114.446 71.042 113.86C61.8293 108.877 52.6082 103.909 43.3785 98.9578C34.2826 94.0656 25.1911 89.1688 16.1038 84.2669C14.4434 83.3688 12.8304 82.2942 11.1195 81.1572C10.5266 80.7624 9.91742 80.3572 9.28307 79.9429L25.1886 69.2513Z" />
    </g>
    <defs>
        <clipPath id="clip0">
            <rect width="156" height="150" transform="translate(0.777344)" />
        </clipPath>
    </defs>
</svg>`
}


export const IconLayerModel = () => {
    return `
    <svg viewBox="-3.2 -3.2 70.40 70.40" xmlns="http://www.w3.org/2000/svg" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <defs> <style>.cls-1{fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style> </defs> <title></title> <g data-name="Layer 49" id="Layer_49"> <rect class="cls-1" height="24" width="24" x="20" y="1.33"></rect> <rect class="cls-1" height="12.78" width="12.78" x="25.61" y="50.23"></rect> <rect class="cls-1" height="12.78" width="12.78" x="1.08" y="50.23"></rect> <rect class="cls-1" height="12.78" width="12.78" x="50.13" y="50.23"></rect> <line class="cls-1" x1="32" x2="32" y1="25.33" y2="50.23"></line> <polyline class="cls-1" points="56.52 50.23 56.52 33.83 7.48 33.83 7.48 50.23"></polyline> </g> </g></svg>
`
}

export const IconFlyCamera = () => {
    return `<svg version="1.1" id="Uploaded to svgrepo.com" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-1.92 -1.92 35.84 35.84" xml:space="preserve">
    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
    <g id="SVGRepo_iconCarrier"> 
    <path class="feather_een" d="M32,19v-4.306c0-0.978-0.707-1.812-1.671-1.973l-9.361-1.56L21,11V7c0-1.202-0.541-2.267-1.38-3h3.88 C24.328,4,25,3.328,25,2.5S24.328,1,23.5,1L17,2c0-0.552-0.448-1-1-1s-1,0.448-1,1L8.5,1C7.672,1,7,1.672,7,2.5S7.672,4,8.5,4h3.88 C11.541,4.733,11,5.798,11,7v4l0.032,0.161l-9.361,1.56C0.707,12.882,0,13.717,0,14.694V19c0,1.105,0.895,2,2,2h11l1.062,5.312 l-4.114,1.371c-0.524,0.175-0.807,0.741-0.632,1.265l0.456,1.368C9.908,30.725,10.29,31,10.721,31h10.558 c0.43,0,0.813-0.275,0.949-0.684l0.456-1.368c0.175-0.524-0.109-1.09-0.632-1.265l-4.114-1.371L19,21h11C31.105,21,32,20.105,32,19z M30,20H19.22l1.571-7.854l9.374,1.562C30.647,13.788,31,14.205,31,14.694V19C31,19.552,30.552,20,30,20z M1,19v-4.306 c0-0.489,0.353-0.906,0.836-0.986l9.374-1.562L12.78,20H2C1.448,20,1,19.552,1,19z M10.721,30l-0.456-1.368l3.977-1.326L14.78,30 H10.721z M21.279,30H17.22l0.539-2.693l3.977,1.326L21.279,30z M23.556,2.003C23.805,2.031,24,2.243,24,2.5 C24,2.776,23.776,3,23.5,3H18V2.858L23.556,2.003z M8,2.5c0-0.257,0.195-0.469,0.444-0.497L14,2.858V3H8.5C8.224,3,8,2.776,8,2.5z M15.82,30L12,10.901V7c0-1.654,1.346-3,3-3h2c1.654,0,3,1.346,3,3v3.901L16.18,30H15.82z"></path> </g></svg>
`
}

export const IconFastCamera = () => {
    return `
            <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>forward</title> <path d="M28.48 15.424l-12-10c-0.129-0.108-0.297-0.174-0.481-0.174-0.414 0-0.75 0.336-0.75 0.75 0 0 0 0.001 0 0.001v-0 8.398l-10.77-8.975c-0.129-0.108-0.297-0.174-0.481-0.174-0.414 0-0.75 0.336-0.75 0.75 0 0 0 0.001 0 0.001v-0 20c0 0 0 0 0 0.001 0 0.299 0.175 0.556 0.427 0.677l0.005 0.002c0.093 0.044 0.203 0.070 0.318 0.070h0c0 0 0.001 0 0.001 0 0.183 0 0.351-0.066 0.48-0.175l-0.001 0.001 10.77-8.975v8.398c0 0 0 0 0 0.001 0 0.299 0.175 0.556 0.427 0.677l0.005 0.002c0.093 0.044 0.203 0.070 0.318 0.070h0c0 0 0.001 0 0.001 0 0.183 0 0.351-0.066 0.48-0.175l-0.001 0.001 12-10c0.165-0.139 0.27-0.345 0.27-0.576s-0.104-0.438-0.268-0.575l-0.001-0.001zM4.75 24.398v-16.797l10.078 8.398zM16.75 24.398v-16.797l10.078 8.398z"></path> </g></svg>

`
}


export const IconSlowCamera = () => {
    return `
            <svg viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>backward</title> <path d="M28.318 5.32c-0.093-0.045-0.203-0.071-0.318-0.071-0.183 0-0.352 0.066-0.482 0.175l0.001-0.001-10.77 8.975v-8.398c0-0 0-0 0-0.001 0-0.414-0.336-0.75-0.75-0.75-0.183 0-0.352 0.066-0.482 0.175l0.001-0.001-12 10c-0.165 0.139-0.27 0.345-0.27 0.576s0.104 0.438 0.268 0.575l0.001 0.001 12 10c0.129 0.108 0.296 0.174 0.479 0.174 0 0 0.001 0 0.001 0h-0c0.116-0 0.225-0.026 0.323-0.072l-0.005 0.002c0.257-0.123 0.432-0.38 0.432-0.679 0-0 0-0 0-0.001v0-8.398l10.77 8.975c0.129 0.108 0.297 0.174 0.48 0.174 0 0 0 0 0 0h-0c0.116-0 0.225-0.026 0.323-0.072l-0.005 0.002c0.257-0.123 0.432-0.38 0.432-0.679 0-0 0-0 0-0.001v0-20c0 0 0-0 0-0 0-0.299-0.174-0.557-0.427-0.678l-0.005-0.002zM15.25 24.398l-10.078-8.398 10.078-8.398zM27.25 24.398l-10.078-8.398 10.078-8.398z"></path> </g></svg>

            `
}

export const IconPlayCamera = () => {
    return `
    <svg viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg">
    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
    <g id="SVGRepo_iconCarrier">
        <path d="M175 .024V1920l1570.845-959.927L175 .024Zm121.648 216.9 1215.875 743.149-1215.875 743.028V216.923Z" fill-rule="evenodd"></path>
    </g>
</svg>
    `
}

export const IconPauseCamera = () => {
    return `
    <svg version="1.1" id="Uploaded to svgrepo.com" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-1.6 -1.6 35.20 35.20" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> 
    <path class="blueprint_een" d="M12,29H3V3h9V29z M13,1H2C1.448,1,1,1.448,1,2v28c0,0.552,0.448,1,1,1h11c0.552,0,1-0.448,1-1 V2C14,1.448,13.552,1,13,1L13,1z M29,29h-9V3h9V29z M19,1c-0.552,0-1,0.448-1,1v28c0,0.552,0.448,1,1,1h11c0.552,0,1-0.448,1-1V2 c0-0.552-0.448-1-1-1H19z"></path> </g></svg>
    `
}

export const IconDotFlyCamera = () => {
    return `
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="-19.6 -19.6 529.30 529.30" xmlns:xlink="http://www.w3.org/1999/xlink" enable-background="new 0 0 490.1 490.1" stroke="#000000" stroke-width="0.004901000000000001">
    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round">
    </g>
    <g id="SVGRepo_iconCarrier">
        <g>
            <g>
                <path d="m447.25,360.1l-187.7-49.1 165.8-33.9-4.2-21.8-210.6,43.7c-13,2.8-9.4,20.9 0,21.8l168.9,44.8-191.8,13.1c-10.5,1.5-12.6,13.3-6.3,19.7l62.6,59-136.6,10.9 1,21.8 159.5-13.1c7.7,0.6 14.9-11.2 6.3-19.7l-61.5-59 233.5-16.4c11.5-0.5 12.8-18.6 1.1-21.8z"></path>
                <path d="m460.85,81.8c0-27.2-21.9-49.2-49-49.2s-49,22-49,49.2 21.9,49.2 49,49.2c26.1,0 49-22 49-49.2zm-77.1,1c0-15.7 12.5-28.3 28.1-28.3 14.6,0 28.1,11.5 28.1,27.2s-12.5,28.3-28.1,28.3c-15.6,0-28.1-11.5-28.1-27.2z"></path>
                <path d="m490.05,78.6c0-44-35.4-78.6-78.2-78.6-42.7,0-78.2,34.6-78.2,78.6 0,43.9 78.2,161.3 78.2,161.3s78.2-117.4 78.2-161.3zm-78.2-57.6c32.3,0 57.3,26.2 57.3,57.6 0,25.1-36.5,88-57.3,122.6-20.8-34.6-57.3-97.4-57.3-122.6 0-31.4 25-57.6 57.3-57.6z"></path>
                <path d="m78.25,269.9c-27.1,0-49,22-49,49.2s21.9,49.2 49,49.2c26.1,0 49-22 49-49.2s-21.9-49.2-49-49.2zm0,77.6c-15.6,0-28.1-11.5-28.1-27.2s12.5-28.3 28.1-28.3c14.6,0 28.1,11.5 28.1,27.2 0,15.7-12.5,28.3-28.1,28.3z"></path>
                <path d="m78.25,237.5c-42.7,0-78.2,34.6-78.2,78.6 0,43.9 78.2,161.3 78.2,161.3s78.2-117.4 78.2-161.3c0-44.1-35.5-78.6-78.2-78.6zm-57.3,78.5c-3.55271e-15-31.4 25-57.6 57.3-57.6s57.3,26.2 57.3,57.6c0,25.1-36.5,88-57.3,122.6-20.9-34.6-57.3-97.4-57.3-122.6z"></path>
            </g>
        </g>
    </g>
</svg>
    `

}

export const IconResize = () => {
    return `<svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" mirror-in-rtl="true" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
    <g id="SVGRepo_iconCarrier"> 
    <path d="M14.228 16.227a1 1 0 0 1-.707-1.707l1-1a1 1 0 0 1 1.416 1.414l-1 1a1 1 0 0 1-.707.293zm-5.638 0a1 1 0 0 1-.707-1.707l6.638-6.638a1 1 0 0 1 1.416 1.414l-6.638 6.638a1 1 0 0 1-.707.293zm-5.84 0a1 1 0 0 1-.707-1.707L14.52 2.043a1 1 0 1 1 1.415 1.414L3.457 15.934a1 1 0 0 1-.707.293z">
    </path> </g></svg>`
}


export const IconSearch = () => {
    return `
   <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M4 11C4 7.13401 7.13401 4 11 4C14.866 4 18 7.13401 18 11C18 14.866 14.866 18 11 18C7.13401 18 4 14.866 4 11ZM11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C13.125 20 15.078 19.2635 16.6177 18.0319L20.2929 21.7071C20.6834 22.0976 21.3166 22.0976 21.7071 21.7071C22.0976 21.3166 22.0976 20.6834 21.7071 20.2929L18.0319 16.6177C19.2635 15.078 20 13.125 20 11C20 6.02944 15.9706 2 11 2Z" ></path> </g></svg>
    `
}

export const IconDataElement = () => {
    return `
   <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="0"><g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke-width= "0"></g>
    <g id="SVGRepo_iconCarrier">
        <path d="M12 2.2C7.03 2.2 2 3.334 2 5.5v12.8c0 2.273 5.152 3.5 10 3.5s10-1.227 10-3.5V5.5c0-2.166-5.03-3.3-10-3.3zm0 18.6c-5.576 0-9-1.456-9-2.5v-6.282c1.708 1.173 5.366 1.782 9 1.782s7.292-.61 9-1.782V18.3c0 1.044-3.424 2.5-9 2.5zm0-8c-5.494 0-9-1.363-9-2.3V7.018C4.708 8.191 8.366 8.8 12 8.8s7.292-.61 9-1.782V10.5c0 .938-3.506 2.3-9 2.3zm0-5c-5.494 0-9-1.363-9-2.3s3.506-2.3 9-2.3 9 1.362 9 2.3-3.506 2.3-9 2.3z"></path><path fill="none" d="M0 0h24v24H0z"></path></g></svg>
    `
}


export const IconMap = () => {
    return `
<svg  viewBox="-1.6 -1.6 35.20 35.20" version="1.1" xmlns="http://www.w3.org/2000/svg"  stroke-width="0.00032">
<g id="SVGRepo_bgCarrier" stroke-width="0"></g>
<g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
<g id="SVGRepo_iconCarrier"> <path d="M31.037 4.219l-10.030-4.158-9.982 3.951-10.062-3.949c-0.086-0.025-0.17-0.035-0.249-0.035-0.41-0.002-0.714 0.304-0.714 0.765v25.938c0 0.549 0.433 1.121 0.963 1.268l10.073 3.974 9.969-4.047 10.032 3.942c0.086 0.023 0.17 0.035 0.249 0.035 0.41 0 0.714-0.305 0.714-0.765v-25.648c0-0.549-0.433-1.122-0.963-1.27zM12 5.661l8-3.135v23.797l-8 3.162v-23.825zM2 2.709l8 2.956v23.805l-8-3.259v-23.502zM30 29.313l-8-3.012v-23.744l8 3.45v23.307z">
</path> 
</g>
</svg>`
}


export const IconArrowDown = () => {
    return `
    <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" >
    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier">
    <path  d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></g></svg>
    `
}

export const IconPlus = () => {
    return `
 <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12.75 11.25V5C12.75 4.80109 12.671 4.61032 12.5303 4.46967C12.3897 4.32902 12.1989 4.25 12 4.25C11.8011 4.25 11.6103 4.32902 11.4697 4.46967C11.329 4.61032 11.25 4.80109 11.25 5V11.25H5C4.80109 11.25 4.61032 11.329 4.46967 11.4697C4.32902 11.6103 4.25 11.8011 4.25 12C4.25 12.1989 4.32902 12.3897 4.46967 12.5303C4.61032 12.671 4.80109 12.75 5 12.75H11.25V19C11.2526 19.1981 11.3324 19.3874 11.4725 19.5275C11.6126 19.6676 11.8019 19.7474 12 19.75C12.1989 19.75 12.3897 19.671 12.5303 19.5303C12.671 19.3897 12.75 19.1989 12.75 19V12.75H19C19.1989 12.75 19.3897 12.671 19.5303 12.5303C19.671 12.3897 19.75 12.1989 19.75 12C19.7474 11.8019 19.6676 11.6126 19.5275 11.4725C19.3874 11.3324 19.1981 11.2526 19 11.25H12.75Z" ></path> </g></svg>
`
}

export const IconMinus = () => {
    return `
  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M6 12L18 12"  stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
`
}

export const IconCreateRoot = () => {
    return ` 
        <svg viewBox="0 0 24 24" style="stroke: none;">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <rect x="0" fill="none" width="24" height="24"></rect> <g>
                    <path d="M21 14v5c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V5c0-1.105.895-2 2-2h5v2H5v14h14v-5h2z"></path>
                    <path d="M21 7h-4V3h-2v4h-4v2h4v4h2V9h4"></path> </g> </g></svg>
    `
}
export const IconCreateFolder = () => {
    return ` <svg viewBox="0 0 1024 1024" class="icon" xmlns="http://www.w3.org/2000/svg" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path  d="M128 192v640h768V320H485.76L357.504 192H128zm-32-64h287.872l128.384 128H928a32 32 0 0132 32v576a32 32 0 01-32 32H96a32 32 0 01-32-32V160a32 32 0 0132-32zm384 416V416h64v128h128v64H544v128h-64V608H352v-64h128z"></path></g></svg>   `
}
export const IconFolderLabel = () => {
    return `
  <svg viewBox="0 -1.5 35 35" version="1.1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>folder</title> <path d="M8.431 9.155h20.958c2.158 0 2.158-2.238 0.084-2.238h-14.486c-0.83 0-1.244-1.244-1.244-1.244s-0.581-1.825-1.743-1.825h-10.789c-1.576 0-1.162 1.825-1.162 1.825s2.407 20.47 2.573 21.715 1.453 1.612 1.453 1.612l2.821-18.103c0.208-1.327 1.12-1.7 1.535-1.742zM33.658 9.942h-24.563c-0.733 0-1.328 0.594-1.328 1.327l-2.572 16.4c0 0.734 0.595 1.328 1.328 1.328h24.563c0.732 0 1.328-0.594 1.328-1.328l2.572-16.4c0-0.733-0.593-1.327-1.328-1.327z"></path> </g></svg>
    `
}
export const IconCreateElement = () => {
    return `    
       <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
           <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
           <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
           <g id="SVGRepo_iconCarrier"> 
               <path d="M4 12H20M12 4V20" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
   `
}

export const IconCursorRemove = () => {
    return `
     
     <svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-14.96 -14.96 329.05 329.05" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="XMLID_311_"> <path id="XMLID_312_" d="M266.236,0h-60c-8.284,0-15,6.716-15,15s6.716,15,15,15h60c8.284,0,15-6.716,15-15S274.521,0,266.236,0z"></path> <path id="XMLID_333_" d="M153.077,175.689l31.813-17.27c4.652-2.525,7.629-7.315,7.833-12.604 c0.204-5.289-2.395-10.295-6.838-13.171L41.322,39.063c-4.609-2.984-10.481-3.21-15.308-0.591 c-4.826,2.62-7.835,7.667-7.844,13.158l-0.278,172.206c-0.008,5.293,2.773,10.199,7.32,12.909 c4.546,2.71,10.185,2.824,14.837,0.298l31.812-17.269l38.817,71.507c2.718,5.007,7.873,7.847,13.196,7.847 c2.417,0,4.869-0.586,7.143-1.82l54.85-29.773c3.496-1.898,6.096-5.107,7.226-8.921c1.13-3.814,0.699-7.921-1.199-11.418 L153.077,175.689z M129.887,263.787L91.07,192.28c-2.718-5.007-7.873-7.847-13.196-7.847c-2.417,0-4.87,0.586-7.143,1.82 l-22.797,12.376l0.193-119.424l100.254,64.898l-22.799,12.377c-7.281,3.952-9.979,13.058-6.027,20.339l38.816,71.506 L129.887,263.787z"></path> </g> </g></svg>
 `
}
export const IconEditElement = () => {
    return ` 
     <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title></title> <g id="Complete"> <g id="edit"> <g> <path d="M20,16v4a2,2,0,0,1-2,2H4a2,2,0,0,1-2-2V6A2,2,0,0,1,4,4H8" fill="none"  stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path> <polygon fill="none" points="12.5 15.8 22 6.2 17.8 2 8.3 11.5 8 16 12.5 15.8"  stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></polygon> </g> </g> </g> </g></svg>
 `
}

export const IconDownLoad = () => {
    return `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M18.22 20.75H5.78C5.43322 20.7359 5.09262 20.6535 4.77771 20.5075C4.4628 20.3616 4.17975 20.155 3.94476 19.8996C3.70977 19.6442 3.52745 19.3449 3.40824 19.019C3.28903 18.693 3.23525 18.3468 3.25 18V15C3.25 14.8011 3.32902 14.6103 3.46967 14.4697C3.61033 14.329 3.80109 14.25 4 14.25C4.19892 14.25 4.38968 14.329 4.53033 14.4697C4.67099 14.6103 4.75 14.8011 4.75 15V18C4.72419 18.2969 4.81365 18.5924 4.99984 18.8251C5.18602 19.0579 5.45465 19.21 5.75 19.25H18.22C18.5154 19.21 18.784 19.0579 18.9702 18.8251C19.1564 18.5924 19.2458 18.2969 19.22 18V15C19.22 14.8011 19.299 14.6103 19.4397 14.4697C19.5803 14.329 19.7711 14.25 19.97 14.25C20.1689 14.25 20.3597 14.329 20.5003 14.4697C20.641 14.6103 20.72 14.8011 20.72 15V18C20.75 18.6954 20.5041 19.3744 20.0359 19.8894C19.5677 20.4045 18.9151 20.7137 18.22 20.75Z" ></path> <path d="M12 15.75C11.9015 15.7504 11.8038 15.7312 11.7128 15.6934C11.6218 15.6557 11.5392 15.6001 11.47 15.53L7.47 11.53C7.33752 11.3878 7.2654 11.1997 7.26882 11.0054C7.27225 10.8111 7.35096 10.6258 7.48838 10.4883C7.62579 10.3509 7.81118 10.2722 8.00548 10.2688C8.19978 10.2654 8.38782 10.3375 8.53 10.47L12 13.94L15.47 10.47C15.6122 10.3375 15.8002 10.2654 15.9945 10.2688C16.1888 10.2722 16.3742 10.3509 16.5116 10.4883C16.649 10.6258 16.7277 10.8111 16.7312 11.0054C16.7346 11.1997 16.6625 11.3878 16.53 11.53L12.53 15.53C12.4608 15.6001 12.3782 15.6557 12.2872 15.6934C12.1962 15.7312 12.0985 15.7504 12 15.75Z" ></path> <path d="M12 15.75C11.8019 15.7474 11.6126 15.6676 11.4725 15.5275C11.3324 15.3874 11.2526 15.1981 11.25 15V4C11.25 3.80109 11.329 3.61032 11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5303 3.46967C12.671 3.61032 12.75 3.80109 12.75 4V15C12.7474 15.1981 12.6676 15.3874 12.5275 15.5275C12.3874 15.6676 12.1981 15.7474 12 15.75Z" ></path> </g></svg>`
}

export const IconSave = () => {
    return `
  <svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <g> <path d="M488.213,137.6l-112-133.76C374.187,1.387,371.2,0,368,0H54.4C36.16,0,21.333,14.72,21.333,32.96v446.08 C21.333,497.28,36.16,512,54.4,512h403.2c18.24,0,33.067-14.72,33.067-32.96V144.427 C490.667,141.973,489.813,139.52,488.213,137.6z M106.667,21.333h202.667v64H106.667V21.333z M469.333,479.04 c0,6.4-5.333,11.627-11.733,11.627H54.4c-6.4,0-11.733-5.227-11.733-11.627V32.96c0-6.4,5.333-11.627,11.733-11.627h30.933V96 c0,5.867,4.8,10.667,10.667,10.667h224c5.867,0,10.667-4.8,10.667-10.667V21.333h32.32l106.347,127.04V479.04z"></path> <path d="M256,178.453c-42.987,0-77.867,34.773-77.867,77.547c0,42.773,34.987,77.547,77.867,77.547s77.867-34.773,77.867-77.547 C333.867,213.227,298.88,178.453,256,178.453z M256,312.213c-31.04-0.107-56.107-25.493-56-56.533 c0.107-31.04,25.493-56.107,56.533-56c30.933,0.107,56,25.28,56,56.213C312.427,287.147,287.147,312.32,256,312.213z"></path> </g> </g> </g> </g></svg>
    `
}


export const IconUpload = () => {
    return `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M18.22 20.75H5.78C5.43322 20.7359 5.09262 20.6535 4.77771 20.5075C4.4628 20.3616 4.17975 20.155 3.94476 19.8996C3.70977 19.6442 3.52745 19.3449 3.40824 19.019C3.28903 18.693 3.23525 18.3468 3.25 18V15C3.25 14.8011 3.32902 14.6103 3.46967 14.4697C3.61033 14.329 3.80109 14.25 4 14.25C4.19892 14.25 4.38968 14.329 4.53033 14.4697C4.67099 14.6103 4.75 14.8011 4.75 15V18C4.72419 18.2969 4.81365 18.5924 4.99984 18.8251C5.18602 19.0579 5.45465 19.21 5.75 19.25H18.22C18.5154 19.21 18.784 19.0579 18.9702 18.8251C19.1564 18.5924 19.2458 18.2969 19.22 18V15C19.22 14.8011 19.299 14.6103 19.4397 14.4697C19.5803 14.329 19.7711 14.25 19.97 14.25C20.1689 14.25 20.3597 14.329 20.5003 14.4697C20.641 14.6103 20.72 14.8011 20.72 15V18C20.75 18.6954 20.5041 19.3744 20.0359 19.8894C19.5677 20.4045 18.9151 20.7137 18.22 20.75Z"></path> <path d="M16 8.74995C15.9015 8.75042 15.8038 8.7312 15.7128 8.69342C15.6218 8.65564 15.5392 8.60006 15.47 8.52995L12 5.05995L8.53 8.52995C8.38782 8.66243 8.19978 8.73455 8.00548 8.73113C7.81118 8.7277 7.62579 8.64898 7.48838 8.51157C7.35096 8.37416 7.27225 8.18877 7.26882 7.99447C7.2654 7.80017 7.33752 7.61213 7.47 7.46995L11.47 3.46995C11.6106 3.3295 11.8012 3.25061 12 3.25061C12.1987 3.25061 12.3894 3.3295 12.53 3.46995L16.53 7.46995C16.6705 7.61058 16.7493 7.8012 16.7493 7.99995C16.7493 8.1987 16.6705 8.38932 16.53 8.52995C16.4608 8.60006 16.3782 8.65564 16.2872 8.69342C16.1962 8.7312 16.0985 8.75042 16 8.74995Z"></path> <path d="M12 15.75C11.8019 15.7474 11.6126 15.6676 11.4725 15.5275C11.3324 15.3874 11.2526 15.1981 11.25 15V4C11.25 3.80109 11.329 3.61032 11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5303 3.46967C12.671 3.61032 12.75 3.80109 12.75 4V15C12.7474 15.1981 12.6676 15.3874 12.5275 15.5275C12.3874 15.6676 12.1981 15.7474 12 15.75Z"></path> </g></svg>`
}

export const IconLabelProperties = () => {
    return `
     <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 486.82 486.82" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g>
 <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
 <g id="SVGRepo_iconCarrier"> 
 <g> 
 <path d="M486.82,21.213L465.607,0l-42.768,42.768H238.991L0,281.759L205.061,486.82l238.992-238.991V63.98L486.82,21.213z M414.053,235.403L205.061,444.394L42.427,281.759L251.418,72.768h141.421l-40.097,40.097c-14.56-6.167-32.029-3.326-43.898,8.543 c-15.621,15.621-15.621,40.948,0,56.569c15.621,15.621,40.948,15.621,56.568,0c11.869-11.869,14.71-29.338,8.543-43.898 l40.097-40.097V235.403z">

</path> 
</g> 
</g>
</svg>
    `

}

export const IconDialogError = () => {
    return `<svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 511.76 511.76" xml:space="preserve" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M436.896,74.869c-99.84-99.819-262.208-99.819-362.048,0c-99.797,99.819-99.797,262.229,0,362.048 c49.92,49.899,115.477,74.837,181.035,74.837s131.093-24.939,181.013-74.837C536.715,337.099,536.715,174.688,436.896,74.869z M361.461,331.317c8.341,8.341,8.341,21.824,0,30.165c-4.16,4.16-9.621,6.251-15.083,6.251c-5.461,0-10.923-2.091-15.083-6.251 l-75.413-75.435l-75.392,75.413c-4.181,4.16-9.643,6.251-15.083,6.251c-5.461,0-10.923-2.091-15.083-6.251 c-8.341-8.341-8.341-21.845,0-30.165l75.392-75.413l-75.413-75.413c-8.341-8.341-8.341-21.845,0-30.165 c8.32-8.341,21.824-8.341,30.165,0l75.413,75.413l75.413-75.413c8.341-8.341,21.824-8.341,30.165,0 c8.341,8.32,8.341,21.824,0,30.165l-75.413,75.413L361.461,331.317z"></path> </g> </g> </g></svg>`
}

export const IconElevationProfile = () => {
    return `
   <svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-25.03 -25.03 550.62 550.62" xml:space="preserve" stroke-width="0.0050056200000000006"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M498.281,430.548L330.814,95.614c-4.267-7.467-11.733-11.733-19.2-11.733c-8.533,0-16,4.267-19.2,11.733l-35.2,70.4 l-50.133-112c-3.2-7.467-10.667-12.8-19.2-12.8s-16,5.333-19.2,12.8L2.281,429.481c-3.2,6.4-3.2,13.867,1.067,20.267 c3.2,6.4,9.6,9.6,17.067,9.6h458.667c7.467,0,13.867-2.133,18.133-8.533C501.481,444.414,501.481,436.948,498.281,430.548z M52.414,417.748l135.467-304l135.467,304H52.414z M368.147,416.681l-89.6-201.6l33.067-65.067l133.333,266.667H368.147z"></path> </g> </g> </g></svg>
    `
}

export const IconNote = () => {
    return `
<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" version="1.1" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="1.2"></g><g id="SVGRepo_iconCarrier"> <path style="fill:none;stroke-width:3;" d="m 8,5 c -8,11 23,1.4 25,4 2,3 -20,9 -20,16 0,7 50,-11 49,-4 -1,6 -52.3,19 -50,25 2,5 21,2 22,6 1,4 -27,14 -28,29 -1,15 12,19 21,9"></path> <path style="fill:none;stroke-width:3;" d="m 8,5 c -8,11 23,1.4 25,4 2,3 -20,9 -20,16 0,7 50,-11 49,-4 -1,6 -52.3,19 -50,25 2,5 21,2 22,6 1,4 -27,14 -28,29 -1,15 12,19 21,9"></path> <path style="stroke-width:3;" d="M 25,78 85,15 c 0,0 5,1 8,4 3,3 4,8 4,8 L 38,90 22,94 z"></path> <path d="M 85,19 28,79 30,81 87,21 z"></path> <path  d="m 22,94 7,-2 -5,-5 z"></path> </g></svg>
    `
}

export const IconMarkupArrow = () => {
    return `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 4C11.4477 4 11 3.55228 11 3C11 2.44772 11.4477 2 12 2L20 2C21.1046 2 22 2.89543 22 4V12C22 12.5523 21.5523 13 21 13C20.4477 13 20 12.5523 20 12V5.39343L3.72798 21.6655C3.33746 22.056 2.70429 22.056 2.31377 21.6655C1.92324 21.2749 1.92324 20.6418 2.31377 20.2512L18.565 4L12 4Z" ></path> </g></svg>
    `
}

export const IconMarkupRectangle = () => {
    return `
<svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-25.6 -25.6 563.20 563.20" xml:space="preserve"  stroke-width="0.00512"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M503.467,72.533h-51.2c-4.71,0-8.533,3.823-8.533,8.533v17.067h-409.6c-4.71,0-8.533,3.823-8.533,8.533V371.2H8.533 c-4.71,0-8.533,3.823-8.533,8.533v51.2c0,4.71,3.823,8.533,8.533,8.533h51.2c4.71,0,8.533-3.823,8.533-8.533v-17.067h409.6 c4.71,0,8.533-3.823,8.533-8.533V396.8v-256h17.067c4.71,0,8.533-3.823,8.533-8.533v-51.2 C512,76.356,508.177,72.533,503.467,72.533z M469.333,396.8H68.267v-17.067c0-4.71-3.823-8.533-8.533-8.533H42.667v-256h401.067 v17.067c0,4.71,3.823,8.533,8.533,8.533h17.067V396.8z"></path> </g> </g> </g></svg>
    `
}

export const IconMarkupText = () => {
    return `
<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9.75 18H8.25V12H6V10.5H12V12H9.75V18Z" fill="#1F2328"></path> <path d="M15.75 6.5V18H14.25V6.5H10V5L20 5V6.5H15.75Z" fill="#1F2328"></path> </g></svg>
    `
}

export const IconExtension = () => {
    return `
<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-labelledby="extensionIconTitle"  stroke-width="1" stroke-linecap="square" stroke-linejoin="miter" color="transparent"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title id="extensionIconTitle">Extension</title> <path d="M9 4C9 2.89543 9.89543 2 11 2C12.1046 2 13 2.89543 13 4V6H18V11H20C21.1046 11 22 11.8954 22 13C22 14.1046 21.1046 15 20 15H18V20H13V18C13 16.8954 12.1046 16 11 16C9.89543 16 9 16.8954 9 18V20H4V15H6C7.10457 15 8 14.1046 8 13C8 11.8954 7.10457 11 6 11H4V6H9V4Z"></path> </g></svg>
    `
}

export const IconIssues = () => {
    return `
        <svg  viewBox="0 0 1024.00 1024.00" xmlns="http://www.w3.org/2000/svg" class="icon" transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="14.336000000000002"></g><g id="SVGRepo_iconCarrier"> <path d="M464 688a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72-112c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48zm400-188h-59.3c-2.6 0-5 1.2-6.5 3.3L763.7 538.1l-49.9-68.8a7.92 7.92 0 0 0-6.5-3.3H648c-6.5 0-10.3 7.4-6.5 12.7l109.2 150.7a16.1 16.1 0 0 0 26 0l165.8-228.7c3.8-5.3 0-12.7-6.5-12.7zm-44 306h-64.2c-5.5 0-10.6 2.9-13.6 7.5a352.2 352.2 0 0 1-49.8 62.2A355.92 355.92 0 0 1 651.1 840a355 355 0 0 1-138.7 27.9c-48.1 0-94.8-9.4-138.7-27.9a355.92 355.92 0 0 1-113.3-76.3A353.06 353.06 0 0 1 184 650.5c-18.6-43.8-28-90.5-28-138.5s9.4-94.7 28-138.5c17.9-42.4 43.6-80.5 76.4-113.2 32.8-32.7 70.9-58.4 113.3-76.3a355 355 0 0 1 138.7-27.9c48.1 0 94.8 9.4 138.7 27.9 42.4 17.9 80.5 43.6 113.3 76.3 19 19 35.6 39.8 49.8 62.2 2.9 4.7 8.1 7.5 13.6 7.5H892c6 0 9.8-6.3 7.2-11.6C828.8 178.5 684.7 82 517.7 80 278.9 77.2 80.5 272.5 80 511.2 79.5 750.1 273.3 944 512.4 944c169.2 0 315.6-97 386.7-238.4A8 8 0 0 0 892 694z"></path> </g></svg>
    `
}



export const IconCursorClick = () => {
    return `
     <svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M 9 2.59375 L 9 28.15625 L 10.65625 26.78125 L 14.6875 23.40625 L 16.71875 27.4375 L 17.15625 28.34375 L 18.0625 27.875 L 21.15625 26.28125 L 22.03125 25.84375 L 21.59375 24.9375 L 19.75 21.3125 L 24.8125 20.6875 L 26.84375 20.4375 L 25.40625 19 L 10.71875 4.28125 Z M 11 7.4375 L 22.5625 18.96875 L 18.0625 19.5 L 16.65625 19.6875 L 17.3125 20.96875 L 19.375 24.96875 L 18.0625 25.65625 L 15.90625 21.34375 L 15.3125 20.21875 L 14.34375 21.03125 L 11 23.84375 Z"></path></g></svg>
    `
}

export const IconDialogWarning = () => {
    return `
    <svg viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--twemoji" preserveAspectRatio="xMidYMid meet" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path fill="#FFCC4D" d="M2.653 35C.811 35-.001 33.662.847 32.027L16.456 1.972c.849-1.635 2.238-1.635 3.087 0l15.609 30.056c.85 1.634.037 2.972-1.805 2.972H2.653z"></path><path fill="#ffffff" d="M15.583 28.953a2.421 2.421 0 0 1 2.419-2.418a2.421 2.421 0 0 1 2.418 2.418a2.422 2.422 0 0 1-2.418 2.419a2.422 2.422 0 0 1-2.419-2.419zm.186-18.293c0-1.302.961-2.108 2.232-2.108c1.241 0 2.233.837 2.233 2.108v11.938c0 1.271-.992 2.108-2.233 2.108c-1.271 0-2.232-.807-2.232-2.108V10.66z"></path></g></svg>
    `

}


export const IconDialogInfo = () => {
    return `
  <svg viewBox="0 0 1024 1024" class="icon" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M512 512m-448 0a448 448 0 1 0 896 0 448 448 0 1 0-896 0Z" fill="#2196F3"></path><path d="M469.333333 469.333333h85.333334v234.666667h-85.333334z" fill="#FFFFFF"></path><path d="M512 352m-53.333333 0a53.333333 53.333333 0 1 0 106.666666 0 53.333333 53.333333 0 1 0-106.666666 0Z" fill="#FFFFFF"></path></g></svg>
    `

}

export const IconDialogSuccess = () => {
    return `
  <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50" xml:space="preserve" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <circle style="fill:#25AE88;" cx="25" cy="25" r="25"></circle> <polyline style="fill:none;stroke:#FFFFFF;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points=" 38,15 22,33 12,25 "></polyline> </g></svg>
    `

}

export const IconDialogErrorRed = () => {
    return `
  <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 50 50" xml:space="preserve" fill="#000000"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <circle style="fill:#D75A4A;" cx="25" cy="25" r="25"></circle> <polyline style="fill:none;stroke:#FFFFFF;stroke-width:2;stroke-linecap:round;stroke-miterlimit:10;" points="16,34 25,25 34,16 "></polyline> <polyline style="fill:none;stroke:#FFFFFF;stroke-width:2;stroke-linecap:round;stroke-miterlimit:10;" points="16,16 25,25 34,34 "></polyline> </g></svg>
    `

}


export const IconArrowTriangle = () => {
    return `
  <svg  version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 123.959 123.959" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M85.742,1.779l-56,56c-2.3,2.3-2.3,6.1,0,8.401l56,56c3.801,3.8,10.2,1.1,10.2-4.2v-112 C95.942,0.679,89.543-2.021,85.742,1.779z"></path> </g> </g></svg>
    `

}

export const IconMoveModel = () => {
    return `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--gis" preserveAspectRatio="xMidYMid meet" ><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M50 0L35.355 14.645H45V30h10V14.645h9.645L50 0zM14.645 35.355L0 50l14.645 14.645V55H30V45H14.645v-9.645zm70.71 0V45H70v10h15.355v9.645L100 50L85.355 35.355zM50 37.45c-6.89 0-12.55 5.661-12.55 12.551c0 6.89 5.66 12.55 12.55 12.55c6.656 0 12.114-5.296 12.48-11.866a3.5 3.5 0 0 0 .07-.684a3.5 3.5 0 0 0-.071-.7C62.104 42.739 56.65 37.45 50 37.45zm0 7c3.107 0 5.55 2.444 5.55 5.551s-2.443 5.55-5.55 5.55c-3.107 0-5.55-2.443-5.55-5.55c0-3.107 2.443-5.55 5.55-5.55zM45 70v15.355h-9.645L50 100l14.645-14.645H55V70H45z" ></path></g></svg>`
}

export const IconRefresh = () => {
    return `
    <svg  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 383.748 383.748" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M62.772,95.042C90.904,54.899,137.496,30,187.343,30c83.743,0,151.874,68.13,151.874,151.874h30 C369.217,81.588,287.629,0,187.343,0c-35.038,0-69.061,9.989-98.391,28.888C70.368,40.862,54.245,56.032,41.221,73.593 L2.081,34.641v113.365h113.91L62.772,95.042z"></path> <path d="M381.667,235.742h-113.91l53.219,52.965c-28.132,40.142-74.724,65.042-124.571,65.042 c-83.744,0-151.874-68.13-151.874-151.874h-30c0,100.286,81.588,181.874,181.874,181.874c35.038,0,69.062-9.989,98.391-28.888 c18.584-11.975,34.707-27.145,47.731-44.706l39.139,38.952V235.742z"></path> </g> </g></svg>`
}

export const IconFilter = () => {
    return `
    <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" stroke-width="3" fill="none"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><line x1="50.69" y1="32" x2="56.32" y2="32"></line><line x1="7.68" y1="32" x2="38.69" y2="32"></line><line x1="26.54" y1="15.97" x2="56.32" y2="15.97"></line><line x1="7.68" y1="15.97" x2="14.56" y2="15.97"></line><line x1="35" y1="48.03" x2="56.32" y2="48.03"></line><line x1="7.68" y1="48.03" x2="23" y2="48.03"></line><circle cx="20.55" cy="15.66" r="6"></circle><circle cx="44.69" cy="32" r="6"></circle><circle cx="29" cy="48.03" r="6"></circle></g></svg>`
}

export const IconMouse = () => {
    return `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24.00 24.00" fill="none"  stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M4 4l7.07 17 2.51-7.39L21 11.07z"></path> </g></svg>
    `
}

// export const IconMouseClick = () => {
//     return `
//    <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 612 792" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M611.608,334.751c-0.826-4.408-3.857-8.265-8.265-9.918L18.996,90.929C13.761,89,7.975,90.102,4.118,93.959 c-3.857,3.857-4.959,9.643-3.031,14.877l233.904,584.624c1.653,4.132,5.51,7.439,9.918,8.265c0.827,0.275,1.929,0.275,2.755,0.275 c3.582,0,6.888-1.378,9.367-3.858L383.765,571.41l106.896,106.896c5.51,5.51,14.051,5.51,19.561,0l77.968-77.968 c2.755-2.48,4.133-6.061,4.133-9.643s-1.653-7.163-4.133-9.643L481.018,473.881L607.75,347.149 C611.057,344.118,612.434,339.159,611.608,334.751z M452.09,464.24c-2.755,2.48-4.133,6.061-4.133,9.643s1.653,7.163,4.133,9.643 l107.172,107.447l-58.407,58.407L393.683,542.208c-5.51-5.51-14.051-5.51-19.561,0L252.624,663.706L38.557,128.399l535.031,214.343 L452.09,464.24z M103.025,189.01c3.582-1.378,7.439,0.276,9.092,3.857l153.181,382.677c1.378,3.582-0.275,7.439-3.857,9.092 c-1.102,0.275-1.929,0.55-2.755,0.55c-2.755,0-5.235-1.653-6.337-4.408L99.168,198.102C97.79,194.52,99.443,190.663,103.025,189.01z "></path> </g></svg>`
// }
export const IconMouseClick = () => {
    return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mouse-pointer-click-icon lucide-mouse-pointer-click"><path d="M14 4.1 12 6"/><path d="m5.1 8-2.9-.8"/><path d="m6 12-1.9 2"/><path d="M7.2 2.2 8 5.1"/><path d="M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z"/></svg>`
}


export const IconCube = () => {
    return `
    <svg  version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 270.06 270.06" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path color-rendering="auto" image-rendering="auto" shape-rendering="auto" color-interpolation="sRGB" d="M264.898,0.007 c-0.181,0.006-0.362,0.023-0.541,0.049H84.996c-1.326,0-2.598,0.527-3.535,1.465l-80,80C0.525,82.459-0.001,83.73,0,85.056v180 c0,2.761,2.239,5,5,5h180c1.326,0,2.598-0.527,3.535-1.465l80-80c0.938-0.938,1.465-2.209,1.465-3.535V5.819 c0.14-0.893,0.035-1.807-0.303-2.645c-0.016-0.045-0.033-0.09-0.051-0.135c-0.808-1.89-2.69-3.093-4.744-3.033L264.898,0.007z M87.066,10.056h165.859l-70,70H17.066L87.066,10.056z M259.996,17.126v165.859l-70,70V87.126L259.996,17.126L259.996,17.126z M84.92,19.985c-2.759,0.042-4.963,2.311-4.924,5.07v40c-0.039,2.761,2.168,5.032,4.929,5.071s5.032-2.168,5.071-4.929 c0.001-0.047,0.001-0.094,0-0.141v-40c0.039-2.761-2.168-5.031-4.93-5.07C85.018,19.984,84.969,19.984,84.92,19.985z M9.996,90.056 h170v170h-170V90.056L9.996,90.056z M84.92,99.985c-2.759,0.042-4.963,2.311-4.924,5.07v30c-0.039,2.761,2.168,5.032,4.929,5.071 s5.032-2.168,5.071-4.929c0.001-0.047,0.001-0.094,0-0.141v-30c0.039-2.761-2.168-5.031-4.93-5.07 C85.018,99.984,84.969,99.984,84.92,99.985z M84.92,159.985c-2.759,0.042-4.963,2.311-4.924,5.07v17.93L61.461,201.52 c-1.992,1.913-2.057,5.078-0.144,7.07c1.913,1.992,5.078,2.057,7.07,0.144c0.049-0.047,0.097-0.095,0.144-0.144l18.535-18.535h17.93 c2.761,0.039,5.032-2.168,5.071-4.929s-2.168-5.032-4.929-5.071c-0.047-0.001-0.094-0.001-0.141,0h-15v-15 c0.039-2.761-2.168-5.031-4.93-5.07C85.018,159.984,84.969,159.984,84.92,159.985z M134.996,180.055 c-2.761-0.039-5.032,2.168-5.071,4.929c-0.039,2.761,2.168,5.032,4.929,5.071c0.047,0.001,0.094,0.001,0.141,0h30 c2.761,0.039,5.032-2.168,5.071-4.929c0.039-2.761-2.168-5.032-4.929-5.071c-0.047-0.001-0.094-0.001-0.141,0H134.996z M204.996,180.055c-2.761-0.039-5.032,2.168-5.071,4.929c-0.039,2.761,2.168,5.032,4.929,5.071c0.047,0.001,0.094,0.001,0.141,0h40 c2.761,0.039,5.032-2.168,5.071-4.929s-2.168-5.032-4.929-5.071c-0.047-0.001-0.094-0.001-0.141,0H204.996z M44.898,220.007 c-1.299,0.039-2.532,0.582-3.438,1.514l-20,20c-1.992,1.913-2.057,5.078-0.144,7.07c1.913,1.992,5.078,2.057,7.07,0.144 c0.049-0.047,0.097-0.095,0.144-0.144l20-20c1.98-1.925,2.025-5.091,0.1-7.071C47.654,220.514,46.3,219.965,44.898,220.007z"></path> </g></svg>`
}

export const IconMousePan = () => {
    return `
    <svg version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <style type="text/css"></style> <g> <path class="st0" d="M461.74,282.084c-15.492-23.242-36.635-35.027-62.846-35.027c-4.532,0-9.321,0.352-14.241,1.057 c-9.251,1.321-31.562,3.543-63.379,6.315l-38.365-33.239h32.69v20.379l2.657,2.362c1.493-0.123,2.92-0.254,4.38-0.377l53.21-39.284 l-60.246-43.212v26.309H243.87l-3.637-3.149c-10.465-9.275-23.123-15.222-35.408-16.763V76.611h26.326l-43.229-60.262 l-43.229,60.262h26.318v99.956c-1.948,1.559-3.809,3.24-5.502,5.151c-1.583,1.788-2.953,3.69-4.224,5.65H60.246v-26.309L0,204.271 l60.246,43.229V221.19h93.231c1.3,13.606,7.221,27.515,17.534,39.235v71.522h-26.318l43.229,77.042l43.229-77.042h-26.326v-41.408 l44.299,38.333c-13.917,33.641-11.49,48.198-10.608,53.471c4.092,24.456,34.876,56.597,49.437,70.621 c8.21,10.342,18.826,18.527,30.919,23.841c7.98,4.732,34.732,18.846,69.952,18.846c15.254,0,30.168-2.69,44.327-8.004 c53.579-20.084,80.196-65.823,78.797-100.612C511.107,366.13,494.217,330.824,461.74,282.084z M424.306,464.061 c-12.4,4.65-24.419,6.397-35.482,6.397c-33.588,0-58.389-16.115-58.389-16.115c-9.415-3.83-17.607-10.145-23.701-18.272 c0,0-40.071-38.168-43.368-57.876c-2.509-15.032,10.268-43.408,16.476-56.055l-87.08-75.353 c-15.976-15.54-18.625-36.807-8.415-48.337c3.916-4.412,8.919-6.233,14.25-6.233c8.587,0,18.026,4.74,25.141,11.047l89.09,77.182 c25.924-2.215,61.476-5.405,75.385-7.39c3.728-0.533,7.283-0.804,10.682-0.804c17.554,0,30.874,7.29,41.88,23.808 c13.138,19.716,45.119,70.03,46,91.985C487.656,409.998,469.097,447.264,424.306,464.061z"></path> </g> </g></svg>
    `
}

export const IconDevTools = () => {
    return `
    <svg  viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M 2 7 L 2 25 L 30 25 L 30 7 L 2 7 z M 4 9 L 28 9 L 28 23 L 4 23 L 4 9 z M 6 11 L 6 21 L 9 21 C 10.654 21 12 19.654 12 18 L 12 14 C 12 12.346 10.654 11 9 11 L 6 11 z M 16 11 C 14.897 11 14 11.897 14 13 L 14 19 C 14 20.103 14.897 21 16 21 L 18 21 L 18 19 L 16 19 L 16 17 L 18 17 L 18 15 L 16 15 L 16 13 L 18 13 L 18 11 L 16 11 z M 19.691406 11 L 21.775391 20.025391 C 21.907391 20.595391 22.415 21 23 21 C 23.585 21 24.092609 20.595391 24.224609 20.025391 L 26.308594 11 L 24.255859 11 L 23 16.439453 L 21.744141 11 L 19.691406 11 z M 8 13 L 9 13 C 9.552 13 10 13.448 10 14 L 10 18 C 10 18.552 9.552 19 9 19 L 8 19 L 8 13 z"></path></g></svg>
    `
}


export const IconAngleRule = () => {
    return `
   <svg viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M8.8914 2.1937C9.1158 2.35464 9.16725 2.66701 9.00631 2.89141L2.47388 12H13.5C13.7761 12 14 12.2239 14 12.5C14 12.7762 13.7761 13 13.5 13H1.5C1.31254 13 1.14082 12.8952 1.0552 12.7284C0.969578 12.5616 0.984438 12.361 1.09369 12.2086L8.19369 2.30862C8.35462 2.08422 8.667 2.03277 8.8914 2.1937ZM11.1 6.50001C11.1 6.22387 11.3238 6.00001 11.6 6.00001C11.8761 6.00001 12.1 6.22387 12.1 6.50001C12.1 6.77615 11.8761 7.00001 11.6 7.00001C11.3238 7.00001 11.1 6.77615 11.1 6.50001ZM10.4 4.00001C10.1239 4.00001 9.90003 4.22387 9.90003 4.50001C9.90003 4.77615 10.1239 5.00001 10.4 5.00001C10.6762 5.00001 10.9 4.77615 10.9 4.50001C10.9 4.22387 10.6762 4.00001 10.4 4.00001ZM12.1 8.50001C12.1 8.22387 12.3238 8.00001 12.6 8.00001C12.8761 8.00001 13.1 8.22387 13.1 8.50001C13.1 8.77615 12.8761 9.00001 12.6 9.00001C12.3238 9.00001 12.1 8.77615 12.1 8.50001ZM13.4 10C13.1239 10 12.9 10.2239 12.9 10.5C12.9 10.7761 13.1239 11 13.4 11C13.6762 11 13.9 10.7761 13.9 10.5C13.9 10.2239 13.6762 10 13.4 10Z" ></path> </g></svg>
    `
}

export const IconSetting = () => {
    return `
    <svg viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M13.5 0c-.822 0-1.5.678-1.5 1.5v1.965c-1.068.275-2.088.695-3.04 1.25l-1.28-1.28c-.582-.58-1.54-.58-2.123 0l-2.12 2.122c-.583.582-.583 1.54 0 2.123l1.282 1.283c-.56.95-.984 1.97-1.263 3.037H1.5c-.822 0-1.5.678-1.5 1.5v3c0 .822.678 1.5 1.5 1.5h1.965c.275 1.068.695 2.088 1.25 3.04l-1.28 1.28c-.58.582-.58 1.54 0 2.123l2.122 2.12c.582.583 1.54.583 2.123 0l1.283-1.282c.95.56 1.97.984 3.037 1.263V28.5c0 .822.678 1.5 1.5 1.5h3c.822 0 1.5-.678 1.5-1.5v-1.965c1.068-.275 2.088-.695 3.04-1.25l1.28 1.28c.582.58 1.54.58 2.123 0l2.12-2.122c.583-.582.583-1.54 0-2.123l-1.282-1.283c.56-.95.984-1.97 1.263-3.037H28.5c.822 0 1.5-.678 1.5-1.5v-3c0-.822-.678-1.5-1.5-1.5h-1.965c-.275-1.068-.695-2.088-1.25-3.04l1.28-1.28c.58-.582.58-1.54 0-2.123l-2.122-2.12c-.582-.583-1.54-.583-2.123 0l-1.283 1.282c-.95-.56-1.97-.984-3.037-1.263V1.5c0-.822-.678-1.5-1.5-1.5h-3zm0 1h3c.286 0 .5.214.5.5v2.283a.5.5 0 0 0 .39.488c1.243.28 2.428.77 3.503 1.455a.5.5 0 0 0 .62-.07l1.514-1.512c.202-.202.508-.202.71 0l2.12 2.12c.202.202.202.508 0 .71L24.35 8.48a.5.5 0 0 0-.07.622c.68 1.076 1.167 2.262 1.44 3.505a.5.5 0 0 0 .49.393h2.29c.286 0 .5.214.5.5v3c0 .286-.214.5-.5.5h-2.283a.5.5 0 0 0-.488.39c-.28 1.243-.77 2.428-1.455 3.503a.5.5 0 0 0 .07.62l1.512 1.514c.202.202.202.508 0 .71l-2.12 2.12c-.202.202-.508.202-.71 0L21.52 24.35a.5.5 0 0 0-.622-.07c-1.076.68-2.262 1.167-3.505 1.44a.5.5 0 0 0-.393.49v2.29c0 .286-.214.5-.5.5h-3c-.286 0-.5-.214-.5-.5v-2.283a.5.5 0 0 0-.39-.488c-1.243-.28-2.428-.77-3.503-1.455a.5.5 0 0 0-.62.07l-1.514 1.512c-.202.202-.508.202-.71 0l-2.12-2.12c-.202-.202-.202-.508 0-.71L5.65 21.52a.5.5 0 0 0 .07-.622c-.68-1.076-1.167-2.262-1.44-3.505A.5.5 0 0 0 3.79 17H1.5c-.286 0-.5-.214-.5-.5v-3c0-.286.214-.5.5-.5h2.283a.5.5 0 0 0 .488-.39c.28-1.243.77-2.428 1.455-3.503a.5.5 0 0 0-.07-.62L4.144 6.972c-.202-.202-.202-.508 0-.71l2.12-2.12c.202-.202.508-.202.71 0L8.48 5.65a.5.5 0 0 0 .622.07c1.076-.68 2.262-1.167 3.505-1.44A.5.5 0 0 0 13 3.79V1.5c0-.286.214-.5.5-.5zm1.5 9c-2.756 0-5 2.244-5 5s2.244 5 5 5 5-2.244 5-5-2.244-5-5-5zm0 1c2.215 0 4 1.785 4 4s-1.785 4-4 4-4-1.785-4-4 1.785-4 4-4z"></path></g></svg>
    `
}

export const IconRotate = () => {
    return `
    <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 486.805 486.805" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M261.397,17.983c-88.909,0-167.372,51.302-203.909,129.073L32.072,94.282L0,109.73l52.783,109.565l109.565-52.786 l-15.451-32.066L89.82,161.934c30.833-65.308,96.818-108.353,171.577-108.353c104.668,0,189.818,85.154,189.818,189.821 s-85.15,189.824-189.818,189.824c-61.631,0-119.663-30.109-155.228-80.539l-29.096,20.521 c42.241,59.87,111.143,95.613,184.324,95.613c124.286,0,225.407-101.122,225.407-225.419S385.684,17.983,261.397,17.983z"></path> </g> </g></svg>
    `
}