import { Annotation, AnnotationsPlugin, <PERSON><PERSON><PERSON>, PickResult } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../globalStore/globalStore";
import mathTs from "../../../libs/mathTs";
import { OverlappingPicker } from "../../../libs/overlappingPick";
import { createUUID } from "../../../utils/utils";

import { Viewer as ViewerCustom } from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";

export class DevCoordinates {
    private _store = GlobalStore.getInstance().getAll();
    private picker = new OverlappingPicker();
    private _mouseDownPos: { x: number; y: number } | null = null;
    private _isDragging = false;
    private _canvas: HTMLCanvasElement | null = null;
    private _listAnnotations: Map<string, Annotation> = new Map<string, Annotation>();
    private _isRemovingAnnotation = false;
    private _hightlighedMarketSelected: Annotation | undefined;
    private _isActiveCoordinates: boolean = false;
    private _markerDiv: HTMLDivElement = document.createElement("div");
    private _annotations = new AnnotationsPlugin(this._store.viewer!, {
        container: this._store.containerCanvas as HTMLElement,
        labelHTML: "<div class='dpu-annotation-label' style='background-color: {{labelBGColor}};'><div class='annotation-title'>{{title}}</div><div class='annotation-desc'>{{description}}</div></div>",

        values: {
            markerBGColor: "red",
            description: "No description"
        },
        surfaceOffset: 0.01
    });

    constructor() {
        this._canvas = this._store.canvas ?? null;
        if (!this._canvas) {
            console.error("Canvas not found in store.");
            return;
        }
        this.initialize();
        GlobalStore.getInstance().set("devGeneral", {
            coordinates: this
        });



    }

    get isActiveCoordinates(): boolean {
        return this._isActiveCoordinates;
    }
    set isActiveCoordinates(value: boolean) {
        this._isActiveCoordinates = value;
    }

    initialize = () => {
        this._annotations.on("markerMouseEnter", (anotation: Annotation) => {
            //handle marker mouse enter
            if (this._isRemovingAnnotation) {
                this._hightlighedMarketSelected = anotation;
                const marker = (anotation as any)._label as HTMLElement;
                const label = (anotation as any)._marker as HTMLElement;
                marker.classList.add("active");
                label.classList.add("active");
            }
        })

        this._annotations.on("markerMouseLeave", (anotation: Annotation) => {
            //handle marker mouse leave
            if (this._isRemovingAnnotation) {
                const marker = (anotation as any)._label as HTMLElement;
                const label = (anotation as any)._marker as HTMLElement;
                marker.classList.remove("active");
                label.classList.remove("active");
                this._hightlighedMarketSelected = undefined;
            }
        });
        this._markerDiv.id = "dpu-annotation-marker-point";
        this._markerDiv.style.background = "black";
        this._markerDiv.style.border = "2px solid blue";
        this._markerDiv.style.borderRadius = "20px";
        this._markerDiv.style.width = "10px";
        this._markerDiv.style.height = "10px";

        this._markerDiv.style.zIndex = "1000";
        this._markerDiv.style.position = "absolute";
        this._markerDiv.style.pointerEvents = "none";
        this._markerDiv.style.display = "none"; // Initially hidden
        this._store.containerCanvas?.appendChild(this._markerDiv);
    }
    //#region actionCreateAnnotation
    actionCreateAnnotation = () => {

        this._canvas?.addEventListener("mousedown", this._handleMouseDown);
        this._canvas?.addEventListener("mousemove", this._handleMouseMove);
        this._canvas?.addEventListener("mouseup", this._handleMouseUp);
        //For trigger select again
        this._store.containerCanvas?.addEventListener("click", this._onclickRemove as EventListener);

        this._markerDiv.style.display = "block"; // Show the marker div


        this._store.viewer!.cameraControl.snapRadius = 30;

        this._store.viewer!.cameraControl.snapToEdge = true;
        this._store.viewer!.cameraControl.snapToVertex = true;



    }

    //#region _handleMouseDown
    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
    };
    //#region _handleMouseMove
    private _handleMouseMove = (event: MouseEvent) => {
        this.updateCursorPosition([event.offsetX, event.offsetY]);
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) this._isDragging = true;




    };
    //#region _handleMouseUp
    private _handleMouseUp = (event: MouseEvent) => {

        if (event.button !== 0 || !this._mouseDownPos) return;
        if (!this._isDragging) {
            // this._createAnnotation(event)
            this._createAnotation2();
        };
        this._mouseDownPos = null;
        this._isDragging = false;
    };
    //#region _createAnnotation
    private _createAnnotation = (event: MouseEvent) => {
        this._isRemovingAnnotation = false;
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._store.canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        let hit: PickResult | null = null;

        while (true) {
            hit = this.picker.pickSurface(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            // console.log("hit anotation", hit);
            if (!hit || !hit.entity) break;

            if (!hit.entity.xrayed) {
                break; // gặp thằng không xray đầu tiên thì break
            }
        }
        if (hit) {
            const idAnotation = createUUID(); // Generate a unique ID for the annotation
            const posX = hit.worldPos[0].toFixed(4);
            const posY = hit.worldPos[2].toFixed(4);
            const posZ = hit.worldPos[1].toFixed(4);

            console.log(hit, posX, posY, posZ);

            const formatNumber = (num: string) => {
                return Number(num) >= 0 ? ` ${Number(num).toFixed(4)}` : Number(num).toFixed(4);
            };
            const annotation = this._annotations.createAnnotation({
                id: idAnotation, // Generate a unique ID for the annotation
                pickResult: hit, // <<------- initializes worldPos and entity from PickResult
                occludable: false,       // Optional, default is true
                markerShown: true,      // Optional, default is true
                labelShown: true,       // Optional, default is true
                values: {
                    markerBGColor: "red",
                    glyph: "",

                },
                markerHTML: `<div id='dpu-annotation-marker-${idAnotation}'  class='dpu-annotation-marker' style='background-color: {{markerBGColor}};'>{{glyph}}</div>`,
                labelHTML: `
        <div id="dpu-annotation-label-${idAnotation}" class="dpu-annotation-label" style="background-color: {{labelBGColor}}">
            <div class="dpu-annotation-content" >
                <span class='dpu-content-x'>X:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posX)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-y'>Y:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posY)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-z'>Z:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posZ)}</span>
            </div>
        </div>`

            });


            this._listAnnotations.set(annotation.id, annotation);

        }

    }
    //#region _createAnotation2
    private _createAnotation2 = () => {
        const idAnotation = createUUID(); // Generate a unique ID for the annotation
        const posX = this._pickResultTest.worldPos[0].toFixed(4);
        const posY = this._pickResultTest.worldPos[2].toFixed(4);
        const posZ = this._pickResultTest.worldPos[1].toFixed(4);


        const formatNumber = (num: string) => {
            return Number(num) >= 0 ? ` ${Number(num).toFixed(4)}` : Number(num).toFixed(4);
        };
        const annotation = this._annotations.createAnnotation({
            id: idAnotation, // Generate a unique ID for the annotation
            pickResult: this._pickResultTest, // <<------- initializes worldPos and entity from PickResult
            occludable: false,       // Optional, default is true
            markerShown: true,      // Optional, default is true
            labelShown: true,       // Optional, default is true
            values: {
                markerBGColor: "red",
                glyph: "",

            },
            markerHTML: `<div id='dpu-annotation-marker-${idAnotation}'  class='dpu-annotation-marker' style='background-color: {{markerBGColor}};'>{{glyph}}</div>`,
            labelHTML: `
        <div id="dpu-annotation-label-${idAnotation}" class="dpu-annotation-label" style="background-color: {{labelBGColor}}">
            <div class="dpu-annotation-content" >
                <span class='dpu-content-x'>X:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posX)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-y'>Y:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posY)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-z'>Z:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posZ)}</span>
            </div>
        </div>`

        });
    }
    //#region actionRemovebyClickAnnotation
    actionRemovebyClickAnnotation = () => {
        this._isRemovingAnnotation = true;
        this._store.viewer!.cameraControl.snapRadius = 30;
        this._store.containerCanvas?.addEventListener("click", this._onclickRemove as EventListener);

        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);
    }

    //#region actionShowHideAllAnnotations
    actionShowHideAllAnnotations = (show: boolean) => {
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);

        this._listAnnotations.forEach((annotation) => {
            annotation.setLabelShown(show);
            annotation.setMarkerShown(show);
        });
    }

    //#region _onclickRemove
    private _onclickRemove = (e: MouseEvent) => {
        e.preventDefault();
        if (this._hightlighedMarketSelected) {
            this._removeAnnotation(this._hightlighedMarketSelected.id);
        }
    }

    //#region _removeAnnotation
    private _removeAnnotation = (id: string) => {
        const annotation = this._listAnnotations.get(id);
        if (annotation) {
            this._annotations.destroyAnnotation(id);
            this._listAnnotations.delete(id);
        }
    }

    private _lastEntity: Entity | null = null;
    private _pickResultTest: any

    private _rafId: number | null = null;
    private _lastMarkerPos: [number, number] | null = null;
    updateCursorPosition = (canvasPos: number[]) => {
        // Sử dụng requestAnimationFrame để update marker mượt hơn
        if (this._rafId) {
            cancelAnimationFrame(this._rafId);
        }
        this._rafId = requestAnimationFrame(() => {
            this._pickResultTest = ((this._store.viewer) as any)?.scene.pick({
                canvasPos: canvasPos,
                snapRadius: 5,
                snapToEdge: true,
                snapToVertex: true
            });
            let x, y;
            if (this._pickResultTest && this._pickResultTest.snappedCanvasPos) {
                x = this._pickResultTest.snappedCanvasPos[0] - 10;
                y = this._pickResultTest.snappedCanvasPos[1] - 10;
                this._markerDiv.style.background = "greenyellow";
                this._markerDiv.style.border = "3px solid green";
            } else {
                x = canvasPos[0] - 10;
                y = canvasPos[1] - 5;
                this._markerDiv.style.background = "white";
                this._markerDiv.style.border = "1px solid black";
            }
            // Chỉ update DOM nếu vị trí thay đổi
            if (!this._lastMarkerPos || this._lastMarkerPos[0] !== x || this._lastMarkerPos[1] !== y) {
                this._markerDiv.style.left = `${x}px`;
                this._markerDiv.style.top = `${y}px`;
                this._lastMarkerPos = [x, y];
            }
        });
    }



    //#region actionRemoveAllAnnotations
    actionRemoveAllAnnotations = () => {
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);

        this._listAnnotations.forEach((annotation, id) => {
            this._annotations.destroyAnnotation(id);
        });
        this._listAnnotations.clear();
    }
    //#region removeAllEventListeners
    removeAllEventListeners = () => {
        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);
        this._isRemovingAnnotation = false;
        this._hightlighedMarketSelected = undefined;
    }

    //#region refreshActions
    refreshActions = () => {
        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);
        this.actionShowHideAllAnnotations(false);
        this._store.viewer!.cameraControl.snapRadius = 5;
        this._store.viewer!.cameraControl.snapToEdge = false;
        this._store.viewer!.cameraControl.snapToVertex = false;
        this._isRemovingAnnotation = false;
        this._hightlighedMarketSelected = undefined;
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);
    }



}