import { LoadXKTModel, Viewer } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";
import { ModelConfig, ModelLoader } from "./plugin/loadModel";
import { GLTFLoaderPlugin, CxConverterIFCLoaderPlugin } from "@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js";
import { WebIFCLoaderPlugin } from "./plugin/customWebIFCLoaderPlugin/WebIFCLoaderPlugin";

import * as CxConverter from "@creooxag/cxconverter";
import * as WebIFC from "web-ifc";
import { XKTLoaderPlugin } from "./plugin/customXKTLoaderPlugin/XKTLoaderPlugin";
export interface XKTLoadConfig extends LoadXKTModel {
    name?: string;
    isShow?: boolean;
    isFolder?: boolean;
}



export class SceneManager {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer: Viewer | undefined;
    private _gltfLoader: any = undefined; // GLTFLoaderPlugin
    _ifcLoader: WebIFCLoaderPlugin | undefined;

    private _loadFiles: ModelLoader | undefined;

    constructor() {
        this._viewer = this._store.viewer;
        if (!this._viewer) {
            console.error("Viewer not initialized");
            return;
        }
        if (!this._store.gltfLoaderPlugin) {
            this._store.gltfLoaderPlugin = new GLTFLoaderPlugin(this._viewer);
            this._gltfLoader = this._store.gltfLoaderPlugin;
        }

        // Khởi tạo IfcAPI một cách async

        GlobalStore.getInstance().set("sceneManager", this);
    }



    public gtlfLoad(modelId: string, modelSrc: string) {
        if (!this._gltfLoader) {
            this._gltfLoader = new GLTFLoaderPlugin(this._viewer!);
        }
        const sceneModel = this._gltfLoader.load({
            id: modelId,
            src: modelSrc,
            edges: true,
            // autoMetaModel: true,


        });

        sceneModel.on("loaded", (e: any) => {
            console.log(`[Viewer] Loaded "${modelId}"`);
            // this._store.treeLayerModel?.mapNewList();
            console.log('Model', sceneModel);

        })


        return sceneModel;
    }

    loadModel = async (modelConfig: ModelConfig, okFunc?: (model: any) => void) => {
        if (!this._loadFiles) {
            this._loadFiles = new ModelLoader();
        }
        await this._loadFiles.loadModel(modelConfig, okFunc);
    }

    public cityJsonLoad(modelId: string, modelSrc: any) {
        console.warn("CityJSONLoaderPlugin is not available in the current xeokit-sdk version");
        console.log("Attempted to load CityJSON model:", { modelId, modelSrc });
        return null;
    }

    // private _xktLoader = new XKTLoaderPlugin(thDis._store.viewer!);
    private _xktLoader = new XKTLoaderPlugin(this._store.viewer!, {
        reuseGeometries: false // <------- Disable geometry resuse
    });

    public XKTLoad(config: LoadXKTModel, okFunc?: (model: any) => void) {
        // TODO: Implement XKT loader
        const model = this._xktLoader.load({          // Returns an Entity that represents the model
            ...config,

        });
        model.on("loaded", () => {
            console.log(`[Viewer] Loaded "${config.id}"`);
            // this._store.treeLayerModel?.mapNewList();
            okFunc && okFunc(model);
        })
        return model;

    }


    public XKTLoadManifest({ modelId, manifestSrc }: { modelId: string, manifestSrc: string }) {
        // TODO: Implement XKT loader
        const model = this._xktLoader.load({          // Returns an Entity that represents the model
            src: manifestSrc,
            id: modelId,
            edges: true,

        });

        return model;

    }



    ifcLoader: WebIFCLoaderPlugin | undefined;
    ifcLoad = async ({ modelId, modelSrc, pathWasm }: { modelId: string, modelSrc: string, pathWasm: string }) => {
        if (!this._ifcLoader) {
            const IfcAPI = new WebIFC.IfcAPI();
            IfcAPI.SetWasmPath(pathWasm);

            await IfcAPI.Init();

            this.ifcLoader = new WebIFCLoaderPlugin(this._viewer!, {
                WebIFC: WebIFC,
                IfcAPI: IfcAPI,
            });
        }

        if (this.ifcLoader) {
            const sceneModel = this.ifcLoader.load({
                id: modelId,
                src: modelSrc,
                excludeTypes: ["IfcSpace"],
                edges: true,
                loadMetadata: true,
                saoEnabled: true,

            });

            sceneModel.on("loaded", (e: any) => {
                console.log(`[Viewer] Loaded "${modelId}"`);
                // this._store.treeLayerModel?.mapNewList();
                console.log('Model', sceneModel);
            });

        }
    }

    private _cxConverterIFCLoaderPlugin = new CxConverterIFCLoaderPlugin(this._store.viewer!);
    ifcLoadV2 = async ({ modelId, modelSrc }: { modelId: string, modelSrc: string }) => {
        this._cxConverterIFCLoaderPlugin.setCxConverterModule(CxConverter)


        const sceneModel = await this._cxConverterIFCLoaderPlugin.load({

            src: modelSrc,

        });
        sceneModel.on("loaded", (e: any) => {
            sceneModel.id = modelId;
            this._viewer?.cameraFlight.flyTo({
                aabb: sceneModel.aabb,
            });
        });

    }
    //#region getCameraView 
    getCameraView = (): {
        cameraEye: [number, number, number] | undefined,
        cameraLook: [number, number, number] | undefined,
        cameraUp: [number, number, number] | undefined,
    } => {
        const viewer = this._store.viewer;
        return {
            cameraEye: viewer?.camera.eye as [number, number, number] | undefined,
            cameraLook: viewer?.camera.look as [number, number, number] | undefined,
            cameraUp: viewer?.camera.up as [number, number, number] | undefined,

        }
    }

    setCameraView = (data: {
        cameraEye: [number, number, number] | undefined,
        cameraLook: [number, number, number] | undefined,
        cameraUp: [number, number, number] | undefined,
    }) => {
        const viewer = this._store.viewer;
        if (!viewer) return;

        const { cameraEye, cameraLook, cameraUp } = data;

        if (cameraEye) {
            viewer.camera.eye = cameraEye as [number, number, number];
        }
        if (cameraLook) {
            viewer.camera.look = cameraLook as [number, number, number];
        }
        if (cameraUp) {
            viewer.camera.up = cameraUp as [number, number, number];
        }
    }

}