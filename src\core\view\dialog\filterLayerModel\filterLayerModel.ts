import { DialogGeneral } from "../../../../widgets/dialog/dialog";
import GlobalStore from "../../../../core/globalStore/globalStore";
import { createUUID } from "../../../../utils/utils";
import { HyperList } from "../../../../utils/hyperlist/hyperlist";
import { IconSearch } from "../../../../utils/icon";
import { INodeLayerModel } from "../../../../types/layerModel/layerModel";
import loading from "../../../../assets/images/loading-roll-2.png"

interface ItemSelect {
    key: string;
    idNodes: string[];
    checked: boolean;
}

export class FilterLayerModel {
    // Backup for original list before search
    private _listDisplayItemBackup: ItemSelect[] = [];

    private _mapFilterKey: Map<string, string[]> = new Map<string, string[]>();
    // Persistent selected key map for filter checkboxes (same structure as _mapFilterKey)
    private _selectedKeys: Map<string, string[]> = new Map<string, string[]>();
    private _store = GlobalStore.getInstance().getAll();
    private _treeLayerModel = this._store.treeLayerModel;
    dialog: DialogGeneral | undefined;
    private _generateId: string = createUUID();
    private _list: HyperList | undefined
    _listDisplayItem: ItemSelect[] = [];
    private _rootNodeLayers: INodeLayerModel[] = []

    private _config = {
        height: '100%',
        itemHeight: 26,
        total: 50,
        reverse: false,
        scrollerTagName: 'div',
        // Customize the virtual row class, defaults to vrow.
        rowClassName: 'dpu-select-conatiner',
        generate: (row: number): HTMLDivElement => {
            const item = this._listDisplayItem[row];
            const div = this._createItemSelect(item) as HTMLDivElement;
            return div || document.createElement('div'); // Ensure a valid HTMLElement is always returned
        }
    }
    private _isShow: boolean = false;
    private onClosedCallback: ((status: boolean, data: any) => void) | undefined; // Callback khi đóng modal

    constructor() {
        this.createModalDialog();
        this.isShow = true;
        this.createLoading(true);
        this._handleFilter();
        this._rootNodeLayers = [...this._store.treeLayerModel!.newTreeViewNode];
        GlobalStore.getInstance().set("filterLayerModel", this);

    }
    get isShow(): boolean {
        return this._isShow;
    }
    set isShow(value: boolean) {
        this._isShow = value;
        this.dialog?.showModal(value)


    }
    //#region createModalDialog
    createModalDialog = () => {
        this.dialog = new DialogGeneral(
            this._store?.containerId!,
            this.handleCloseModal
        );
        this.dialog.innerHeader = 'Filter'
        this.dialog.isModal = true
        this.dialog.minimizeDialog = true;
        this.dialog.zIndex = 4005;
        this.dialog.topModal = "50%"
        this.dialog.leftModal = "50%"
        this.dialog.widthModal = '350px'

        this.dialog.heightModal = '450px'
        this.dialog.firstLoad = false
        // Lấy chuỗi HTML của tree view và gán cho innerBody
        this.dialog.innerBody = this.renderBodyModal();
        this.dialog.innerFooter = this.renderFooter();

        this.dialog.createDialog();

    }

    //#region setClosedCallback
    setClosedCallback(callback: (status: boolean, data: any) => void): void {
        this.onClosedCallback = callback;
    }
    //#region notifyCallBack
    // Gọi callback để thông báo thay đổi trạng thái
    notifyCallBack(status: boolean, data: any): void {
        if (this.onClosedCallback) {

            this.onClosedCallback(status, data); // Thông báo cho lớp cha

        }


    }

    //#region handleCloseModal
    private handleCloseModal = (status: boolean) => {
        if (!status) {

        }
        this.isShow = false;

    }
    //#region renderFooter
    private containerFooter: HTMLDivElement | undefined
    private renderFooter = (): HTMLElement => {
        this.containerFooter = document.createElement('div')
        this.containerFooter.style.cssText = 'width: 100%;display: flex; justify-content: space-between; align-items: center;padding: 5px 5px 0 5px;'


        const containerBtnLeft = document.createElement('div');
        containerBtnLeft.style.cssText = 'display: flex; gap: 5px; align-items: center;';
        this.containerFooter.appendChild(containerBtnLeft);

        const btnSelectAll = document.createElement('button');
        btnSelectAll.className = 'dpu-btn-defaut dpu-btn-handled-select-filter';
        btnSelectAll.textContent = 'Select All';
        btnSelectAll.addEventListener('click', () => this.handleSelectAll());
        containerBtnLeft.appendChild(btnSelectAll);

        const btnRemoveAll = document.createElement('button');
        btnRemoveAll.className = 'dpu-btn-defaut dpu-btn-handled-select-filter';
        btnRemoveAll.textContent = 'Remove All';
        btnRemoveAll.addEventListener('click', () => this.handleRemoveAll());
        containerBtnLeft.appendChild(btnRemoveAll);
        // Select all checkboxes and update selectedKeys





        const btnOk = document.createElement('button');
        btnOk.className = 'dpu-btn-ok-primary';
        btnOk.textContent = 'Ok';
        btnOk.addEventListener('click', () => this.handleOk());
        this.containerFooter.appendChild(btnOk);

        return this.containerFooter
    }

    //#region handleSelectAll
    // Select all checkboxes and update selectedKeys
    private handleSelectAll = () => {
        this._selectedKeys.clear();
        this._listDisplayItem.forEach(item => {
            item.checked = true;
            this._selectedKeys.set(item.key, item.idNodes);
        });
        this.handleRefreshList();
    }

    //#region handleRemoveAll
    // Remove all selections and update selectedKeys
    private handleRemoveAll = () => {
        this._selectedKeys.clear();
        this._listDisplayItem.forEach(item => {
            item.checked = false;
        });
        this.handleRefreshList();
    }

    //#region renderBodyModal
    private containerBody: HTMLDivElement | undefined
    private _containerBodySelect: HTMLDivElement | undefined
    private renderBodyModal = (): HTMLElement => {
        if (this.containerBody) {
            this.clearContainer()
        } else {
            this.containerBody = document.createElement('div')
            this.containerBody.className = 'container-layer-filter'
            this._createSearch();
            // this._createItem();
            this._containerBodySelect = document.createElement('div');
            this._containerBodySelect.className = 'container-select-layer-filter';
            this._containerSelect = document.createElement('div');
            this._containerBodySelect.appendChild(this._containerSelect);

            this.containerBody.appendChild(this._containerBodySelect);

        }

        return this.containerBody
    }




    //#region clearContainer
    private clearContainer = () => {
        if (this.containerBody) {
            this.containerBody.innerHTML = '';
        }
    };
    //#region _createSearch
    private _inputSearch: HTMLInputElement | null = null;
    private _searchBtn: HTMLButtonElement | null = null;
    private _createSearch = () => {
        const containerSearch = document.createElement("div");
        containerSearch.style.cssText = "padding:5px;width:100%;display:flex";

        this._inputSearch = document.createElement("input");
        this._inputSearch.className = "dpu-input-text";
        this._inputSearch.setAttribute("type", "text");
        this._inputSearch.placeholder = "Search...";


        containerSearch.appendChild(this._inputSearch);

        this._searchBtn = document.createElement("button");
        this._searchBtn.className = "dpu-btn-defaut";
        this._searchBtn.innerHTML = `${IconSearch()}`;
        this._searchBtn.style.width = "32px";
        this._searchBtn.style.height = "30px";
        this._searchBtn.style.padding = "5px";
        this._searchBtn.style.marginLeft = "5px";

        // // Chỉ thêm event listener cho button search
        this._searchBtn.addEventListener('click', this._handeleSearch);

        containerSearch.appendChild(this._searchBtn);

        this.containerBody?.appendChild(containerSearch);
    }
    //#region _handeleSearch 
    private _handeleSearch = () => {
        const searchTerm = this._inputSearch?.value.toLowerCase() || '';
        // Backup original list if not already backed up
        if (this._listDisplayItemBackup.length === 0) {
            this._listDisplayItemBackup = [...this._listDisplayItem];
        }
        // If search is empty, restore full list
        if (!searchTerm.trim()) {
            this._listDisplayItem = [...this._listDisplayItemBackup];
            this.handleRefreshList();
            return;
        }
        // Filter by key contains search term
        const filtered = this._listDisplayItemBackup.filter(item =>
            item.key.toLowerCase().includes(searchTerm)
        );
        // Update _listDisplayItem and refresh
        this._listDisplayItem = filtered;
        this.handleRefreshList();
    }

    //#region _createItemSelect
    private _containerSelect: HTMLDivElement | undefined;
    private _createItemSelect = (itemSelect: ItemSelect) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'dpu-select-conatiner';

        const checkbox = document.createElement('input');
        checkbox.id = `checkbox-${itemSelect.key}`;
        checkbox.type = 'checkbox';
        checkbox.className = 'dpu-checkbox-filter';
        checkbox.value = itemSelect.key;
        checkbox.checked = itemSelect.checked;

        // Add event listener to update persistent selected keys
        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                itemSelect.checked = true;
                // Add key and its value (array) to selectedKeys
                this._selectedKeys.set(itemSelect.key, itemSelect.idNodes);

            } else {
                itemSelect.checked = false;
                // Remove key from selectedKeys
                this._selectedKeys.delete(itemSelect.key);
            }
            // Optionally, notify callback or update UI here
            // this.notifyCallBack(true, Array.from(this._selectedKeys.keys()));
        });

        const label = document.createElement('label');
        label.className = 'dpu-label-filter';
        label.textContent = itemSelect.key;
        label.htmlFor = checkbox.id;

        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);
        this._containerSelect?.appendChild(wrapper);
        return wrapper
    }

    //#region handleOk
    handleOk = () => {
        this.isShow = false;
        this._treeLayerModel!.isLoading = true;
        if (this._selectedKeys.size === 0) {

            this._treeLayerModel!.isFiltering = false;
            this._treeLayerModel!.newTreeViewNode = [...this._rootNodeLayers];
            this._treeLayerModel!.handleDisplayItem();
            this._treeLayerModel!.handleRefreshList();
            this._treeLayerModel!.isLoading = false;

        } else {
            this._treeLayerModel!.isFiltering = true;

            this.handleProgressDataNode();


        }

    }

    //#region handleProgressDataNode
    handleProgressDataNode = () => {
        // Chỉ truyền id vào worker, không truyền object gốc
        const selectedKeys = Array.from(this._selectedKeys.entries());
        const rootNodeLayerIds = this._rootNodeLayers.map(node => ({ id: node.id, objectId: node.nodeXeokit?.objectId, children: node.children }));

        function handleProgressDataNodeWorker() {
            self.onmessage = function (e) {
                const { selectedKeys, rootNodeLayerIds } = e.data as any;
                const tempNodeLayers = [] as any[];
                (selectedKeys || []).forEach(([key, idNodes]: [any, any]) => {
                    const idParent = 'parent-' + key + '-' + Date.now() + '-' + Math.floor(Math.random() * 100000);
                    let hasChild = false;
                    let countChild = 0;
                    (idNodes || []).forEach((idNode: any) => {
                        const foundNode = (rootNodeLayerIds || []).find((node: any) => node.objectId === idNode && node.children.length === 0);
                        if (foundNode) {
                            countChild++;
                            if (!hasChild) {
                                // Thêm số lượng con thực tế vào key
                                tempNodeLayers.push({ id: idParent, isParent: true, key: `${key} (0)`, idNodes });
                                hasChild = true;
                            }
                            tempNodeLayers.push({ id: foundNode.id, parentId: idParent, isParent: false });
                        }
                    });
                    // Sau khi duyệt xong, cập nhật lại key cho parent (nếu có)
                    if (hasChild) {
                        const parentIdx = tempNodeLayers.findIndex(n => n.id === idParent && n.isParent);
                        if (parentIdx !== -1) {
                            tempNodeLayers[parentIdx].key = `${key} (${countChild})`;
                        }
                    }
                });
                const tempNodeIds = new Set((tempNodeLayers as any[]).map((n: any) => n.id));
                const parentUnknownId = 'parent-unknown-' + Date.now() + '-' + Math.floor(Math.random() * 100000);
                const remainingList = (rootNodeLayerIds || []).filter((node: any) => !tempNodeIds.has(node.id) && (!node.children || node.children.length === 0));
                tempNodeLayers.push({ id: parentUnknownId, isParent: true, key: `Unknown (${remainingList.length})`, idNodes: [] });
                (remainingList || []).forEach((node: any) => {
                    tempNodeLayers.push({ id: node.id, parentId: parentUnknownId, isParent: false });
                });
                self.postMessage({ tempNodeLayers });
            };
        }

        // Tạo worker blob
        const workerCode = `(${handleProgressDataNodeWorker.toString()})()`;
        const blob = new Blob([workerCode], { type: 'application/javascript' });
        const worker = new Worker(URL.createObjectURL(blob));

        worker.onmessage = (e) => {
            const { tempNodeLayers } = e.data;
            // Map id sang object gốc
            const idToNode = new Map(this._rootNodeLayers.map(n => [n.id, n]));
            const newTreeViewNode: INodeLayerModel[] = [];
            const listParentNode = new Map<string, INodeLayerModel>();
            // Tạo parent node (folder) mới
            tempNodeLayers.forEach((item: any) => {
                if (item.isParent) {
                    // Tạo node cha mới
                    const parentNode: INodeLayerModel = {
                        id: item.id,
                        title: item.key,
                        isShow: true,
                        isFolder: true,
                        parent: null,
                        expand: false,
                        level: 0,
                        children: [],
                        childrenFilter: item.idNodes ? [...item.idNodes] : [],
                        listNodesXeokit: []
                    };
                    newTreeViewNode.push(parentNode);
                    listParentNode.set(item.id, parentNode);
                }
            });
            // Gán con vào cha, tạo node con mới (clone), set parent đúng parentNode
            tempNodeLayers.forEach((item: any) => {
                if (!item.isParent) {
                    const parentNode = listParentNode.get(item.parentId);
                    const originNode = idToNode.get(item.id);
                    if (parentNode && originNode) {
                        const childNode: INodeLayerModel = {
                            ...originNode,
                            id: item.id, // giữ id theo worker trả về
                            parent: parentNode,
                            level: 1
                        };
                        parentNode.children?.push(childNode);
                        parentNode.listNodesXeokit?.push(originNode.nodeXeokit!);
                        newTreeViewNode.push(childNode);
                    }
                }
            });
            this._treeLayerModel!.newTreeViewNode = [];
            this._treeLayerModel!.newTreeViewNode = [...newTreeViewNode];
            if (this._treeLayerModel!.listParentNode && typeof this._treeLayerModel!.listParentNode.clear === 'function') {
                this._treeLayerModel!.listParentNode.clear();
                listParentNode.forEach((node, id) => {
                    this._treeLayerModel!.listParentNode.set(id, node);
                });
            }
            this._treeLayerModel!.handleDisplayItem();
            this._treeLayerModel!.handleRefreshList();
            this._treeLayerModel!.isLoading = false;
            worker.terminate();
        };

        worker.postMessage({ selectedKeys, rootNodeLayerIds });

    }

    //#region _handleFilter
    // private _handleFilter = () => {
    //     const metaScene = this._store?.viewer?.metaScene;
    //     if (!metaScene?.propertySets) return;

    //     // Only send plain data to worker
    //     const safePropertySets = Object.values(metaScene.propertySets).map((propertySet: any) => ({
    //         id: propertySet.id,
    //         properties: (propertySet.properties || []).map((property: any) => ({
    //             name: property.name
    //         }))
    //     }));

    //     // Worker function with Set type and explicit Record typing, returns both mapFilterKey and listItemSelect
    //     function filterWorker() {
    //         self.onmessage = function (e) {
    //             const { propertySets } = e.data;
    //             const mapFilterKey = {};
    //             Object.values(propertySets || {}).forEach(function (propertySet) {
    //                 const ps = propertySet as any;
    //                 (ps.properties || []).forEach(function (property: any) {
    //                     if (property.name) {
    //                         if (!(mapFilterKey as Record<string, Set<string>>)[property.name]) {
    //                             (mapFilterKey as Record<string, Set<string>>)[property.name] = new Set<string>();
    //                         }
    //                         (mapFilterKey as Record<string, Set<string>>)[property.name].add(ps.id);
    //                     }
    //                 });
    //             });
    //             // Convert Set to Array for postMessage
    //             const result: Record<string, string[]> = {};
    //             Object.keys(mapFilterKey).forEach((key) => {
    //                 result[key] = Array.from((mapFilterKey as Record<string, Set<string>>)[key]);
    //             });
    //             // Build listItemSelect array
    //             const listItemSelect = Object.keys(result).map((key) => ({
    //                 key,
    //                 idNodes: result[key],
    //                 checked: false
    //             }));
    //             self.postMessage({ mapFilterKey: result, listItemSelect });
    //         };
    //     }

    //     // Convert function to string
    //     const workerCode = `(${filterWorker.toString()})()`.replace(/[\r\n]+/g, '');
    //     const blob = new Blob([workerCode], { type: 'application/javascript' });
    //     const worker = new Worker(URL.createObjectURL(blob));

    //     worker.onmessage = (e) => {
    //         const { mapFilterKey, listItemSelect } = e.data;
    //         this._mapFilterKey = new Map(Object.entries(mapFilterKey));
    //         this._listDisplayItem = listItemSelect || [];
    //         console.log("this._mapFilterKey", this._mapFilterKey);
    //         console.log("this._listDisplayItem", this._listDisplayItem);
    //         this.handleRefreshList();
    //         // this._createItem();
    //         worker.terminate(); // cleanup
    //     };

    //     worker.postMessage({
    //         propertySets: safePropertySets
    //     });
    // }
    private _handleFilter = () => {
        // this.createLoading(true);
        const metaScene = this._store?.viewer?.metaScene;
        if (!metaScene?.metaObjects) return;

        // Chuẩn bị dữ liệu "safe" gửi vào worker: mỗi metaObject chỉ gồm id và danh sách properties (chỉ name)
        const safeMetaObjects = Object.values(metaScene.metaObjects).map((metaObj: any) => ({
            id: metaObj.id,
            propertySets: (metaObj.propertySets || []).map((ps: any) => ({
                properties: (ps?.properties || []).map((p: any) => ({ name: p.name }))
            }))
        }));

        // Worker xử lý: xây dựng map từ property.name -> Set(metaObject.id) rồi trả về dưới dạng mảng
        function filterWorker() {
            self.onmessage = function (e) {
                const { metaObjects } = e.data;
                const mapFilterKey = {} as Record<string, Set<string>>;

                (metaObjects || []).forEach(function (mo: any) {
                    const metaId = mo.id;
                    (mo.propertySets || []).forEach(function (ps: any) {
                        (ps.properties || []).forEach(function (prop: any) {
                            if (prop && prop.name) {
                                if (!mapFilterKey[prop.name]) {
                                    mapFilterKey[prop.name] = new Set<string>();
                                }
                                mapFilterKey[prop.name].add(metaId);
                            }
                        });
                    });
                });

                // Chuyển Set sang Array để postMessage được (structured-clone)
                const result: Record<string, string[]> = {};
                Object.keys(mapFilterKey).forEach((key) => {
                    result[key] = Array.from(mapFilterKey[key]);
                });

                const listItemSelect = Object.keys(result)
                    .filter((key) => result[key] && result[key].length > 0)
                    .map((key) => ({
                        key,
                        idNodes: result[key],
                        checked: false
                    }));

                self.postMessage({ mapFilterKey: result, listItemSelect });
            };
        }

        // tạo worker từ function
        const workerCode = `(${filterWorker.toString()})()`;
        const blob = new Blob([workerCode], { type: 'application/javascript' });
        const worker = new Worker(URL.createObjectURL(blob));

        worker.onmessage = (e) => {
            const { mapFilterKey, listItemSelect } = e.data;
            // mapFilterKey là Record<string, string[]>
            this._mapFilterKey = new Map(Object.entries(mapFilterKey));
            this._listDisplayItem = listItemSelect || [];
            // console.log("this._mapFilterKey", this._mapFilterKey);
            // console.log("this._listDisplayItem", this._listDisplayItem);
            this.createLoading(false);
            this.handleRefreshList();

            worker.terminate();
        };

        worker.postMessage({ metaObjects: safeMetaObjects });
    };

    //#region handleRefreshList 
    handleRefreshList = () => {
        if (this._containerSelect && !this._list) {
            // const height = this.containerUl?.getBoundingClientRect().height || window.innerHeight;
            this._config.total = this._listDisplayItem.length
            this._list = new HyperList(this._containerSelect!, this._config)
        }


        this._config.total = this._listDisplayItem.length
        this._list?.refresh(this._containerSelect!, this._config)

    }

    //#region createLoading
    private _loadingElement: HTMLElement | null = null;
    createLoading = (isShowLoading: boolean) => {
        // const containerSelectLayer = this._containerBodySelect?.querySelector('.container-select-layer-filter')

        if (!this._loadingElement) {
            this._loadingElement = document.createElement('div');
            this._loadingElement.style.cssText = "position: absolute;top:0;width: 100%;height: 100%;background-color: rgba(255, 255, 255, 0.2);z-index: 9999;";

            this._loadingElement.innerHTML = `<div style=" display:flex;justify-content:center;align-items:center ;height: 100%;"><img class = "dpu-loading-animation" src="${loading}" alt="Loading..." width="35" height ="35"  style="pointer-events: none;"/></div>`
        }
        if (isShowLoading) {
            // this._containerBodyDialog.style.position = 'relative';
            this._containerBodySelect?.appendChild(this._loadingElement);
        } else {
            if (this._loadingElement && this._containerBodySelect?.contains(this._loadingElement)) {
                this._containerBodySelect?.removeChild(this._loadingElement);
            }
        }
    }
}

