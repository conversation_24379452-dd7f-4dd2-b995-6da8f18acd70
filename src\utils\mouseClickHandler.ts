type MouseClickCallback = (event: MouseEvent) => void;

export class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {
    private _mouseDownPos: { x: number; y: number } | null = null;
    private _isDragging: boolean = false;
    private _element: HTMLElement;
    private _onClick: MouseClickCallback;

    constructor(element: HTMLElement, onClick: MouseClickCallback) {
        this._element = element;
        this._onClick = onClick;

        // Gán sự kiện
        this._element.addEventListener("mousedown", this._handleMouseDown);
        this._element.addEventListener("mousemove", this._handleMouseMove);
        this._element.addEventListener("mouseup", this._handleMouseUp);
    }


    initializeEventListener = () => {
        this._element.addEventListener("mousedown", this._handleMouseDown);
        this._element.addEventListener("mousemove", this._handleMouseMove);
        this._element.addEventListener("mouseup", this._handleMouseUp);
    }

    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
    };

    private _handleMouseMove = (event: MouseEvent) => {
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) this._isDragging = true;
    };

    private _handleMouseUp = (event: MouseEvent) => {
        if (event.button !== 0 || !this._mouseDownPos) return;
        if (!this._isDragging) {
            this._onClick(event);
        }
        this._mouseDownPos = null;
        this._isDragging = false;
    };

    // Nếu muốn huỷ đăng ký event khi không cần nữa
    public destroy() {
        this._element.removeEventListener("mousedown", this._handleMouseDown);
        this._element.removeEventListener("mousemove", this._handleMouseMove);
        this._element.removeEventListener("mouseup", this._handleMouseUp);
    }
}
