import { createUUID } from "../../../../utils/utils";
import GlobalStore from "../../../../core/globalStore/globalStore";
import { DialogGeneral } from "../../../../widgets/dialog/dialog";
import { TreeViewPlugin } from "@xeokit/xeokit-sdk";
import { IconArrowDown, IconEyeHide, IconEyeShow, IconSearch } from "../../../../utils/icon";
import { HyperList } from "../../../../utils/hyperlist/hyperlist";
import { TreeViewNode } from "@xeokit/xeokit-sdk/types/plugins/TreeViewPlugin/TreeViewNode";

export interface NodeModel {
    nodeId: string;
    title: string;
    objectId: string;
    type: string;
    children: NodeModel[];
    parent: NodeModel | null;
    numEntities: number;
    numVisibleEntities: number;
    checked: boolean;
    xrayed: boolean;
    expand?: boolean
    level?: number;
    isShow?: boolean;
}


export class TreeLayerModeltest {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer = this._store.viewer!;
    private _targetContainerId = this._store.containerId as string;
    private _idGeneral: string = createUUID();
    private _dialog: DialogGeneral | undefined;
    private _treeViewPlugin: TreeViewPlugin | undefined;
    private _newTreeViewNode: NodeModel[] = [];
    private _list: HyperList | undefined

    private _config = {
        height: '100%',
        itemHeight: 33,
        total: 50,
        reverse: false,
        scrollerTagName: 'div',
        // Customize the virtual row class, defaults to vrow.
        rowClassName: 'dpu-tree-item',
        generate: (row: number): HTMLDivElement => {
            const item = this._listDisplayItem[row];
            const div = this._createItemTag(item, this._listDisplayItem) as HTMLDivElement;
            return div ?? document.createElement('div'); // Ensure a valid HTMLElement is always returned
        }
    }
    // Thuộc tính để lưu callback
    private onClosedCallback: ((status: boolean) => void) | undefined;
    constructor() {
        // GlobalStore.getInstance().set("treeLayerModel", this);

        this._createModal();
        this._dialog?.showModal(false)


    }

    private _createModal() {
        this._dialog = new DialogGeneral(
            this._targetContainerId,
            this._handleCloseModal
        );
        this._dialog.innerHeader = "Model";
        this._dialog.modal = false
        this._dialog.minimizeDialog = true;
        this._dialog.widthModal = '300px'
        this._dialog.heightModal = '500px'
        this._dialog.zIndex = 4000
        this._dialog.topModal = '5px'
        this._dialog.leftModal = '5px'
        // Lấy chuỗi HTML của tree view và gán cho innerBody
        this._dialog.innerBody = this.renderTreeLayerModel();
        this._dialog.createDialog();

        this._treeViewPlugin = new TreeViewPlugin(
            this._viewer!, {
            containerElementId: this._store.containerId,
            // containerElement: this._containerBodyTree!,

        }
        )


        console.log("TreeViewPlugin instance:", this._treeViewPlugin);
    }

    private _handleCloseModal = (status: boolean) => {
        if (!status) {
            this.notifyCallBack(status)
        }
    }

    // Gọi callback để thông báo thay đổi trạng thái
    notifyCallBack(status: boolean): void {
        if (this.onClosedCallback) {
            this.onClosedCallback(status); // Thông báo cho lớp cha
        } else {
            console.error("State change callback is not set.");
        }
    }
    // Gán callback từ lớp cha
    setClosedCallback(callback: (status: boolean) => void): void {
        this.onClosedCallback = callback;
    }
    //#region  renderTreeLayerModel
    private _containerBodyTree: HTMLElement | null = null;
    private _containerBodyDialog = document.createElement("div")
    // Hàm renderTreeView
    renderTreeLayerModel = (): HTMLElement => {

        // Nếu containerUl đã tồn tại, remove nó khỏi DOM
        if (this._containerBodyTree) {
            this._clearContainer()
        } else {
            this._containerBodyDialog = document.createElement("div");
            this._containerBodyDialog.style.cssText = "width:100%;height:100%;overflow:hidden";

            this._createSearch()


            // Tạo một phần tử div mới để chứa tree view
            this._containerBodyTree = document.createElement("div");
            this._containerBodyTree.id = `tree-layer-model-${this._idGeneral}`;

            const containerHeight = document.createElement("div");
            containerHeight.style.height = "calc(100% - 40px)";

            containerHeight.appendChild(this._containerBodyTree);

            this._containerBodyDialog.appendChild(containerHeight);
        }

        //Render ra tree layer
        this.handleDisplayItem()

        this._createTreeNode(this._listDisplayItem)



        return this._containerBodyDialog;
        // return this.containerDialog!;
    };
    private _clearContainer = () => {
        if (this._containerBodyTree) {
            this._containerBodyTree.innerHTML = ""; // Xóa nội dung bên trong containerUl
        }
    };

    //#region _createSearch
    private _inputSearch: HTMLInputElement | null = null;
    private _searchBtn: HTMLButtonElement | null = null;
    private _createSearch = () => {
        const containerSearch = document.createElement("div");
        containerSearch.style.cssText = "padding:5px;width:100%;display:flex";

        this._inputSearch = document.createElement("input");
        this._inputSearch.className = "dpu-input-text";
        this._inputSearch.setAttribute("type", "text");
        this._inputSearch.placeholder = "Search...";


        containerSearch.appendChild(this._inputSearch);

        this._searchBtn = document.createElement("button");
        this._searchBtn.className = "dpu-btn-defaut";
        this._searchBtn.textContent = "Search";
        this._searchBtn.style.marginLeft = "5px";

        // Chỉ thêm event listener cho button search
        this._searchBtn.addEventListener('click', this._handeleSearch);

        containerSearch.appendChild(this._searchBtn);

        this._containerBodyDialog.appendChild(containerSearch);
    }
    //#region  _handeleSearch
    // ...existing code...
    private _originalListDisplayItem: NodeModel[] = []; // Lưu list tạm
    private _handeleSearch = () => {
        const searchTerm = this._inputSearch?.value?.trim().toLowerCase() || '';

        // Nếu input rỗng, trả lại list display tạm
        if (searchTerm === '') {
            if (this._originalListDisplayItem.length > 0) {
                this._listDisplayItem = [...this._originalListDisplayItem];
                this._originalListDisplayItem = []; // Clear list tạm
                this.handleRefreshList();

            }
            return;
        }

        // Lưu lại list display hiện tại nếu chưa lưu
        if (this._originalListDisplayItem.length === 0) {
            this._originalListDisplayItem = [...this._listDisplayItem];
        }

        // Tìm kiếm trong _newTreeViewNode
        const searchResults = this._newTreeViewNode.filter(node => {
            const titleMatch = node.title?.toLowerCase().includes(searchTerm);
            const typeMatch = node.type?.toLowerCase().includes(searchTerm);
            const objectIdMatch = node.objectId?.toLowerCase().includes(searchTerm);

            return titleMatch || typeMatch || objectIdMatch;
        });

        // Gán kết quả search vào listDisplay hiện tại
        this._listDisplayItem = searchResults

        // Refresh hiển thị
        this.handleRefreshList();
    }


    //#region showHideModalTreeLayerModel
    showHideModalTreeLayerModel = (show: boolean) => {
        if (show) {
        } else {
        }
        this._dialog?.showModal(show)

    }
    private _createTreeNode = (flatData: NodeModel[]) => {
        flatData.forEach((currentItem: NodeModel) => {
            const div = this._createItemTag(currentItem, flatData)
            if (div) {
                this._containerBodyTree!.appendChild(div);
            }
        })
    }

    private _listItemMap: Map<string, NodeModel> = new Map<string, NodeModel>()

    private _createItemTag = (currentItem: NodeModel, flatData: NodeModel[]): HTMLElement => {
        if (!currentItem) return document.createElement('div');
        this._listItemMap.set(currentItem.objectId, currentItem)
        const div = document.createElement('div');
        div.setAttribute('data-layer-id', currentItem.objectId);
        if (currentItem.level || currentItem.level !== 0) {
            div.setAttribute('data-level', String(currentItem?.level ?? ""));
            div.style.paddingLeft = `${(currentItem.level ?? 0) * 15}px`;
        }
        const fullrow = document.createElement('div');
        fullrow.className = "fullrow";
        div.appendChild(fullrow);

        div.addEventListener('click', (event) => this.handleItemSelection(event, div, currentItem));

        //Tạo tất cả row
        const span = this.createCaretSpan(currentItem);
        div.appendChild(span);

        //Tạo nút visible/search
        const buttonContainer = this.createButtonContainer(currentItem, div);

        div.appendChild(buttonContainer);

        return div
    }

    private createButtonContainer(currentItem: NodeModel, li: HTMLElement): HTMLDivElement {
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'button-container';
        // Tạo nút tìm kiếm
        const showButton = document.createElement('div');
        showButton.className = 'folder-button';
        showButton.setAttribute('data-folder-id', currentItem.objectId.toString());
        if (currentItem.checked === undefined) {
            currentItem.checked = true
        }
        showButton.innerHTML = currentItem.checked ? IconEyeShow() : IconEyeHide();
        showButton.addEventListener('click', (event) => {

            event.stopPropagation();

            this._handleToggleVisible(currentItem, showButton)


            // this.handleItemSelection(event, li, currentItem)
            // Làm nổi bật mục được chọn
            const allTreeItems = this._containerBodyTree!.querySelectorAll('.dpu-tree-item');
            allTreeItems.forEach(item => item.classList.remove('active'));
            const parentLi = searchButton.closest('.dpu-tree-item') as HTMLLIElement;
            parentLi?.classList.add('active');
        });

        // Thêm nút vào container
        buttonContainer.appendChild(showButton);
        // Tạo nút tìm kiếm
        const searchButton = document.createElement('div');
        searchButton.className = 'search-button';
        searchButton.innerHTML = IconSearch(); // Hàm tạo biểu tượng tìm kiếm
        searchButton.addEventListener('click', (event) => {
            event.stopPropagation();
            this._handleZoomTo(currentItem)

            // this.zoomLabelPoint(currentItem);

            // Làm nổi bật mục được chọn
            const allTreeItems = this._containerBodyTree!.querySelectorAll('.dpu-tree-item');
            allTreeItems.forEach(item => item.classList.remove('active'));
            const parentLi = searchButton.closest('.dpu-tree-item') as HTMLLIElement;
            parentLi?.classList.add('active');
        });
        buttonContainer.appendChild(searchButton);




        return buttonContainer;
    }



    private createCaretSpan(currentItem: NodeModel): HTMLSpanElement {
        const span = document.createElement('span');
        span.className = "caret";
        //Tạo arrow
        this.createNestedContainer(currentItem, span)

        const textNode = document.createElement('span');
        textNode.className = "text";
        textNode.setAttribute('data-layer-text-id', currentItem.objectId);
        textNode.textContent = (currentItem.title === "") ? currentItem.type || "" : currentItem.title;
        if (currentItem.checked === false) {
            textNode.style.opacity = "0.5"
        }
        span.appendChild(textNode);

        return span;
    }

    private createNestedContainer(currentItem: NodeModel, span: HTMLElement) {
        const spanHolder = document.createElement('span');
        if (currentItem.level == 0 || currentItem.children.length > 0) {
            spanHolder.className = "arrow-icon";
            spanHolder.innerHTML = IconArrowDown();
            if (currentItem.expand === undefined || currentItem.expand === null) {
                currentItem.expand = false

            }
            if (currentItem.expand) {
                spanHolder.classList.add("arrow-down");
            } else {
                spanHolder.classList.remove("arrow-down");

            }

            spanHolder.addEventListener("click", (event) => {
                event.stopPropagation();
                this._actionArrow(currentItem, spanHolder)
            });

        } else {
            spanHolder.className = "arrow-placeholder";
        }



        span.appendChild(spanHolder);


        // children.forEach(child => {
        //     const childItem = this.createTreeItem(flatData, child);
        //     nestedContainer.appendChild(childItem);
        // });

    }

    //#region actionArrow
    private _actionArrow = async (currentItem: any, arrow: HTMLSpanElement) => {


        if (!currentItem.expand) {
            currentItem.expand = true

            this.handleDisplayItem(currentItem)


        } else {
            currentItem.expand = false
            this.handleRemoveDisplayItem(currentItem)

        }
        this.handleRefreshList()
        // arrow.classList.toggle("arrow-down");




    }
    //#region _handleToggleVisible
    // Hàm xử lý khi nút bên trái được nhấn
    private _handleToggleVisible = async (dataView: NodeModel, button: HTMLElement) => {
        const isVisible = !dataView.checked; // đổi logic ở đây
        dataView.checked = isVisible;
        // dataView.checked = !dataView.checked

        // button.innerHTML = isVisible ? IconEyeShow() : IconEyeHide();

        const treeViewNode = this._treeViewNodes ? (this._treeViewNodes as any)[dataView.nodeId] : undefined;
        if (treeViewNode) {
            this._viewer.scene.setObjectsXRayed(this._viewer.scene.objectIds, true);
            this._treeViewPlugin?.withNodeTree(treeViewNode, (treeViewNode: any) => {
                if (treeViewNode.objectId) {
                    const entity = this._viewer.scene.objects[treeViewNode.objectId];
                    if (entity) {
                        entity.xrayed = false
                        entity.visible = isVisible;
                    }
                }
            });
        }
        this.handleRefreshList()
    }
    //#region _handleZoomTo
    private _handleZoomTo = async (currentItem: NodeModel) => {
        const isVisible = currentItem.checked;
        if (!isVisible) {
            // button.innerHTML = isVisible ? IconEyeShow() : IconEyeHide();
            const node = this._containerBodyTree!.querySelector(`[data-layer-text-id="${currentItem.objectId}"]`) as HTMLElement;
            const btnShow = this._containerBodyTree!.querySelector(`[data-folder-id="${currentItem.objectId}"]`) as HTMLElement;
            node.style.removeProperty('opacity');
            btnShow.innerHTML = IconEyeShow()
        }

        const objectIds: string[] = [];
        const treeViewNode = this._treeViewNodes ? (this._treeViewNodes as any)[currentItem.nodeId] : undefined;
        if (treeViewNode) {
            this._treeViewPlugin?.withNodeTree(treeViewNode, (treeViewNode: any) => {
                if (treeViewNode.objectId) {
                    objectIds.push(treeViewNode.objectId);
                }
            });
        }
        //Show all
        this._viewer.scene.setObjectsVisible(this._viewer.scene.objectIds, true);
        //xrayed all
        this._viewer.scene.setObjectsXRayed(this._viewer.scene.objectIds, true);

        //xrayed Ids
        this._viewer.scene.setObjectsXRayed(objectIds, false);
        this._viewer.cameraFlight.flyTo({
            aabb: this._viewer.scene.getAABB(objectIds),
            duration: 0.5
        },);
        this.handleRefreshList()
    }

    private handleItemSelection(event: MouseEvent, li: HTMLElement, currentItem: any): void {
        // const isCtrl = event.ctrlKey;
        // const isShift = event.shiftKey;

        // if (isCtrl) {
        //     this.toggleSelectItem(li, currentItem);
        //     return;
        // }

        // if (isShift && this.lastSelectedItem) {
        //     this.selectRange(li, currentItem);
        //     return;
        // }

        this.selectSingleItem(li, currentItem);
    }

    //#region selectSingleItem
    private selectedRow: NodeModel | null = null;
    private selectSingleItem(li: HTMLElement, currentItem: NodeModel): void {

        //Lưu lại dòng được chọn
        this.selectedRow = currentItem

        const allTreeItems = this._containerBodyTree!.querySelectorAll('.dpu-tree-item');
        allTreeItems.forEach(item => item.classList.remove('active'));

        li.classList.add('active');

    }

    private _listDisplayItem: NodeModel[] = [];
    private _buttonsMapHide = new Map<string, NodeModel>()

    handleDisplayItem = (startItem?: NodeModel) => {
        if (this._newTreeViewNode.length == 0) return

        if (!startItem) {
            this._listDisplayItem = []

            const rootItems = this._newTreeViewNode.filter(item => item.parent === null || item.parent === undefined);
            // Set level cho root
            rootItems.forEach(root => {
                root.level = 0;
                this._listDisplayItem.push(root);
            });
            // Duyệt từng phần tử để xử lý expand
            let index = 0;
            while (index < this._listDisplayItem.length) {
                const currentItem = this._listDisplayItem[index];
                if (currentItem.expand) {
                    const children = this._newTreeViewNode.filter(item => item.parent?.objectId === currentItem.objectId);
                    children.forEach(child => {
                        child.level = (currentItem.level || 0) + 1;
                    });
                    // currentItem.childrenObject = children;
                    this._listDisplayItem.splice(index + 1, 0, ...children);
                }
                index++;
            }
        } else {
            // Tìm vị trí startItem
            const startIndex = this._listDisplayItem.findIndex(item => item.objectId === startItem.objectId);
            if (startIndex !== -1) {
                const children = this._newTreeViewNode.filter(item => item.parent?.objectId === startItem.objectId);
                children.forEach(child => {
                    child.level = (startItem.level || 0) + 1;
                    if (!startItem.checked) {
                        this._buttonsMapHide.set(child.objectId, child); // Thêm vào map nếu không hiển thị
                    }
                });
                // Thêm children vào ngay sau startItem
                this._listDisplayItem.splice(startIndex + 1, 0, ...children);
                // Tạo stack để duyệt cây con
                let stack = [...children];
                while (stack.length > 0) {
                    const currentItem = stack.shift();
                    if (currentItem?.expand) {
                        const grandChildren = this._newTreeViewNode.filter(item => item.parent?.objectId === currentItem.objectId);
                        grandChildren.forEach(gc => {
                            gc.level = (currentItem.level || 0) + 1;
                        });

                        // Thêm grandChildren vào sau currentItem
                        const currentIndex = this._listDisplayItem.findIndex(item => item.objectId === currentItem.objectId);
                        this._listDisplayItem.splice(currentIndex + 1, 0, ...grandChildren);

                        // Thêm vào stack để duyệt tiếp nếu expand
                        stack.unshift(...grandChildren);
                    }
                }
            }
        }


    }

    //#region _handleRemoveDisplayItem
    handleRemoveDisplayItem = (parentItem: NodeModel) => {
        // Tìm tất cả con của parentItem trong treeLayerModels
        const children = this._newTreeViewNode.filter(item => item.parent?.objectId === parentItem.objectId);

        children.forEach(child => {
            // Xóa thằng con trong listDisplayItem nếu có
            const index = this._listDisplayItem.findIndex(item => item.objectId === child.objectId);
            if (index !== -1) {
                this._listDisplayItem.splice(index, 1);
            }

            // Đệ quy tiếp tục với từng thằng con
            this.handleRemoveDisplayItem(child);
        });
    }

    mapNewList = () => {
        this._treeViewNodes = (this._treeViewPlugin as any)._nodeNodes;

        // map từng value trong object sang NodeModel
        this._newTreeViewNode = Object.values(this._treeViewNodes || [])

        this._newTreeViewNode.forEach((item) => {
            item.expand = false;
            item.level = 0;
        })

        this.handleDisplayItem()
        this.handleRefreshList()

        console.log("New TreeViewNode list:", this._newTreeViewNode);

    }


    //#region _handleRefreshList 
    handleRefreshList = () => {
        if (this._containerBodyTree && !this._list) {
            // const height = this.containerUl?.getBoundingClientRect().height || window.innerHeight;
            this._config.total = this._newTreeViewNode.length
            this._list = new HyperList(this._containerBodyTree!, this._config)
        }


        this._config.total = this._listDisplayItem.length
        this._list?.refresh(this._containerBodyTree!, this._config)
    }

    private _treeViewNodes: Map<string, TreeViewNode> | undefined;



}
