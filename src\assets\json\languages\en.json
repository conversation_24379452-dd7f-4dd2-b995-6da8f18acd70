{"model-title-dialog": "Models", "model-title-option-tileset": "Model options", "layer-title-dialog": "Layer models", "check-coordinate-system": "Coordinate system", "check-coordinate-geographic": "Geographic coordinate", "your-location": "You are here", "map-title-dialog": "Map", "map-terrain": "Terrain", "map-group-transparent": "Terrain transparent", "map-through-group": "Through terrain", "weather-shadow": "Shadow", "hours": "hours", "label-properties-title-dialog": "Label Properties", "label-properties-search": "Search", "label-properties-dialog-header": "Label", "label-properties-dialog-header-create-folder": "Create folder", "label-properties-dialog-header-edit-folder": "Edit folder", "label-properties-dialog-header-create-label": "Create Label", "label-properties-dialog-header-edit-label": "Edit Label", "label-properties-dialog-header-edit-root-label": "Edit Root Label", "label-properties-enter-name": "Enter name", "label-properties-enter-name-placeholder": "Name", "label-properties-enter-url": "Enter Url", "label-properties-check-folder": "Folder", "label-properties-enter-url-placeholder": "Enter Url", "label-properties-enter-size-point": "Enter name size", "label-properties-color-point": "Select color", "label-point-edit": "Edit label", "label-point-download-json": "Download Json", "label-point-upload-json": "Upload <PERSON><PERSON>", "label-point-create-folder": "Create root folder label", "label-point-create-root": "Create root label", "label-point-create-element": "Create label", "label-point-cursor-remove": "Remove selected object", "label-point-show-all": "Show all objects", "label-point-hide-all": "Hide all objects", "label-point-remove-all": "Remove all objects", "label-point-refresh": "Refresh data", "label-point-save": "Save data", "element-properties-title-dialog": "Element Properties", "camera-choose-kmz": "Choose KMZ files", "camera-choose-kmz-place-holder": "Please select KMZ files", "camera-distance": "Total distance", "camera-time": "Time (s)", "camera-speed": "Speed", "camera-height": "Camera height away from initial point", "camera-view": "Camera view (angle)", "noData": "No data available", "height": "Height", "issue-point-size-point": "Size point", "issue-point-color-point": "Select color", "issue-point-tool-tip-create": "Please click on the screen", "issue-point-tool-tip-remove": "Please select the point you want to remove", "footer-angle": "Rotation angle", "footer-tilt": "Tilt angle", "footer-elevation": "Height", "footer-longitude": "Longitude", "footer-latitude": "Latitude", "tool-tip-model": "Layer", "tool-tip-layer": "Layer elements", "tool-tip-project-location": "Project location", "tool-tip-your-location": "Your location", "tool-tip-map": "Map", "tool-tip-measure": "Mearsure", "tool-tip-measure-action": "Measure distance", "tool-tip-measure-multi-action": "Measure multiple distances", "tool-tip-measure-delete-pick": "Remove 1 element", "tool-tip-measure-visible": "Show all element", "tool-tip-measure-hide": "Hide all element", "tool-tip-measure-delete-all": "Remove all element", "tool-tip-coordinates": "Coordinates", "tool-tip-coordinates-action": "Point by pick", "tool-tip-coordinates-delete-pick": "Remove 1 point", "tool-tip-coordinates-visible": "Show all point", "tool-tip-coordinates-hide": "Hide all point", "tool-tip-coordinates-delete-all": "Remove all point", "tool-tip-label-point": "Label properties", "tool-tip-data-properties": "Properties element", "tool-tip-camera-fly": "Fly camera along path", "tool-tip-camera-fly-fast": "Fly fast x1.2", "tool-tip-camera-fly-slow": "Fly slow x1.2", "tool-tip-camera-fly-start": "Start", "tool-tip-camera-fly-pause": "Pause", "tool-tip-camera-fly-visbile-path": "Show/Hide path", "tool-tip-note": "Note", "tool-tip-section-action": "Create section plane", "tool-tip-section-interact": "Interact", "tool-tip-section-pick": "Pick surface", "tool-tip-section-remove": "Remove section plane", "tool-tip-setting": "Settings", "dialog-service-success": "Success", "dialog-service-info": "Info", "dialog-service-warning": "Warning", "dialog-service-error": "Error", "dialog-service-open-link-header": "Open link?", "dialog-service-open-link-content": "Do you want to open this link?", "dialog-service-remove-select": "Are you sure you want to delete the $$$$ selected items?", "dialog-service-question-remove-header": "Are you sure?", "dialog-service-question-remove-content": "Do you want to delete this item?", "dialog-service-question-remove-all": "Do you want to delete all item?"}