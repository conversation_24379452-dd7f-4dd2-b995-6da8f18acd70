import { math } from "@xeokit/xeokit-sdk";

const rayOriginPrecision = 0.001;
const rayDirectionPrecision = 0.2 * math.DEGTORAD;

let alreadyPicked = [];
let recentPickEntity = null;
let referenceRay = null;

const sentinel = {};

function reset() {
    alreadyPicked.forEach(i => i.pickable = true);
    alreadyPicked = [];
    recentPickEntity = null;
    referenceRay = null;
}

function pick(scene, pickRay, { wrapAround = false, pickCloser = false } = {}) {
    const rayChanged =
        referenceRay &&
        (math.distVec3(pickRay.origin, referenceRay.origin) > rayOriginPrecision ||
            math.angleVec3(pickRay.direction, referenceRay.direction) > rayDirectionPrecision);

    if (!referenceRay || rayChanged) {
        referenceRay = { origin: pickRay.origin, direction: pickRay.direction };
    }

    if (rayChanged) {
        alreadyPicked.forEach(i => i.pickable = true);
        alreadyPicked = [];
    }

    if (pickCloser) {
        for (let i = 0; i < 2; ++i) {
            if (alreadyPicked.length > 0)
                alreadyPicked.pop().pickable = true;
        }
    }

    const innerPick = (resetAndRepeat) => {
        const pickResult = scene.pick({ origin: referenceRay.origin, direction: referenceRay.direction });
        const pickEntity = pickResult && pickResult.entity;

        if (pickEntity) {
            if (rayChanged && pickEntity === recentPickEntity && !pickCloser) {
                alreadyPicked.push(pickEntity);
                pickEntity.pickable = false;
                return innerPick(true);
            } else {
                alreadyPicked.push(pickEntity);
                pickEntity.pickable = false;
                recentPickEntity = pickEntity;
                return pickResult;
            }
        } else if (wrapAround && resetAndRepeat && alreadyPicked.length > 0) {
            alreadyPicked.forEach(i => i.pickable = true);
            alreadyPicked = [];
            return innerPick(false);
        } else {
            if (alreadyPicked.length > 0 && alreadyPicked[alreadyPicked.length - 1] !== sentinel) {
                alreadyPicked.push(sentinel);
            }
            recentPickEntity = null;
            return null;
        }
    };

    return innerPick(true);
}

export const overlappingPick = {
    pick,
    reset
};
