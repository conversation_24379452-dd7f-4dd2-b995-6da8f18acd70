// Type definitions for math utilities
type FloatArrayConstructor = Float32ArrayConstructor | Float64ArrayConstructor;
type FloatArray = Float32Array | Float64Array;

// Vector types
type Vec2 = FloatArray;
type Vec3 = FloatArray;
type Vec4 = FloatArray;

// Matrix types
type Mat3 = FloatArray;
type Mat4 = FloatArray;

// Some temporary vars to help avoid garbage collection
let doublePrecision: boolean = true;
let FloatArrayType: FloatArrayConstructor = doublePrecision ? Float64Array : Float32Array;

const tempVec3a: Vec3 = new FloatArrayType(3);
const tempMat1: Mat4 = new FloatArrayType(16);
const tempMat2: Mat4 = new FloatArrayType(16);
const tempVec4: Vec4 = new FloatArrayType(4);

/**
 * Math utilities for 3D graphics operations
 */
export const math = {
    /**
     * Enable or disable double precision
     */
    setDoublePrecisionEnabled(enable: boolean): void {
        doublePrecision = enable;
        FloatArrayType = doublePrecision ? Float64Array : Float32Array;
    },

    /**
     * Get current double precision setting
     */
    getDoublePrecisionEnabled(): boolean {
        return doublePrecision;
    },

    MIN_DOUBLE: -Number.MAX_SAFE_INTEGER,
    MAX_DOUBLE: Number.MAX_SAFE_INTEGER,
    MAX_INT: 10000000,

    /**
     * The number of radians in a degree (0.0174532925).
     */
    DEGTORAD: 0.0174532925,

    /**
     * The number of degrees in a radian.
     */
    RADTODEG: 57.295779513,

    /**
     * Unglobalize object ID
     */
    unglobalizeObjectId(modelId: string, globalId: string): string {
        const idx = globalId.indexOf("#");
        return (idx === modelId.length && globalId.startsWith(modelId)) ? globalId.substring(idx + 1) : globalId;
    },

    /**
     * Globalize object ID
     */
    globalizeObjectId(modelId: string, objectId: string): string {
        return (modelId + "#" + objectId);
    },

    /**
     * Returns:
     * - x != 0 => 1/x,
     * - x == 1 => 1
     */
    safeInv(x: number): number {
        const retVal = 1 / x;
        if (isNaN(retVal) || !isFinite(retVal)) {
            return 1;
        }
        return retVal;
    },

    /**
     * Returns a new, uninitialized two-element vector.
     */
    vec2(values?: number[]): Vec2 {
        return values ? new FloatArrayType(values) : new FloatArrayType(2);
    },

    /**
     * Returns a new, uninitialized three-element vector.
     */
    vec3(values?: number[]): Vec3 {
        return values ? new FloatArrayType(values) : new FloatArrayType(3);
    },

    /**
     * Returns a new, uninitialized four-element vector.
     */
    vec4(values?: number[]): Vec4 {
        return values ? new FloatArrayType(values) : new FloatArrayType(4);
    },

    /**
     * Returns a new, uninitialized 3x3 matrix.
     */
    mat3(values?: number[]): Mat3 {
        return values ? new FloatArrayType(values) : new FloatArrayType(9);
    },

    /**
     * Converts a 3x3 matrix to 4x4
     */
    mat3ToMat4(mat3: Mat3, mat4: Mat4 = new FloatArrayType(16)): Mat4 {
        mat4[0] = mat3[0];
        mat4[1] = mat3[1];
        mat4[2] = mat3[2];
        mat4[3] = 0;
        mat4[4] = mat3[3];
        mat4[5] = mat3[4];
        mat4[6] = mat3[5];
        mat4[7] = 0;
        mat4[8] = mat3[6];
        mat4[9] = mat3[7];
        mat4[10] = mat3[8];
        mat4[11] = 0;
        mat4[12] = 0;
        mat4[13] = 0;
        mat4[14] = 0;
        mat4[15] = 1;
        return mat4;
    },

    /**
     * Returns a new, uninitialized 4x4 matrix.
     */
    mat4(values?: number[]): Mat4 {
        return values ? new FloatArrayType(values) : new FloatArrayType(16);
    },

    /**
     * Converts a 4x4 matrix to 3x3
     */
    mat4ToMat3(mat4: Mat4, mat3: Mat3 = new FloatArrayType(9)): Mat3 {
        mat3[0] = mat4[0];
        mat3[1] = mat4[1];
        mat3[2] = mat4[2];
        
        mat3[3] = mat4[4];
        mat3[4] = mat4[5];
        mat3[5] = mat4[6];
        
        mat3[6] = mat4[8];
        mat3[7] = mat4[9];
        mat3[8] = mat4[10];
        
        return mat3;
    },

    /**
     * Converts a list of double-precision values to a list of high-part floats and a list of low-part floats.
     */
    doublesToFloats(doubleVals: number[], floatValsHigh: FloatArray, floatValsLow: FloatArray): void {
        const floatPair = new FloatArrayType(2);
        for (let i = 0, len = doubleVals.length; i < len; i++) {
            math.splitDouble(doubleVals[i], floatPair);
            floatValsHigh[i] = floatPair[0];
            floatValsLow[i] = floatPair[1];
        }
    },

    /**
     * Splits a double value into two floats.
     */
    splitDouble(value: number, floatPair: FloatArray): void {
        const hi = FloatArrayType.from([value])[0];
        const low = value - hi;
        floatPair[0] = hi;
        floatPair[1] = low;
    },

    /**
     * Returns a new UUID.
     */
    createUUID: ((): () => string => {
        const lut: string[] = [];
        for (let i = 0; i < 256; i++) {
            lut[i] = (i < 16 ? '0' : '') + (i).toString(16);
        }
        return (): string => {
            const d0 = Math.random() * 0xffffffff | 0;
            const d1 = Math.random() * 0xffffffff | 0;
            const d2 = Math.random() * 0xffffffff | 0;
            const d3 = Math.random() * 0xffffffff | 0;
            return `${lut[d0 & 0xff] + lut[d0 >> 8 & 0xff] + lut[d0 >> 16 & 0xff] + lut[d0 >> 24 & 0xff]}-${lut[d1 & 0xff]}${lut[d1 >> 8 & 0xff]}-${lut[d1 >> 16 & 0x0f | 0x40]}${lut[d1 >> 24 & 0xff]}-${lut[d2 & 0x3f | 0x80]}${lut[d2 >> 8 & 0xff]}-${lut[d2 >> 16 & 0xff]}${lut[d2 >> 24 & 0xff]}${lut[d3 & 0xff]}${lut[d3 >> 8 & 0xff]}${lut[d3 >> 16 & 0xff]}${lut[d3 >> 24 & 0xff]}`;
        };
    })(),

    /**
     * Clamps a value to the given range.
     */
    clamp(value: number, min: number, max: number): number {
        return Math.max(min, Math.min(max, value));
    },

    /**
     * Floating-point modulus
     */
    fmod(a: number, b: number): number {
        if (a < b) {
            console.error("math.fmod : Attempting to find modulus within negative range - would be infinite loop - ignoring");
            return a;
        }
        while (b <= a) {
            a -= b;
        }
        return a;
    },

    /**
     * Returns true if the two 3-element vectors are the same.
     */
    compareVec3(v1: Vec3, v2: Vec3): boolean {
        return (v1[0] === v2[0] && v1[1] === v2[1] && v1[2] === v2[2]);
    },

    /**
     * Returns true if the two 4-element vectors are the same.
     */
    compareVec4(v1: Vec4, v2: Vec4): boolean {
        return (v1[0] === v2[0] && v1[1] === v2[1] && v1[2] === v2[2] && v1[3] === v2[3]);
    },

    /**
     * Negates a three-element vector.
     */
    negateVec3(v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = v;
        }
        dest[0] = -v[0];
        dest[1] = -v[1];
        dest[2] = -v[2];
        return dest;
    },

    /**
     * Negates a four-element vector.
     */
    negateVec4(v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = -v[0];
        dest[1] = -v[1];
        dest[2] = -v[2];
        dest[3] = -v[3];
        return dest;
    },

    /**
     * Adds one four-element vector to another.
     */
    addVec4(u: Vec4, v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] + v[0];
        dest[1] = u[1] + v[1];
        dest[2] = u[2] + v[2];
        dest[3] = u[3] + v[3];
        return dest;
    },

    /**
     * Adds a scalar value to each element of a four-element vector.
     */
    addVec4Scalar(v: Vec4, s: number, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] + s;
        dest[1] = v[1] + s;
        dest[2] = v[2] + s;
        dest[3] = v[3] + s;
        return dest;
    },

    /**
     * Adds one three-element vector to another.
     */
    addVec3(u: Vec3, v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] + v[0];
        dest[1] = u[1] + v[1];
        dest[2] = u[2] + v[2];
        return dest;
    },

    /**
     * Adds a scalar value to each element of a three-element vector.
     */
    addVec3Scalar(v: Vec3, s: number, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] + s;
        dest[1] = v[1] + s;
        dest[2] = v[2] + s;
        return dest;
    },

    /**
     * Subtracts one four-element vector from another.
     */
    subVec4(u: Vec4, v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] - v[0];
        dest[1] = u[1] - v[1];
        dest[2] = u[2] - v[2];
        dest[3] = u[3] - v[3];
        return dest;
    },

    /**
     * Subtracts one three-element vector from another.
     */
    subVec3(u: Vec3, v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] - v[0];
        dest[1] = u[1] - v[1];
        dest[2] = u[2] - v[2];
        return dest;
    },

    /**
     * Subtracts one two-element vector from another.
     */
    subVec2(u: Vec2, v: Vec2, dest?: Vec2): Vec2 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] - v[0];
        dest[1] = u[1] - v[1];
        return dest;
    },

    /**
     * Get the geometric mean of the vectors.
     */
    geometricMeanVec2(...vectors: Vec2[]): Vec2 {
        const geometricMean = new FloatArrayType(vectors[0]);
        for (let i = 1; i < vectors.length; i++) {
            geometricMean[0] += vectors[i][0];
            geometricMean[1] += vectors[i][1];
        }
        geometricMean[0] /= vectors.length;
        geometricMean[1] /= vectors.length;
        return geometricMean;
    },

    /**
     * Subtracts a scalar value from each element of a four-element vector.
     */
    subVec4Scalar(v: Vec4, s: number, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] - s;
        dest[1] = v[1] - s;
        dest[2] = v[2] - s;
        dest[3] = v[3] - s;
        return dest;
    },

    /**
     * Sets each element of a 4-element vector to a scalar value minus the value of that element.
     */
    subScalarVec4(v: Vec4, s: number, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = s - v[0];
        dest[1] = s - v[1];
        dest[2] = s - v[2];
        dest[3] = s - v[3];
        return dest;
    },

    /**
     * Multiplies one four-element vector by another.
     */
    mulVec4(u: Vec4, v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] * v[0];
        dest[1] = u[1] * v[1];
        dest[2] = u[2] * v[2];
        dest[3] = u[3] * v[3];
        return dest;
    },

    /**
     * Multiplies each element of a four-element vector by a scalar.
     */
    mulVec4Scalar(v: Vec4, s: number, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] * s;
        dest[1] = v[1] * s;
        dest[2] = v[2] * s;
        dest[3] = v[3] * s;
        return dest;
    },

    /**
     * Multiplies each element of a three-element vector by a scalar.
     */
    mulVec3Scalar(v: Vec3, s: number, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] * s;
        dest[1] = v[1] * s;
        dest[2] = v[2] * s;
        return dest;
    },

    /**
     * Multiplies each element of a two-element vector by a scalar.
     */
    mulVec2Scalar(v: Vec2, s: number, dest?: Vec2): Vec2 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] * s;
        dest[1] = v[1] * s;
        return dest;
    },

    /**
     * Divides one three-element vector by another.
     */
    divVec3(u: Vec3, v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] / v[0];
        dest[1] = u[1] / v[1];
        dest[2] = u[2] / v[2];
        return dest;
    },

    /**
     * Divides one four-element vector by another.
     */
    divVec4(u: Vec4, v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = u;
        }
        dest[0] = u[0] / v[0];
        dest[1] = u[1] / v[1];
        dest[2] = u[2] / v[2];
        dest[3] = u[3] / v[3];
        return dest;
    },

    /**
     * Divides a scalar by a three-element vector, returning a new vector.
     */
    divScalarVec3(s: number, v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = v;
        }
        dest[0] = s / v[0];
        dest[1] = s / v[1];
        dest[2] = s / v[2];
        return dest;
    },

    /**
     * Divides a three-element vector by a scalar.
     */
    divVec3Scalar(v: Vec3, s: number, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] / s;
        dest[1] = v[1] / s;
        dest[2] = v[2] / s;
        return dest;
    },

    /**
     * Divides a four-element vector by a scalar.
     */
    divVec4Scalar(v: Vec4, s: number, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = v[0] / s;
        dest[1] = v[1] / s;
        dest[2] = v[2] / s;
        dest[3] = v[3] / s;
        return dest;
    },

    /**
     * Divides a scalar by a four-element vector, returning a new vector.
     */
    divScalarVec4(s: number, v: Vec4, dest?: Vec4): Vec4 {
        if (!dest) {
            dest = v;
        }
        dest[0] = s / v[0];
        dest[1] = s / v[1];
        dest[2] = s / v[2];
        dest[3] = s / v[3];
        return dest;
    },

    /**
     * Returns the dot product of two four-element vectors.
     */
    dotVec4(u: Vec4, v: Vec4): number {
        return (u[0] * v[0] + u[1] * v[1] + u[2] * v[2] + u[3] * v[3]);
    },

    /**
     * Returns the cross product of two four-element vectors.
     */
    cross3Vec4(u: Vec4, v: Vec4): number[] {
        const u0 = u[0];
        const u1 = u[1];
        const u2 = u[2];
        const v0 = v[0];
        const v1 = v[1];
        const v2 = v[2];
        return [
            u1 * v2 - u2 * v1,
            u2 * v0 - u0 * v2,
            u0 * v1 - u1 * v0,
            0.0];
    },

    /**
     * Returns the cross product of two three-element vectors.
     */
    cross3Vec3(u: Vec3, v: Vec3, dest?: Vec3): Vec3 {
        if (!dest) {
            dest = u;
        }
        const x = u[0];
        const y = u[1];
        const z = u[2];
        const x2 = v[0];
        const y2 = v[1];
        const z2 = v[2];
        dest[0] = y * z2 - z * y2;
        dest[1] = z * x2 - x * z2;
        dest[2] = x * y2 - y * x2;
        return dest;
    },

    /**
     * Returns the squared length of a four-element vector.
     */
    sqLenVec4(v: Vec4): number {
        return math.dotVec4(v, v);
    },

    /**
     * Returns the length of a four-element vector.
     */
    lenVec4(v: Vec4): number {
        return Math.sqrt(math.sqLenVec4(v));
    },

    /**
     * Returns the dot product of two three-element vectors.
     */
    dotVec3(u: Vec3, v: Vec3): number {
        return (u[0] * v[0] + u[1] * v[1] + u[2] * v[2]);
    },

    /**
     * Returns the dot product of two two-element vectors.
     */
    dotVec2(u: Vec2, v: Vec2): number {
        return (u[0] * v[0] + u[1] * v[1]);
    },

    /**
     * Returns the squared length of a three-element vector.
     */
    sqLenVec3(v: Vec3): number {
        return math.dotVec3(v, v);
    },

    /**
     * Returns the squared length of a two-element vector.
     */
    sqLenVec2(v: Vec2): number {
        return math.dotVec2(v, v);
    },

    /**
     * Returns the length of a three-element vector.
     */
    lenVec3(v: Vec3): number {
        return Math.sqrt(math.sqLenVec3(v));
    },

    /**
     * Returns the distance between two three-element vectors.
     */
    distVec3: ((): (v: Vec3, w: Vec3) => number => {
        const vec = new FloatArrayType(3);
        return (v: Vec3, w: Vec3): number => math.lenVec3(math.subVec3(v, w, vec));
    })(),

    /**
     * Returns the length of a two-element vector.
     */
    lenVec2(v: Vec2): number {
        return Math.sqrt(math.sqLenVec2(v));
    },

    /**
     * Returns the distance between two two-element vectors.
     */
    distVec2: ((): (v: Vec2, w: Vec2) => number => {
        const vec = new FloatArrayType(2);
        return (v: Vec2, w: Vec2): number => math.lenVec2(math.subVec2(v, w, vec));
    })(),

    /**
     * Returns the reciprocal of a three-element vector.
     */
    rcpVec3(v: Vec3, dest?: Vec3): Vec3 {
        return math.divScalarVec3(1.0, v, dest);
    },

    /**
     * Normalizes a four-element vector
     */
    normalizeVec4(v: Vec4, dest?: Vec4): Vec4 {
        const f = 1.0 / math.lenVec4(v);
        return math.mulVec4Scalar(v, f, dest);
    },

    /**
     * Normalizes a three-element vector
     */
    normalizeVec3(v: Vec3, dest?: Vec3): Vec3 {
        const f = 1.0 / math.lenVec3(v);
        return math.mulVec3Scalar(v, f, dest);
    },

    /**
     * Normalizes a two-element vector
     */
    normalizeVec2(v: Vec2, dest?: Vec2): Vec2 {
        const f = 1.0 / math.lenVec2(v);
        return math.mulVec2Scalar(v, f, dest);
    },

    /**
     * Gets the angle between two vectors
     */
    angleVec3(v: Vec3, w: Vec3): number {
        let theta = math.dotVec3(v, w) / (Math.sqrt(math.sqLenVec3(v) * math.sqLenVec3(w)));
        theta = theta < -1 ? -1 : (theta > 1 ? 1 : theta);  // Clamp to handle numerical problems
        return Math.acos(theta);
    },

    /**
     * Creates a three-element vector from the rotation part of a sixteen-element matrix.
     */
    vec3FromMat4Scale: ((): (m: Mat4, dest: Vec3) => Vec3 => {
        const tempVec3 = new FloatArrayType(3);
        return (m: Mat4, dest: Vec3): Vec3 => {
            tempVec3[0] = m[0];
            tempVec3[1] = m[1];
            tempVec3[2] = m[2];
            dest[0] = math.lenVec3(tempVec3);

            tempVec3[0] = m[4];
            tempVec3[1] = m[5];
            tempVec3[2] = m[6];
            dest[1] = math.lenVec3(tempVec3);

            tempVec3[0] = m[8];
            tempVec3[1] = m[9];
            tempVec3[2] = m[10];
            dest[2] = math.lenVec3(tempVec3);

            return dest;
        };
    })(),

    /**
     * Converts an n-element vector to a JSON-serializable
     * array with values rounded to two decimal places.
     */
    vecToArray: ((): (v: FloatArray) => number[] => {
        function trunc(v: number): number {
            return Math.round(v * 100000) / 100000;
        }
        return (v: FloatArray): number[] => {
            const result = Array.prototype.slice.call(v);
            for (let i = 0, len = result.length; i < len; i++) {
                result[i] = trunc(result[i]);
            }
            return result;
        };
    })(),

    /**
     * Converts a 3-element vector from an array to an object of the form {x:999, y:999, z:999}.
     */
    xyzArrayToObject(arr: Vec3): { x: number; y: number; z: number } {
        return { "x": arr[0], "y": arr[1], "z": arr[2] };
    },

    /**
     * Converts a 3-element vector object of the form {x:999, y:999, z:999} to an array.
     */
    xyzObjectToArray(xyz: { x: number; y: number; z: number }, arry?: Vec3): Vec3 {
        arry = arry || math.vec3();
        arry[0] = xyz.x;
        arry[1] = xyz.y;
        arry[2] = xyz.z;
        return arry;
    },

    /**
     * Duplicates a 4x4 matrix.
     */
    dupMat4(m: Mat4): Mat4 {
        return m.slice(0, 16) as Mat4;
    },

    /**
     * Extracts a 3x3 matrix from a 4x4 matrix.
     */
    mat4To3(m: Mat4): number[] {
        return [
            m[0], m[1], m[2],
            m[4], m[5], m[6],
            m[8], m[9], m[10]
        ];
    },

    /**
     * Returns a 4x4 matrix with each element set to the given scalar value.
     */
    m4s(s: number): number[] {
        return [
            s, s, s, s,
            s, s, s, s,
            s, s, s, s,
            s, s, s, s
        ];
    },

    /**
     * Returns a 4x4 matrix with each element set to zero.
     */
    setMat4ToZeroes(): number[] {
        return math.m4s(0.0);
    },

    /**
     * Returns a 4x4 matrix with each element set to 1.0.
     */
    setMat4ToOnes(): number[] {
        return math.m4s(1.0);
    },

    /**
     * Returns a 4x4 matrix with diagonal elements set to the given vector.
     */
    diagonalMat4v(v: Vec4): Mat4 {
        return new FloatArrayType([
            v[0], 0.0, 0.0, 0.0,
            0.0, v[1], 0.0, 0.0,
            0.0, 0.0, v[2], 0.0,
            0.0, 0.0, 0.0, v[3]
        ]);
    },

    /**
     * Returns a 4x4 matrix with diagonal elements set to the given values.
     */
    diagonalMat4c(x: number, y: number, z: number, w: number): Mat4 {
        const vec = new FloatArrayType([x, y, z, w]);
        return math.diagonalMat4v(vec);
    },

    /**
     * Returns a 4x4 matrix with diagonal elements set to the given scalar.
     */
    diagonalMat4s(s: number): Mat4 {
        return math.diagonalMat4c(s, s, s, s);
    },

    /**
     * Returns a 4x4 identity matrix.
     */
    identityMat4(mat: Mat4 = new FloatArrayType(16)): Mat4 {
        mat[0] = 1.0;
        mat[1] = 0.0;
        mat[2] = 0.0;
        mat[3] = 0.0;

        mat[4] = 0.0;
        mat[5] = 1.0;
        mat[6] = 0.0;
        mat[7] = 0.0;

        mat[8] = 0.0;
        mat[9] = 0.0;
        mat[10] = 1.0;
        mat[11] = 0.0;

        mat[12] = 0.0;
        mat[13] = 0.0;
        mat[14] = 0.0;
        mat[15] = 1.0;

        return mat;
    },

    /**
     * Returns a 3x3 identity matrix.
     */
    identityMat3(mat: Mat3 = new FloatArrayType(9)): Mat3 {
        mat[0] = 1.0;
        mat[1] = 0.0;
        mat[2] = 0.0;

        mat[3] = 0.0;
        mat[4] = 1.0;
        mat[5] = 0.0;

        mat[6] = 0.0;
        mat[7] = 0.0;
        mat[8] = 1.0;

        return mat;
    },

    /**
     * Tests if the given 4x4 matrix is the identity matrix.
     */
    isIdentityMat4(m: Mat4): boolean {
        if (m[0] !== 1.0 || m[1] !== 0.0 || m[2] !== 0.0 || m[3] !== 0.0 ||
            m[4] !== 0.0 || m[5] !== 1.0 || m[6] !== 0.0 || m[7] !== 0.0 ||
            m[8] !== 0.0 || m[9] !== 0.0 || m[10] !== 1.0 || m[11] !== 0.0 ||
            m[12] !== 0.0 || m[13] !== 0.0 || m[14] !== 0.0 || m[15] !== 1.0) {
            return false;
        }
        return true;
    },

    /**
     * Negates the given 4x4 matrix.
     */
    negateMat4(m: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = m;
        }
        dest[0] = -m[0];
        dest[1] = -m[1];
        dest[2] = -m[2];
        dest[3] = -m[3];
        dest[4] = -m[4];
        dest[5] = -m[5];
        dest[6] = -m[6];
        dest[7] = -m[7];
        dest[8] = -m[8];
        dest[9] = -m[9];
        dest[10] = -m[10];
        dest[11] = -m[11];
        dest[12] = -m[12];
        dest[13] = -m[13];
        dest[14] = -m[14];
        dest[15] = -m[15];
        return dest;
    },

    /**
     * Adds the given 4x4 matrices together.
     */
    addMat4(a: Mat4, b: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = a;
        }
        dest[0] = a[0] + b[0];
        dest[1] = a[1] + b[1];
        dest[2] = a[2] + b[2];
        dest[3] = a[3] + b[3];
        dest[4] = a[4] + b[4];
        dest[5] = a[5] + b[5];
        dest[6] = a[6] + b[6];
        dest[7] = a[7] + b[7];
        dest[8] = a[8] + b[8];
        dest[9] = a[9] + b[9];
        dest[10] = a[10] + b[10];
        dest[11] = a[11] + b[11];
        dest[12] = a[12] + b[12];
        dest[13] = a[13] + b[13];
        dest[14] = a[14] + b[14];
        dest[15] = a[15] + b[15];
        return dest;
    },

    /**
     * Adds the given scalar to each element of the given 4x4 matrix.
     */
    addMat4Scalar(m: Mat4, s: number, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = m;
        }
        dest[0] = m[0] + s;
        dest[1] = m[1] + s;
        dest[2] = m[2] + s;
        dest[3] = m[3] + s;
        dest[4] = m[4] + s;
        dest[5] = m[5] + s;
        dest[6] = m[6] + s;
        dest[7] = m[7] + s;
        dest[8] = m[8] + s;
        dest[9] = m[9] + s;
        dest[10] = m[10] + s;
        dest[11] = m[11] + s;
        dest[12] = m[12] + s;
        dest[13] = m[13] + s;
        dest[14] = m[14] + s;
        dest[15] = m[15] + s;
        return dest;
    },

    /**
     * Adds the given scalar to each element of the given 4x4 matrix.
     */
    addScalarMat4(s: number, m: Mat4, dest?: Mat4): Mat4 {
        return math.addMat4Scalar(m, s, dest);
    },

    /**
     * Subtracts the second 4x4 matrix from the first.
     */
    subMat4(a: Mat4, b: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = a;
        }
        dest[0] = a[0] - b[0];
        dest[1] = a[1] - b[1];
        dest[2] = a[2] - b[2];
        dest[3] = a[3] - b[3];
        dest[4] = a[4] - b[4];
        dest[5] = a[5] - b[5];
        dest[6] = a[6] - b[6];
        dest[7] = a[7] - b[7];
        dest[8] = a[8] - b[8];
        dest[9] = a[9] - b[9];
        dest[10] = a[10] - b[10];
        dest[11] = a[11] - b[11];
        dest[12] = a[12] - b[12];
        dest[13] = a[13] - b[13];
        dest[14] = a[14] - b[14];
        dest[15] = a[15] - b[15];
        return dest;
    },

    /**
     * Subtracts the given scalar from each element of the given 4x4 matrix.
     */
    subMat4Scalar(m: Mat4, s: number, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = m;
        }
        dest[0] = m[0] - s;
        dest[1] = m[1] - s;
        dest[2] = m[2] - s;
        dest[3] = m[3] - s;
        dest[4] = m[4] - s;
        dest[5] = m[5] - s;
        dest[6] = m[6] - s;
        dest[7] = m[7] - s;
        dest[8] = m[8] - s;
        dest[9] = m[9] - s;
        dest[10] = m[10] - s;
        dest[11] = m[11] - s;
        dest[12] = m[12] - s;
        dest[13] = m[13] - s;
        dest[14] = m[14] - s;
        dest[15] = m[15] - s;
        return dest;
    },

    /**
     * Subtracts each element of the given 4x4 matrix from the given scalar.
     */
    subScalarMat4(s: number, m: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = m;
        }
        dest[0] = s - m[0];
        dest[1] = s - m[1];
        dest[2] = s - m[2];
        dest[3] = s - m[3];
        dest[4] = s - m[4];
        dest[5] = s - m[5];
        dest[6] = s - m[6];
        dest[7] = s - m[7];
        dest[8] = s - m[8];
        dest[9] = s - m[9];
        dest[10] = s - m[10];
        dest[11] = s - m[11];
        dest[12] = s - m[12];
        dest[13] = s - m[13];
        dest[14] = s - m[14];
        dest[15] = s - m[15];
        return dest;
    },

    /**
     * Multiplies the two given 4x4 matrices by each other.
     */
    mulMat4(a: Mat4, b: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = a;
        }

        // Cache the matrix values (makes for huge speed increases!)
        const a00 = a[0];
        const a01 = a[1];
        const a02 = a[2];
        const a03 = a[3];
        const a10 = a[4];
        const a11 = a[5];
        const a12 = a[6];
        const a13 = a[7];
        const a20 = a[8];
        const a21 = a[9];
        const a22 = a[10];
        const a23 = a[11];
        const a30 = a[12];
        const a31 = a[13];
        const a32 = a[14];
        const a33 = a[15];
        const b00 = b[0];
        const b01 = b[1];
        const b02 = b[2];
        const b03 = b[3];
        const b10 = b[4];
        const b11 = b[5];
        const b12 = b[6];
        const b13 = b[7];
        const b20 = b[8];
        const b21 = b[9];
        const b22 = b[10];
        const b23 = b[11];
        const b30 = b[12];
        const b31 = b[13];
        const b32 = b[14];
        const b33 = b[15];

        dest[0] = b00 * a00 + b01 * a10 + b02 * a20 + b03 * a30;
        dest[1] = b00 * a01 + b01 * a11 + b02 * a21 + b03 * a31;
        dest[2] = b00 * a02 + b01 * a12 + b02 * a22 + b03 * a32;
        dest[3] = b00 * a03 + b01 * a13 + b02 * a23 + b03 * a33;
        dest[4] = b10 * a00 + b11 * a10 + b12 * a20 + b13 * a30;
        dest[5] = b10 * a01 + b11 * a11 + b12 * a21 + b13 * a31;
        dest[6] = b10 * a02 + b11 * a12 + b12 * a22 + b13 * a32;
        dest[7] = b10 * a03 + b11 * a13 + b12 * a23 + b13 * a33;
        dest[8] = b20 * a00 + b21 * a10 + b22 * a20 + b23 * a30;
        dest[9] = b20 * a01 + b21 * a11 + b22 * a21 + b23 * a31;
        dest[10] = b20 * a02 + b21 * a12 + b22 * a22 + b23 * a32;
        dest[11] = b20 * a03 + b21 * a13 + b22 * a23 + b23 * a33;
        dest[12] = b30 * a00 + b31 * a10 + b32 * a20 + b33 * a30;
        dest[13] = b30 * a01 + b31 * a11 + b32 * a21 + b33 * a31;
        dest[14] = b30 * a02 + b31 * a12 + b32 * a22 + b33 * a32;
        dest[15] = b30 * a03 + b31 * a13 + b32 * a23 + b33 * a33;

        return dest;
    },

    /**
     * Multiplies the two given 3x3 matrices by each other.
     */
    mulMat3(a: Mat3, b: Mat3, dest?: Mat3): Mat3 {
        if (!dest) {
            dest = new FloatArrayType(9);
        }

        const a11 = a[0];
        const a12 = a[3];
        const a13 = a[6];
        const a21 = a[1];
        const a22 = a[4];
        const a23 = a[7];
        const a31 = a[2];
        const a32 = a[5];
        const a33 = a[8];
        const b11 = b[0];
        const b12 = b[3];
        const b13 = b[6];
        const b21 = b[1];
        const b22 = b[4];
        const b23 = b[7];
        const b31 = b[2];
        const b32 = b[5];
        const b33 = b[8];

        dest[0] = a11 * b11 + a12 * b21 + a13 * b31;
        dest[3] = a11 * b12 + a12 * b22 + a13 * b32;
        dest[6] = a11 * b13 + a12 * b23 + a13 * b33;

        dest[1] = a21 * b11 + a22 * b21 + a23 * b31;
        dest[4] = a21 * b12 + a22 * b22 + a23 * b32;
        dest[7] = a21 * b13 + a22 * b23 + a23 * b33;

        dest[2] = a31 * b11 + a32 * b21 + a33 * b31;
        dest[5] = a31 * b12 + a32 * b22 + a33 * b32;
        dest[8] = a31 * b13 + a32 * b23 + a33 * b33;

        return dest;
    },

    /**
     * Multiplies each element of the given 4x4 matrix by the given scalar.
     */
    mulMat4Scalar(m: Mat4, s: number, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = m;
        }
        dest[0] = m[0] * s;
        dest[1] = m[1] * s;
        dest[2] = m[2] * s;
        dest[3] = m[3] * s;
        dest[4] = m[4] * s;
        dest[5] = m[5] * s;
        dest[6] = m[6] * s;
        dest[7] = m[7] * s;
        dest[8] = m[8] * s;
        dest[9] = m[9] * s;
        dest[10] = m[10] * s;
        dest[11] = m[11] * s;
        dest[12] = m[12] * s;
        dest[13] = m[13] * s;
        dest[14] = m[14] * s;
        dest[15] = m[15] * s;
        return dest;
    },

    /**
     * Multiplies the given 4x4 matrix by the given four-element vector.
     */
    mulMat4v4(m: Mat4, v: Vec4, dest: Vec4 = math.vec4()): Vec4 {
        const v0 = v[0];
        const v1 = v[1];
        const v2 = v[2];
        const v3 = v[3];
        dest[0] = m[0] * v0 + m[4] * v1 + m[8] * v2 + m[12] * v3;
        dest[1] = m[1] * v0 + m[5] * v1 + m[9] * v2 + m[13] * v3;
        dest[2] = m[2] * v0 + m[6] * v1 + m[10] * v2 + m[14] * v3;
        dest[3] = m[3] * v0 + m[7] * v1 + m[11] * v2 + m[15] * v3;
        return dest;
    },

    /**
     * Transforms a four-element vector by a 4x4 matrix.
     */
    transformVec4(m: Mat4, v: Vec4, dest?: Vec4): Vec4 {
        const v0 = v[0];
        const v1 = v[1];
        const v2 = v[2];
        const v3 = v[3];
        dest = dest || math.vec4();
        dest[0] = m[0] * v0 + m[4] * v1 + m[8] * v2 + m[12] * v3;
        dest[1] = m[1] * v0 + m[5] * v1 + m[9] * v2 + m[13] * v3;
        dest[2] = m[2] * v0 + m[6] * v1 + m[10] * v2 + m[14] * v3;
        dest[3] = m[3] * v0 + m[7] * v1 + m[11] * v2 + m[15] * v3;
        return dest;
    },

    /**
     * Transposes the given 4x4 matrix.
     */
    transposeMat4(mat: Mat4, dest?: Mat4): Mat4 {
        // If we are transposing ourselves we can skip a few steps but have to cache some values
        const m4 = mat[4];
        const m14 = mat[14];
        const m8 = mat[8];
        const m13 = mat[13];
        const m12 = mat[12];
        const m9 = mat[9];
        if (!dest || mat === dest) {
            const a01 = mat[1];
            const a02 = mat[2];
            const a03 = mat[3];
            const a12 = mat[6];
            const a13 = mat[7];
            const a23 = mat[11];
            mat[1] = m4;
            mat[2] = m8;
            mat[3] = m12;
            mat[4] = a01;
            mat[6] = m9;
            mat[7] = m13;
            mat[8] = a02;
            mat[9] = a12;
            mat[11] = m14;
            mat[12] = a03;
            mat[13] = a13;
            mat[14] = a23;
            return mat;
        }
        dest[0] = mat[0];
        dest[1] = m4;
        dest[2] = m8;
        dest[3] = m12;
        dest[4] = mat[1];
        dest[5] = mat[5];
        dest[6] = m9;
        dest[7] = m13;
        dest[8] = mat[2];
        dest[9] = mat[6];
        dest[10] = mat[10];
        dest[11] = m14;
        dest[12] = mat[3];
        dest[13] = mat[7];
        dest[14] = mat[11];
        dest[15] = mat[15];
        return dest;
    },

    /**
     * Transposes the given 3x3 matrix.
     */
    transposeMat3(mat: Mat3, dest?: Mat3): Mat3 {
        if (!dest) {
            dest = mat;
        }
        if (dest === mat) {
            const a01 = mat[1];
            const a02 = mat[2];
            const a12 = mat[5];
            dest[1] = mat[3];
            dest[2] = mat[6];
            dest[3] = a01;
            dest[5] = mat[7];
            dest[6] = a02;
            dest[7] = a12;
        } else {
            dest[0] = mat[0];
            dest[1] = mat[3];
            dest[2] = mat[6];
            dest[3] = mat[1];
            dest[4] = mat[4];
            dest[5] = mat[7];
            dest[6] = mat[2];
            dest[7] = mat[5];
            dest[8] = mat[8];
        }
        return dest;
    },

    /**
     * Returns the determinant of the given 4x4 matrix.
     */
    determinantMat4(mat: Mat4): number {
        // Cache the matrix values (makes for huge speed increases!)
        const a00 = mat[0];
        const a01 = mat[1];
        const a02 = mat[2];
        const a03 = mat[3];
        const a10 = mat[4];
        const a11 = mat[5];
        const a12 = mat[6];
        const a13 = mat[7];
        const a20 = mat[8];
        const a21 = mat[9];
        const a22 = mat[10];
        const a23 = mat[11];
        const a30 = mat[12];
        const a31 = mat[13];
        const a32 = mat[14];
        const a33 = mat[15];
        return a30 * a21 * a12 * a03 - a20 * a31 * a12 * a03 - a30 * a11 * a22 * a03 + a10 * a31 * a22 * a03 +
            a20 * a11 * a32 * a03 - a10 * a21 * a32 * a03 - a30 * a21 * a02 * a13 + a20 * a31 * a02 * a13 +
            a30 * a01 * a22 * a13 - a00 * a31 * a22 * a13 - a20 * a01 * a32 * a13 + a00 * a21 * a32 * a13 +
            a30 * a11 * a02 * a23 - a10 * a31 * a02 * a23 - a30 * a01 * a12 * a23 + a00 * a31 * a12 * a23 +
            a10 * a01 * a32 * a23 - a00 * a11 * a32 * a23 - a20 * a11 * a02 * a33 + a10 * a21 * a02 * a33 +
            a20 * a01 * a12 * a33 - a00 * a21 * a12 * a33 - a10 * a01 * a22 * a33 + a00 * a11 * a22 * a33;
    },

    /**
     * Returns the inverse of the given 4x4 matrix.
     */
    inverseMat4(mat: Mat4, dest?: Mat4): Mat4 {
        if (!dest) {
            dest = mat;
        }

        // Cache the matrix values (makes for huge speed increases!)
        const a00 = mat[0];
        const a01 = mat[1];
        const a02 = mat[2];
        const a03 = mat[3];
        const a10 = mat[4];
        const a11 = mat[5];
        const a12 = mat[6];
        const a13 = mat[7];
        const a20 = mat[8];
        const a21 = mat[9];
        const a22 = mat[10];
        const a23 = mat[11];
        const a30 = mat[12];
        const a31 = mat[13];
        const a32 = mat[14];
        const a33 = mat[15];
        const b00 = a00 * a11 - a01 * a10;
        const b01 = a00 * a12 - a02 * a10;
        const b02 = a00 * a13 - a03 * a10;
        const b03 = a01 * a12 - a02 * a11;
        const b04 = a01 * a13 - a03 * a11;
        const b05 = a02 * a13 - a03 * a12;
        const b06 = a20 * a31 - a21 * a30;
        const b07 = a20 * a32 - a22 * a30;
        const b08 = a20 * a33 - a23 * a30;
        const b09 = a21 * a32 - a22 * a31;
        const b10 = a21 * a33 - a23 * a31;
        const b11 = a22 * a33 - a23 * a32;

        // Calculate the determinant (inlined to avoid double-caching)
        const invDet = 1 / (b00 * b11 - b01 * b10 + b02 * b09 + b03 * b08 - b04 * b07 + b05 * b06);

        dest[0] = (a11 * b11 - a12 * b10 + a13 * b09) * invDet;
        dest[1] = (-a01 * b11 + a02 * b10 - a03 * b09) * invDet;
        dest[2] = (a31 * b05 - a32 * b04 + a33 * b03) * invDet;
        dest[3] = (-a21 * b05 + a22 * b04 - a23 * b03) * invDet;
        dest[4] = (-a10 * b11 + a12 * b08 - a13 * b07) * invDet;
        dest[5] = (a00 * b11 - a02 * b08 + a03 * b07) * invDet;
        dest[6] = (-a30 * b05 + a32 * b02 - a33 * b01) * invDet;
        dest[7] = (a20 * b05 - a22 * b02 + a23 * b01) * invDet;
        dest[8] = (a10 * b10 - a11 * b08 + a13 * b06) * invDet;
        dest[9] = (-a00 * b10 + a01 * b08 - a03 * b06) * invDet;
        dest[10] = (a30 * b04 - a31 * b02 + a33 * b00) * invDet;
        dest[11] = (-a20 * b04 + a21 * b02 - a23 * b00) * invDet;
        dest[12] = (-a10 * b09 + a11 * b07 - a12 * b06) * invDet;
        dest[13] = (a00 * b09 - a01 * b07 + a02 * b06) * invDet;
        dest[14] = (-a30 * b03 + a31 * b01 - a32 * b00) * invDet;
        dest[15] = (a20 * b03 - a21 * b01 + a22 * b00) * invDet;

        return dest;
    },

    /**
     * Returns the trace of the given 4x4 matrix.
     */
    traceMat4(m: Mat4): number {
        return (m[0] + m[5] + m[10] + m[15]);
    },

    /**
     * Transforms a Canvas-space position into a World-space ray, in the context of a Camera.
     */
    canvasPosToWorldRay: ((): (
        canvas: HTMLCanvasElement,
        viewMatrix: Mat4,
        projMatrix: Mat4,
        projection: string,
        canvasPos: number[] | Vec2,
        worldRayOrigin: Vec3,
        worldRayDir: Vec3
    ) => void => {
        const pvMatInv = new FloatArrayType(16);
        const vec4Near = new FloatArrayType(4);
        const vec4Far = new FloatArrayType(4);

        const clipToWorld = (clipX: number, clipY: number, clipZ: number, isOrtho: boolean, outVec4: Vec4): void => {
            outVec4[0] = clipX;
            outVec4[1] = clipY;
            outVec4[2] = clipZ;
            outVec4[3] = 1;

            math.transformVec4(pvMatInv, outVec4, outVec4);
            if (!isOrtho) {
                math.mulVec4Scalar(outVec4, 1 / outVec4[3]);
            }
        };

        return (
            canvas: HTMLCanvasElement,
            viewMatrix: Mat4,
            projMatrix: Mat4,
            projection: string,
            canvasPos: number[] | Vec2,
            worldRayOrigin: Vec3,
            worldRayDir: Vec3
        ): void => {
            const isOrtho = projection === "ortho";

            math.mulMat4(projMatrix, viewMatrix, pvMatInv);
            math.inverseMat4(pvMatInv, pvMatInv);

            // Calculate clip space coordinates, which will be in range
            // of x=[-1..1] and y=[-1..1], with y=(+1) at top
            // clientWidth/Height needs to be used in case canvas.width/height is scaled down,
            // but not reflecting client dimensions (e.g. when using FastNavPlugin)
            const clipX = 2 * canvasPos[0] / canvas.clientWidth - 1;  // Calculate clip space coordinates
            const clipY = 1 - 2 * canvasPos[1] / canvas.clientHeight;

            clipToWorld(clipX, clipY, -1, isOrtho, vec4Near);
            clipToWorld(clipX, clipY, 1, isOrtho, vec4Far);

            worldRayOrigin[0] = vec4Near[0];
            worldRayOrigin[1] = vec4Near[1];
            worldRayOrigin[2] = vec4Near[2];

            math.subVec3(vec4Far as Vec3, vec4Near as Vec3, worldRayDir);
            math.normalizeVec3(worldRayDir);
        };
    })(),
};

export default math;
