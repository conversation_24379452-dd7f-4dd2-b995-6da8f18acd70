'use strict';

// Default configuration.
const defaultConfig = {
  width: '100%',
  height: '100%',
};

// Check for valid number.
const isNumber = (input: any): boolean => !Number.isNaN(Number(input));

// Add a class to an element.
const addClass = (typeof document !== 'undefined' && 'classList' in document.documentElement)
  ? (element: HTMLElement, className: string): void => {
    element.classList.add(className);
  }
  : (element: HTMLElement, className: string): void => {
    const oldClass = element.getAttribute('class') || '';
    element.setAttribute('class', `${oldClass} ${className}`);
  };

interface HyperListConfig {
  width?: string | number;
  height?: string | number;
  total: number;
  itemHeight: number | number[];
  generate: (index: number) => { element: HTMLElement; height?: number } | HTMLElement;
  scrollerTagName?: string;
  scroller?: HTMLElement;
  scrollContainer?: HTMLElement;
  horizontal?: boolean;
  reverse?: boolean;
  useFragment?: boolean;
  afterRender?: () => void;
  applyPatch?: (element: HTMLElement, fragment: DocumentFragment | HTMLElement[]) => void;
  overrideScrollPosition?: () => number;
  rowClassName?: string;
}

export class HyperList {

  private _config: HyperListConfig = {
    total: 0,
    itemHeight: 0,
    generate: function (index: number): { element: HTMLElement; height?: number; } | HTMLElement {
      throw new Error("Function not implemented.");
    }
  };
  private _lastRepaint: number | null = null;
  private _maxElementHeight: number;
  private _element!: HTMLElement;
  private _scroller!: HTMLElement;
  private _itemHeights!: number[];
  private _itemPositions!: number[];
  private _scrollHeight!: number;
  private _scrollPaddingTop!: number;
  private _scrollPaddingBottom!: number;
  private _containerSize!: number;
  private _screenItemsLen!: number;
  private _cachedItemsLen!: number;
  private _averageHeight!: number;
  private _renderAnimationFrame!: number;
  private _lastFrom: any // Default configuration.
    ;

  static create(element: HTMLElement, userProvidedConfig: HyperListConfig): HyperList {
    return new HyperList(element, userProvidedConfig);
  }

  /**
   * Merge given CSS style on an element
   */
  static mergeStyle(element: HTMLElement, style: Partial<CSSStyleDeclaration>): void {
    for (const i in style) {
      if (style[i] && element.style[i as any] !== style[i]) {
        element.style[i as any] = style[i]!;
      }
    }
  }

  static getMaxBrowserHeight(): number {
    const wrapper = document.createElement('div');
    const fixture = document.createElement('div');

    HyperList.mergeStyle(wrapper, { position: 'absolute', height: '1px', opacity: '0' });
    HyperList.mergeStyle(fixture, { height: '1e7px' });

    wrapper.appendChild(fixture);
    document.body.appendChild(wrapper);

    const maxElementHeight = fixture.offsetHeight;
    document.body.removeChild(wrapper);

    return maxElementHeight;
  }

  constructor(element: HTMLElement, userProvidedConfig: HyperListConfig) {
    this._maxElementHeight = HyperList.getMaxBrowserHeight();
    this.refresh(element, userProvidedConfig);

    const render = () => {
      const scrollTop = this._getScrollPosition();
      const lastRepaint = this._lastRepaint;

      this._renderAnimationFrame = window.requestAnimationFrame(render);

      if (scrollTop === lastRepaint) {
        return;
      }

      const diff = lastRepaint ? scrollTop - lastRepaint : 0;
      if (!lastRepaint || diff < 0 || diff > this._averageHeight) {
        const rendered = this._renderChunk();

        this._lastRepaint = scrollTop;

        if (rendered !== false && typeof this._config.afterRender === 'function') {
          this._config.afterRender();
        }
      }
    };

    render();
  }

  destroy(): void {
    window.cancelAnimationFrame(this._renderAnimationFrame);
  }

  refresh(element: HTMLElement, userProvidedConfig: HyperListConfig): void {
    Object.assign(this._config, defaultConfig, userProvidedConfig);

    if (!element || element.nodeType !== 1) {
      throw new Error('HyperList requires a valid DOM Node container');
    }

    this._element = element;

    const config = this._config;

    const scroller = this._scroller || config.scroller || document.createElement(config.scrollerTagName || 'tr');

    if (typeof config.useFragment !== 'boolean') {
      this._config.useFragment = true;
    }

    if (!config.generate) {
      throw new Error('Missing required `generate` function');
    }

    if (!isNumber(config.total)) {
      throw new Error('Invalid required `total` value, expected number');
    }

    if (!Array.isArray(config.itemHeight) && !isNumber(config.itemHeight)) {
      throw new Error('Invalid required `itemHeight` value, expected number or array');
    } else if (isNumber(config.itemHeight)) {
      this._itemHeights = Array(config.total).fill(config.itemHeight as number);
    } else {
      this._itemHeights = config.itemHeight as number[];
    }

    Object.keys(defaultConfig)
      .filter((prop) => prop in config)
      .forEach((prop) => {
        const value = config[prop as keyof HyperListConfig];
        const isValueNumber = isNumber(value);

        if (value && typeof value !== 'string' && typeof value !== 'number') {
          throw new Error(`Invalid optional \`${prop}\`, expected string or number`);
        } else if (isValueNumber) {
          (config as Record<string, any>)[prop] = `${value}px`;
        }
      });

    const isHoriz = Boolean(config.horizontal);
    const value = config[isHoriz ? 'width' : 'height'];

    if (value) {
      const isValueNumber = isNumber(value);
      const isValuePercent = isValueNumber ? false : (value as string).slice(-1) === '%';
      const numberValue = isValueNumber ? value : parseInt((value as string).replace(/px|%/, ''), 10);
      const innerSize = window[isHoriz ? 'innerWidth' : 'innerHeight'];

      if (isValuePercent) {
        this._containerSize = (innerSize * (numberValue as number)) / 100;
      } else {
        this._containerSize = isNumber(value) ? Number(value) : Number(numberValue);
      }
    }

    const scrollContainer = config.scrollContainer;
    const scrollerHeight = (config.itemHeight as number) * config.total;
    const maxElementHeight = this._maxElementHeight;

    if (scrollerHeight > maxElementHeight) {
      console.warn(
        `HyperList: The maximum element height ${maxElementHeight}px has been exceeded; please reduce your item height.`
      );
    }

    const elementStyle: Partial<CSSStyleDeclaration> = {
      width: `${config.width}`,
      height: scrollContainer ? `${scrollerHeight}px` : `${config.height}`,
      overflow: scrollContainer ? 'none' : 'auto',
      position: 'relative',
    };

    HyperList.mergeStyle(element, elementStyle);

    if (scrollContainer) {
      HyperList.mergeStyle(config.scrollContainer!, { overflow: 'auto' });
    }

    const scrollerStyle: Partial<CSSStyleDeclaration> = {
      opacity: '0',
      position: 'absolute',
      [isHoriz ? 'height' : 'width']: '1px',
      [isHoriz ? 'width' : 'height']: `${scrollerHeight}px`,
    };

    HyperList.mergeStyle(scroller, scrollerStyle);

    if (!this._scroller) {
      element.appendChild(scroller);
    }

    const padding = this._computeScrollPadding();
    this._scrollPaddingBottom = padding.bottom;
    this._scrollPaddingTop = padding.top;

    this._scroller = scroller;
    this._scrollHeight = this._computeScrollHeight();

    this._itemPositions = this._itemPositions || Array(config.total).fill(0);

    this._computePositions(0);

    this._renderChunk(this._lastRepaint !== null);

    if (typeof config.afterRender === 'function') {
      config.afterRender();
    }
  }

  private _getScrollPosition(): number {
    const config = this._config;

    if (typeof config.overrideScrollPosition === 'function') {
      return config.overrideScrollPosition();
    }

    return this._element[config.horizontal ? 'scrollLeft' : 'scrollTop'];
  }

  private _renderChunk(force?: boolean): boolean | void {
    const config = this._config;
    const element = this._element;
    const scrollTop = this._getScrollPosition();
    const total = config.total;

    let from = config.reverse ? this._getReverseFrom(scrollTop) : this._getFrom(scrollTop) - 1;

    if (from < 0 || from - this._screenItemsLen < 0) {
      from = 0;
    }

    if (!force && this._lastFrom === from) {
      return false;
    }

    this._lastFrom = from;

    let to = from + this._cachedItemsLen;

    if (to > total || to + this._cachedItemsLen > total) {
      to = total;
    }

    const fragment = config.useFragment ? document.createDocumentFragment() : [];

    const scroller = this._scroller;

    if (config.useFragment) {
      (fragment as DocumentFragment).appendChild(scroller);
    } else {
      (fragment as HTMLElement[]).push(scroller);
    }

    for (let i = from; i < to; i++) {
      const row = this._getRow(i);

      if (config.useFragment) {
        (fragment as DocumentFragment).appendChild(row);
      } else {
        (fragment as HTMLElement[]).push(row);
      }
    }

    if (config.applyPatch) {
      return config.applyPatch(element, fragment as DocumentFragment | HTMLElement[]);
    }

    element.innerHTML = '';
    element.appendChild(fragment as DocumentFragment);
  }

  private _getRow(i: number): HTMLElement {
    const config = this._config;
    let item = config.generate(i);
    let height = (item as { height?: number }).height;

    if (height !== undefined && isNumber(height)) {
      item = (item as { element: HTMLElement }).element;

      if (height !== this._itemHeights[i]) {
        this._itemHeights[i] = height;
        this._computePositions(i);
        this._scrollHeight = this._computeScrollHeight();
      }
    } else {
      height = this._itemHeights[i];
    }

    if (!item || (item as HTMLElement).nodeType !== 1) {
      throw new Error(`Generator did not return a DOM Node for index: ${i}`);
    }

    addClass(item as HTMLElement, config.rowClassName || 'vrow');

    const top = this._itemPositions[i] + this._scrollPaddingTop;

    HyperList.mergeStyle(item as HTMLElement, {
      position: 'absolute',
      [config.horizontal ? 'left' : 'top']: `${top}px`,
    });

    return item as HTMLElement;
  }

  private _computeScrollHeight(): number {
    const config = this._config;
    const isHoriz = Boolean(config.horizontal);
    const total = config.total;
    const scrollHeight =
      this._itemHeights.reduce((a, b) => a + b, 0) + this._scrollPaddingBottom + this._scrollPaddingTop;

    HyperList.mergeStyle(this._scroller, {
      opacity: '0',
      position: 'absolute',
      top: '0px',
      [isHoriz ? 'height' : 'width']: '1px',
      [isHoriz ? 'width' : 'height']: `${scrollHeight}px`,
    });

    const sortedItemHeights = this._itemHeights.slice(0).sort((a, b) => a - b);
    const middle = Math.floor(total / 2);
    const averageHeight =
      total % 2 === 0
        ? (sortedItemHeights[middle] + sortedItemHeights[middle - 1]) / 2
        : sortedItemHeights[middle];

    const clientProp = isHoriz ? 'clientWidth' : 'clientHeight';
    const element = config.scrollContainer ? config.scrollContainer : this._element;
    const containerHeight = element[clientProp] ? element[clientProp] : this._containerSize;
    this._screenItemsLen = Math.ceil(containerHeight / averageHeight);
    this._containerSize = containerHeight;

    this._cachedItemsLen = Math.max(this._cachedItemsLen || 0, this._screenItemsLen * 3);
    this._averageHeight = averageHeight;

    if (config.reverse) {
      window.requestAnimationFrame(() => {
        if (isHoriz) {
          this._element.scrollLeft = scrollHeight;
        } else {
          this._element.scrollTop = scrollHeight;
        }
      });
    }

    return scrollHeight;
  }

  private _computeScrollPadding(): { top: number; bottom: number } {
    const config = this._config;
    const isHoriz = Boolean(config.horizontal);
    const isReverse = config.reverse;
    const styles = window.getComputedStyle(this._element);

    const padding = (location: string): number => {
      const cssValue = styles.getPropertyValue(`padding-${location}`);
      return parseInt(cssValue, 10) || 0;
    };

    if (isHoriz && isReverse) {
      return {
        bottom: padding('left'),
        top: padding('right'),
      };
    } else if (isHoriz) {
      return {
        bottom: padding('right'),
        top: padding('left'),
      };
    } else if (isReverse) {
      return {
        bottom: padding('top'),
        top: padding('bottom'),
      };
    } else {
      return {
        bottom: padding('bottom'),
        top: padding('top'),
      };
    }
  }

  private _computePositions(from = 1): void {
    const config = this._config;
    const total = config.total;
    const reverse = config.reverse;

    if (from < 1 && !reverse) {
      from = 1;
    }

    for (let i = from; i < total; i++) {
      if (reverse) {
        if (i === 0) {
          this._itemPositions[0] = this._scrollHeight - this._itemHeights[0];
        } else {
          this._itemPositions[i] = this._itemPositions[i - 1] - this._itemHeights[i];
        }
      } else {
        this._itemPositions[i] = this._itemHeights[i - 1] + this._itemPositions[i - 1];
      }
    }
  }

  private _getFrom(scrollTop: number): number {
    let i = 0;

    while (this._itemPositions[i] < scrollTop) {
      i++;
    }

    return i;
  }

  private _getReverseFrom(scrollTop: number): number {
    let i = this._config.total - 1;

    while (i > 0 && this._itemPositions[i] < scrollTop + this._containerSize) {
      i--;
    }

    return i;
  }

  /**
 * Scroll đến vị trí index mong muốn
 */
  public scrollToIndex(index: number): void {
    if (!this._element) return;
    let scrollTop = 0;
    if (Array.isArray(this._itemHeights)) {
      for (let i = 0; i < index; i++) {
        scrollTop += this._itemHeights[i];
      }
    } else {
      scrollTop = index * (this._config.itemHeight as number);
    }
    this._element.scrollTop = scrollTop;
  }
}