//Tạo UUID ngẫu nhiên
export const createUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Hàm theo dõi sự thay đổi kích thước của một phần tử
export const watchElement = (
    element: HTMLElement | null,
    callback: (info: { element: HTMLElement; width: number; height: number }) => void
): void => {
    if (!element) {
        console.warn("Element không hợp lệ!");
        return;
    }

    // Tạo ResizeObserver
    const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
            const { width, height } = entry.contentRect;
            callback({ element, width, height });
        });
    });

    // Bắt đầu theo dõi
    resizeObserver.observe(element);
};