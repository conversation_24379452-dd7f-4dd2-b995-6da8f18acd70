import { DistanceMeasurement, DistanceMeasurementsMouseControl, DistanceMeasurementsPlugin, PointerLens, AngleMeasurementsMouseControl, AngleMeasurementsPlugin } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";
import { createUUID } from "../../../utils/utils";
import { AngleMeasurement } from "@xeokit/xeokit-sdk/types/plugins/AngleMeasurementsPlugin/AngleMeasurement";

enum EClassMeasure {
    distance = "point-len-distance",
    angle = "point-len-angle",
}


export class DevMeasure {
    private _store = GlobalStore.getInstance().getAll();
    private _idGeneral = createUUID();
    distanceMeasurementsPlugin = new DistanceMeasurementsPlugin(this._store.viewer!, {
        container: this._store.containerCanvas as HTMLElement,
        zIndex: 500

    });

    angleDistanceMeasurementsPlugin = new AngleMeasurementsPlugin(this._store.viewer!, {
        container: this._store.containerCanvas as HTMLElement,
        zIndex: 500,
    })

    distanceMeasurementsMouseControl: DistanceMeasurementsMouseControl | undefined; // Để sử dụng sau này
    distanceMeasurementsAngleMouseControl: AngleMeasurementsMouseControl | undefined; // Để sử dụng sau này
    private _isActiveMeasure: boolean = false;

    get isActiveMeasure(): boolean {
        return this._isActiveMeasure;
    }
    set isActiveMeasure(value: boolean) {
        this._isActiveMeasure = value;
    }

    constructor() {
        this.initialize()
        GlobalStore.getInstance().set("devGeneral", {
            measure: this
        });
    }
    //#region initialize
    initialize = () => {
        if (this._store.viewer && this._store.viewer.cameraControl) {
            //Less snap to edge
            this._store.viewer.cameraControl.snapRadius = 30;
        }

        this.distanceMeasurementsPlugin.on("mouseOver", (e) => {
            if (this._isRemovingMeasure) {
                this._hightlighedMeasureSelected = e.measurement;

                e.measurement.setHighlighted(true);

                
            }
            console.log("mouseOver",e);
        });
        this.distanceMeasurementsPlugin.on("mouseLeave", (e) => {
            if (this._isRemovingMeasure) {
                e.measurement.setHighlighted(false);
                this._hightlighedMeasureSelected = undefined;
            }
        });

        this.angleDistanceMeasurementsPlugin.on("mouseOver", (e) => {
            if (this._isRemovingMeasure) {
                this._hightlighedAngleMeasureSelected = e.measurement;
                e.measurement.highlighted = true;
            }
        });


        this.angleDistanceMeasurementsPlugin.on("mouseLeave", (e) => {
            if (this._isRemovingMeasure) {
                e.measurement.highlighted = false;
                this._hightlighedAngleMeasureSelected = undefined;
            }
        });


        if (!this.distanceMeasurementsMouseControl) {
            this.distanceMeasurementsMouseControl = new DistanceMeasurementsMouseControl(this.distanceMeasurementsPlugin, {
                snapping: true, // Default
                pointerLens: new PointerLens(this._store.viewer!, {
                    containerId: `${EClassMeasure.distance}-${this._idGeneral}`,
                }),
               
            })

            const containerViewCanvas = this._store.containerCanvas as HTMLElement;
            const containerPointerLens = document.getElementById(`${EClassMeasure.distance}-${this._idGeneral}`) as HTMLElement;
            containerPointerLens.style.bottom = "50px";
            containerViewCanvas.appendChild(containerPointerLens);
        }

        if (!this.distanceMeasurementsAngleMouseControl) {
            this.distanceMeasurementsAngleMouseControl = new AngleMeasurementsMouseControl(this.angleDistanceMeasurementsPlugin, {
                snapping: true, // Default  
                pointerLens: new PointerLens(this._store.viewer!, {
                    containerId: `${EClassMeasure.angle}-${this._idGeneral}`,
                })
            })

            const containerViewCanvas = this._store.containerCanvas as HTMLElement;
            const containerPointerLens = document.getElementById(`${EClassMeasure.angle}-${this._idGeneral}`) as HTMLElement;
            containerPointerLens.style.bottom = "50px";
            containerViewCanvas.appendChild(containerPointerLens);
        }
        this.distanceMeasurementsMouseControl.snapping = true; // Bật snapping
        this.distanceMeasurementsMouseControl?.deactivate(); 

        this.distanceMeasurementsAngleMouseControl.snapping = true; // Bật snapping
        this.distanceMeasurementsAngleMouseControl?.deactivate();


        //Activate tool Measure
        this._isActiveMeasure = true;



    }

    //#region _onClickRemove
    private _onClickRemove = (e: MouseEvent) => {
        e.preventDefault();
        if (this._hightlighedMeasureSelected) {
            this.distanceMeasurementsPlugin.destroyMeasurement(this._hightlighedMeasureSelected.id);
            this._hightlighedMeasureSelected = undefined;
        }

        if (this._hightlighedAngleMeasureSelected) {
            this.angleDistanceMeasurementsPlugin.destroyMeasurement(this._hightlighedAngleMeasureSelected.id);
            this._hightlighedAngleMeasureSelected = undefined;
        }


    };


    private _hightlighedMeasureSelected: DistanceMeasurement | undefined = undefined;
    private _hightlighedAngleMeasureSelected: AngleMeasurement | undefined = undefined;

    private _isRemovingMeasure = false;

    //#region actionStartMeasure
    actionStartMeasure = () => {
        if (this._store.viewer && this._store.viewer.cameraControl) {
            //Less snap to edge
            this._store.viewer.cameraControl.snapRadius = 30;
        }
        this.distanceMeasurementsMouseControl?.activate();
        this._isRemovingMeasure = false;

        this.distanceMeasurementsAngleMouseControl?.deactivate();

        this.destroyEventRemoveMeasurement();
        this._handleCancelMeasure();
    }

    actionStartMeasureAngle = () => {
        if (this._store.viewer && this._store.viewer.cameraControl) {
            //Less snap to edge
            this._store.viewer.cameraControl.snapRadius = 30;
        }
        this.distanceMeasurementsAngleMouseControl?.activate();
        this._isRemovingMeasure = false;

        this.distanceMeasurementsMouseControl?.deactivate();

        this.destroyEventRemoveMeasurement();
        this._handleCancelMeasure();
    }
    //#region _onEscKeyDown
    private _onEscKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
            this.distanceMeasurementsMouseControl?.reset();
            this.distanceMeasurementsAngleMouseControl?.reset();
        }
    };

    //#region _onRightClick
    private _onRightClick = (e: Event) => {
        e.preventDefault();
        this.distanceMeasurementsMouseControl?.reset();
        this.distanceMeasurementsAngleMouseControl?.reset();
    };

    //#region _handleCancelMeasure
    private _handleCancelMeasure = () => {
        window.addEventListener("keydown", this._onEscKeyDown);
        this._store.containerCanvas?.addEventListener("contextmenu", this._onRightClick as EventListener);
    };
    //#region _removeCancelMeasureListeners
    private _removeCancelMeasureListeners = () => {
        window.removeEventListener("keydown", this._onEscKeyDown);
        this._store.containerCanvas?.removeEventListener("contextmenu", this._onRightClick);
    };

    //#region actionRemoveMeasurement
    actionRemoveMeasurement = () => {
        this.distanceMeasurementsMouseControl?.deactivate();
        this.distanceMeasurementsMouseControl?.reset();
        this.distanceMeasurementsAngleMouseControl?.deactivate();
        this.distanceMeasurementsAngleMouseControl?.reset();
        this._isRemovingMeasure = true;
        this._store.containerCanvas?.addEventListener("click", this._onClickRemove as EventListener);

        this._removeCancelMeasureListeners();
    }

    //#region actionShowHideAllMeasurements
    actionShowHideAllMeasurements = (show: boolean) => {
        const measurements = this.distanceMeasurementsPlugin.measurements;
        const angleMeasurements = this.angleDistanceMeasurementsPlugin.measurements;
        this.distanceMeasurementsMouseControl?.deactivate();

        this.distanceMeasurementsMouseControl?.reset();

        this.distanceMeasurementsAngleMouseControl?.deactivate();
        this.distanceMeasurementsAngleMouseControl?.reset();

        for (const measurementId in measurements) {
            const measurement = measurements[measurementId];
            if (measurement) {
                measurement.visible = show; //Ẩn/Hiển thị tất cả các đo lường
            }
        }
        for (const angleMeasurementId in angleMeasurements) {
            const angleMeasurement = angleMeasurements[angleMeasurementId];
            if (angleMeasurement) {
                angleMeasurement.visible = show; //Ẩn/Hiển thị tất cả các đo lường góc
            }
        }

        this.destroyEventRemoveMeasurement();
        this._removeCancelMeasureListeners();

    }

    //#region actionRemoveAllMeasurements
    actionRemoveAllMeasurements = () => {
        const measurements = this.distanceMeasurementsPlugin.measurements;
        const angleMeasurements = this.angleDistanceMeasurementsPlugin.measurements;


        this.distanceMeasurementsMouseControl?.deactivate();
        this.distanceMeasurementsAngleMouseControl?.deactivate();
        this.distanceMeasurementsMouseControl?.reset();
        this.distanceMeasurementsAngleMouseControl?.reset();
        for (const measurementId in measurements) {
            const measurement = measurements[measurementId];
            if (measurement) {
                this.distanceMeasurementsPlugin.destroyMeasurement(measurement.id); // Xóa tất cả các đo lường
            }
        }

        for (const angleMeasurementId in angleMeasurements) {
            const angleMeasurement = angleMeasurements[angleMeasurementId];
            if (angleMeasurement) {
                this.angleDistanceMeasurementsPlugin.destroyMeasurement(angleMeasurement.id); // Xóa tất cả các đo lường góc
            }
        }
        this._removeCancelMeasureListeners();

    }

    //#region destroyEventRemoveMeasurement
    destroyEventRemoveMeasurement = () => {

        this._store.containerCanvas?.removeEventListener("click", this._onClickRemove! as EventListener);
    }

    //#region refreshAction
    refreshAction = () => {
        this._isRemovingMeasure = false;
        this._hightlighedMeasureSelected = undefined;
        this._hightlighedAngleMeasureSelected = undefined;
        this.distanceMeasurementsMouseControl?.reset();
        this.distanceMeasurementsAngleMouseControl?.reset();
        this.distanceMeasurementsMouseControl?.deactivate();
        this.distanceMeasurementsAngleMouseControl?.deactivate();
        this.destroyEventRemoveMeasurement()
        this._removeCancelMeasureListeners();

        if (this._store.viewer && this._store.viewer.cameraControl) {
            this._store.viewer.cameraControl.snapRadius = 5;
        }
        this._isActiveMeasure = false; // Deactivate measure tool
    }
}