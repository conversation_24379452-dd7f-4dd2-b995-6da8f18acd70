import { IconResize } from "../../utils/icon";

import { createUUID, watchElement } from "../../utils/utils";
export class DialogGeneral {
    IdGenneral = createUUID();//Tạo ID ngẫu nhiên khi khởi tạo
    closeModal: ((status: boolean) => void) | undefined;
    modal: any
    targetContainerId: string;

    isModal: boolean = false//Modal có cho phép truy thao tác ở background dialog

    innerHeader?: string | undefined
    innerBody?: HTMLElement | undefined
    innerFooter?: HTMLElement | undefined
    //Option dialog
    topModal: string = "0";
    leftModal: string = "0";
    rightModal: string = "auto";
    bottomModal: string = "auto";
    heightModal: string = "auto";
    minHeightModal: number = 200;
    widthModal: string = "auto";
    minWidthModal: number = 200;
    firstLoad: boolean = true

    zIndex: number = 100

    minimizeDialog: boolean = false;//<PERSON> phép thu nhỏ hoặc hủy Dialog
    enableResize: boolean = true;//Cho phép resize
    allowDragging: boolean = true;//Cho phép dragging Dialog
    fixedPosition: boolean = false//Giữ nguyên vị trí khi resize

    dialogBody: HTMLElement | null = null
    dialogFooter: HTMLElement | null = null
    constructor(targetContainerId: string, callback: (status: boolean) => void) {

        this.closeModal = callback; // Lưu callback
        this.targetContainerId = targetContainerId
    }
    private containerId: HTMLElement | undefined
    createDialog = () => {
        this.containerId = document.getElementById(this.targetContainerId) as HTMLElement;
        if (!this.containerId) {
            console.error(`Container with ID "${this.containerId}" not found`);
            return;
        }


        this.modal = document.createElement('div');
        this.modal.className = 'dpu-dialog-popup-setting' + (this.isModal ? "" : " not-modal");
        this.modal.id = `my-modal-${this.IdGenneral}`;
        this.modal.style.zIndex = `${this.zIndex}`
        // Tạo nội dung của modal
        const modalContent = `
          <div id="modal-${this.IdGenneral}" class="dpu-dialog-content-popup" 
          style="width:${this.widthModal};
          top:0;left:0; z-index:${this.zIndex}">
            <div id="modal-header-${this.IdGenneral}" class="dpu-dialog-header ${this.allowDragging ? "with-cursor" : ""}" >
                <div style="font-weight:600;font-size:16px">${this.innerHeader || ''}</div>
                <div id="close-dialog-${this.IdGenneral}" class="dpu-dialog-close">𐌗</div>
            </div>
            <div id="dialog-body-${this.IdGenneral}" class="dpu-dialog-body"></div>
            <div class="dpu-dialog-footer"></div>
            <div id="dialog-resize-${this.IdGenneral}" class="${!this.enableResize ? "" : "dpu-dialog-resize"}">${this.enableResize ? IconResize() : ""}</div>
          </div>
        `;

        // Gán HTML cơ bản vào modal
        this.modal.innerHTML = modalContent;

        // Thêm phần tử modal vào container viewer
        this.containerId.appendChild(this.modal);
        const dialogContent = this.modal.querySelector(`#modal-${this.IdGenneral}`) as HTMLElement
        if (this.topModal !== "0") {
            dialogContent.style.top = this.topModal
        }
        if (this.leftModal !== "0") {
            dialogContent.style.left = this.leftModal
        }
        if (this.rightModal !== "auto" && this.rightModal.endsWith('px')) {
            dialogContent.style.removeProperty('left')
            dialogContent.style.right = this.rightModal
        }
        if (this.bottomModal !== "auto" && this.bottomModal.endsWith('px')) {
            dialogContent.style.removeProperty('top')
            dialogContent.style.bottom = this.bottomModal
        }

        // Nếu có nội dung cho innerBody, append nó vào phần thân modal
        this.dialogBody = this.modal.querySelector('.dpu-dialog-body');
        if (this.dialogBody && this.innerBody) {
            this.dialogBody.appendChild(this.innerBody); // Gán phần tử DOM thực tế vào body của modal
        }
        if (this.heightModal !== "auto") {
            const headerDiv = this.containerId!.querySelector(`#modal-${this.IdGenneral}`) as HTMLElement
            if (headerDiv) {
                headerDiv.style.height = this.heightModal
            }
        }


        // Nếu có nội dung cho innerFooter, append nó vào phần footer modal
        // this.dialogFooter = this.modal.querySelector('.dialog-footer');
        // if (this.dialogFooter && this.innerFooter) {
        //     this.dialogFooter.appendChild(this.innerFooter); // Gán phần tử DOM thực tế vào body của modal            
        //     const totalHeight = 55 + this.innerFooter.offsetHeight
        //     if (this.dialogBody) {
        //         this.dialogBody.style.height = `calc(100% - ${totalHeight}px)`;
        //     }

        // }
        this.createFooter()

        /**
* Hàm tính khoảng cách từ điểm phần trăm (top, left) tới top và left của container
* @param {number} containerWidth - Chiều rộng của container
* @param {number} containerHeight - Chiều cao của container
* @param {number} elementWidth - Chiều rộng của element
* @param {number} elementHeight - Chiều cao của element
* @param {number} topPercent - Phần trăm vị trí top (0-100)
* @param {number} leftPercent - Phần trăm vị trí left (0-100)
* @returns {Object} - Khoảng cách {top, left}
*/
        const calculateOffsetFromPercent = (containerWidth: number, containerHeight: number, elementWidth: number, elementHeight: number, topPercent: number, leftPercent: number) => {
            // Quy đổi % thành pixel dựa trên kích thước container
            const topOffset = (containerHeight * (topPercent / 100)) - (elementHeight / 2);
            const leftOffset = (containerWidth * (leftPercent / 100)) - (elementWidth / 2);

            // Trả về khoảng cách tới top và left
            return { top: topOffset, left: leftOffset };
        }
        if (this.topModal.endsWith('%')
            || this.leftModal.endsWith('%')
            || this.bottomModal.endsWith('%')
            || this.rightModal.endsWith('%')) {
            const containerDialog = this.modal.querySelector(`#modal-${this.IdGenneral}`) as HTMLElement
            this.modal.style.opacity = '0'
            this.modal.style.display = "block"



            const parentHeight = this.containerId!.offsetHeight
            const parentWidth = this.containerId!.offsetWidth


            const heightModal = containerDialog.offsetHeight
            const widthModal = containerDialog.offsetWidth


            if (this.firstLoad) {
                this.modal.style.display = "none"
            }
            this.modal.style.opacity = "unset"

            let offsetWidth = 0
            let offsetHeight = 0
            // Quy đổi các giá trị top, left, bottom, right từ phần trăm sang px
            if (this.topModal.endsWith('%')) {
                offsetHeight = parseFloat(this.topModal)
            }
            if (this.leftModal.endsWith('%')) {
                offsetWidth = parseFloat(this.leftModal)
            }

            if (this.bottomModal.endsWith('%')) {
                offsetHeight = 100 - parseFloat(this.bottomModal)
            }
            if (this.rightModal.endsWith('%')) {
                offsetWidth = 100 - parseFloat(this.rightModal)
            }


            const postionModal = calculateOffsetFromPercent(parentWidth, parentHeight, widthModal, heightModal, offsetWidth, offsetHeight)
            containerDialog.style.top = `${postionModal.top}px`
            containerDialog.style.left = `${postionModal.left}px`
        }






        // Xử lý đóng modal
        const btnCloseModal = this.modal.querySelector(`#close-dialog-${this.IdGenneral}`);
        if (btnCloseModal) {
            btnCloseModal.addEventListener('click', () => { this.handleCloseModal(false); });
            btnCloseModal.addEventListener('touchstart', () => { this.handleCloseModal(false); }, { passive: true });
        }


        // Bắt sự kiện kéo và thay đổi kích thước
        this.dragElement(document.querySelector(`#modal-${this.IdGenneral}`));
        this.resizeModal();
        if (this.enableResize) {
            watchElement(this.containerId, ({ width, height }) => {
                this._checkSizeDialog()

            })
            if (this.fixedPosition)
                window.addEventListener('resize', () => {
                    this._checkSizeDialog()
                })

        }
    };
    /**
     * Tạo footer cho modal
     */
    private createFooter = () => {
        // Nếu có nội dung cho innerFooter, append nó vào phần footer modal
        this.dialogFooter = this.modal.querySelector('.dpu-dialog-footer');
        if (this.dialogFooter && this.innerFooter) {
            this.dialogFooter.appendChild(this.innerFooter); // Gán phần tử DOM thực tế vào body của modal            
            const totalHeight = 55 + this.innerFooter.offsetHeight
            if (this.dialogBody) {
                this.dialogBody.style.height = `calc(100% - ${totalHeight}px)`;
            }

        }

    }
    /**
     * Tính toán chiều cao của footer và cập nhật chiều cao của dialog body
     */
    calFooterHeight = () => {
        if (this.dialogFooter && this.innerFooter) {
            const totalHeight = 55 + this.innerFooter.offsetHeight
            if (this.dialogBody) {
                this.dialogBody.style.height = `calc(100% - ${totalHeight}px)`;
            }

        }
    }
    //#region handleCloseModal
    /**
      * Gọi callback khi modal đóng
      */
    private handleCloseModal = (status: boolean) => {
        if (this.closeModal) {
            this.closeModal(status); // Gọi callback

            if (!this.minimizeDialog) {
                // Nếu minimizeDialog = false, xóa khỏi DOM
                if (this.modal && this.modal.parentNode) {
                    this.modal.parentNode.removeChild(this.modal);
                    this.modal = null; // Loại bỏ tham chiếu trong mã
                }
            } else {
                // Nếu minimizeDialog = true, chỉ ẩn modal
                if (this.modal) {
                    this.modal.style.display = 'none'; // Ẩn bằng display: none
                }
            }
        } else {
            console.error("Callback closeModal is undefined");
        }
    };

    showModal = (show: boolean) => {

        if (this.modal) {
            if (show) {
                // Hiển thị modal bằng cách thay đổi style
                this.modal.style.display = 'block';
                this._checkSizeDialog()
            } else {
                // Hiển thị modal bằng cách thay đổi style
                this.modal.style.display = 'none';
            }

        } else {
            console.error("Modal is not created yet. Call createDialog() first.");
        }

    };

    //#region dragElement
    private dragElement = (element: any) => {
        if (this.allowDragging) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

            const elementHeader = document.getElementById(`modal-header-${this.IdGenneral}`);

            // Hàm khởi tạo sự kiện bắt đầu kéo
            const initializeDrag = (startX: number, startY: number, onDrag: any, onStop: any) => {
                pos3 = startX;
                pos4 = startY;

                document.addEventListener('mousemove', onDrag, { passive: false });
                document.addEventListener('mouseup', onStop);

                document.addEventListener('touchmove', onDrag, { passive: false });
                document.addEventListener('touchend', onStop);
            };

            // Hàm xử lý khi kéo
            const handleDrag = (deltaX: number, deltaY: number) => {
                pos1 = pos3 - deltaX;
                pos2 = pos4 - deltaY;
                pos3 = deltaX;
                pos4 = deltaY;

                const newTop = element.offsetTop - pos2;
                const newLeft = element.offsetLeft - pos1;

                const elementRect = element.getBoundingClientRect();
                const parentRect = this.containerId!.getBoundingClientRect();

                const maxLeft = parentRect.width - elementRect.width;
                const maxTop = parentRect.height - elementRect.height;


                element.style.top = Math.max(0, Math.min(newTop, maxTop)) + "px";
                element.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + "px";

                // Kiểm tra và xóa `right` nếu tồn tại
                if (element.style.right) {
                    element.style.removeProperty('right');
                }

                // Kiểm tra và xóa `bottom` nếu tồn tại
                if (element.style.bottom) {
                    element.style.removeProperty('bottom');
                }
            };

            const onMouseDrag = (e: any) => {
                e.preventDefault();
                handleDrag(e.clientX, e.clientY);
            };

            const onTouchDrag = (e: any) => {
                handleDrag(e.targetTouches[0].clientX, e.targetTouches[0].clientY);
            };

            const stopDrag = () => {
                document.removeEventListener('mousemove', onMouseDrag);
                document.removeEventListener('mouseup', stopDrag);

                document.removeEventListener('touchmove', onTouchDrag);
                document.removeEventListener('touchend', stopDrag);
            };

            const startMouseDrag = (e: any) => {
                e.preventDefault();
                initializeDrag(e.clientX, e.clientY, onMouseDrag, stopDrag);
            };

            const startTouchDrag = (e: any) => {
                e.preventDefault();
                initializeDrag(e.targetTouches[0].clientX, e.targetTouches[0].clientY, onTouchDrag, stopDrag);
            };

            if (elementHeader) {
                elementHeader.addEventListener('mousedown', startMouseDrag);
                elementHeader.addEventListener('touchstart', startTouchDrag, { passive: false });
            } else {
                element.addEventListener('mousedown', startMouseDrag);
                element.addEventListener('touchstart', startTouchDrag, { passive: false });
            }
        }
    };

    //#region resizeModal
    private resizeModal = () => {
        if (this.enableResize) {
            const resizeHandle = this.modal.querySelector(`#dialog-resize-${this.IdGenneral}`) as HTMLElement
            const resizableElement = this.modal.querySelector(`#modal-${this.IdGenneral}`) as HTMLElement
            let isResizing = false;
            let startWidth: number, startHeight: number, startX: number, startY: number;
            let startLeft: number, startTop: number;

            const initializeResize = (e: any, isTouch: boolean) => {
                isResizing = true;
                startWidth = resizableElement.offsetWidth;
                startHeight = resizableElement.offsetHeight;

                // Kiểm tra và tính toán lại `left` nếu `right` tồn tại
                const startRight = resizableElement.style.right ? parseInt(resizableElement.style.right, 10) : 0;
                if (startRight && startRight > 0) {
                    // Tính toán lại `left` dựa trên `right`
                    resizableElement.style.left = `${this.containerId!.offsetWidth - resizableElement.offsetWidth - startRight}px`;
                    resizableElement.style.removeProperty('right'); // Xóa `right`
                }

                // Kiểm tra và tính toán lại `top` nếu `bottom` tồn tại
                const startBottom = resizableElement.style.bottom ? parseInt(resizableElement.style.bottom, 10) : 0;
                if (startBottom && startBottom > 0) {
                    // Tính toán lại `top` dựa trên `bottom`
                    resizableElement.style.top = `${this.containerId!.offsetHeight - resizableElement.offsetHeight - startBottom}px`;
                    resizableElement.style.removeProperty('bottom'); // Xóa `bottom`
                }

                if (isTouch) {
                    startX = e.targetTouches[0].clientX;
                    startY = e.targetTouches[0].clientY;
                } else {
                    startX = e.clientX;
                    startY = e.clientY;
                }

                startLeft = resizableElement.style.left ? parseInt(resizableElement.style.left, 10) : 0;
                startTop = resizableElement.style.top ? parseInt(resizableElement.style.top, 10) : 0;

                document.addEventListener('mousemove', doResize, { passive: false });
                document.addEventListener('mouseup', stopResize);

                document.addEventListener('touchmove', doResize, { passive: false });
                document.addEventListener('touchend', stopResize);
            };

            const calculateResize = (deltaX: number, deltaY: number) => {

                const newWidth = startWidth + deltaX;
                const newHeight = startHeight + deltaY;

                const maxWidth = this.containerId!.offsetWidth - startLeft;
                const maxHeight = this.containerId!.offsetHeight - startTop;

                const updatedWidth = Math.max(Math.min(newWidth, maxWidth), this.minWidthModal);
                const updatedHeight = Math.max(Math.min(newHeight, maxHeight), this.minHeightModal);

                resizableElement.style.width = updatedWidth + 'px';
                resizableElement.style.height = updatedHeight + 'px';

                const updatedLeft = Math.min(startLeft, this.containerId!.offsetWidth - updatedWidth);
                const updatedTop = Math.min(startTop, this.containerId!.offsetHeight - updatedHeight);

                resizableElement.style.left = updatedLeft + 'px';
                resizableElement.style.top = updatedTop + 'px';
            };

            const doResize = (e: any) => {
                if (!isResizing) return;
                const deltaX = e.type === 'mousemove' ? e.clientX - startX : e.targetTouches[0].clientX - startX;
                const deltaY = e.type === 'mousemove' ? e.clientY - startY : e.targetTouches[0].clientY - startY;
                calculateResize(deltaX, deltaY);
            };

            const stopResize = () => {
                isResizing = false;
                document.removeEventListener('mousemove', doResize);
                document.removeEventListener('mouseup', stopResize);

                document.removeEventListener('touchmove', doResize);
                document.removeEventListener('touchend', stopResize);
            };

            resizeHandle.addEventListener('mousedown', (e: any) => initializeResize(e, false));
            resizeHandle.addEventListener('touchstart', (e: any) => {
                e.preventDefault();
                initializeResize(e, true)
            }, { passive: false });
        }
    };



    //#region destroyDialog
    destroyDialog(): void {
        if (this.modal) {
            // Xóa modal khỏi DOM
            this.modal.remove();


        }
    }

    //#region checkSizeDialog
    //Hàm để check width,height của Dialog
    private _checkSizeDialog = () => {
        const parentHeight = this.containerId!.offsetHeight
        const parentWidth = this.containerId!.offsetWidth


        const containerDialog = this.modal.querySelector(`#modal-${this.IdGenneral}`) as HTMLElement

        const heightModal = containerDialog.offsetHeight
        const widthModal = containerDialog.offsetWidth



        if (parentHeight <= heightModal && parentHeight >= this.minHeightModal) {
            containerDialog.style.top = '0px'
            containerDialog.style.height = `${parentHeight}px`
        }
        if (parentWidth <= widthModal && !this.fixedPosition && parentWidth >= this.minWidthModal) {
            containerDialog.style.left = '0px'
            containerDialog.style.width = `${parentWidth}px`
        }


    }

}